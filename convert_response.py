#!/usr/bin/env python3
"""
Script to convert response.json from string format to dictionary format.
This script reads the response.json file where data is stored as strings
and converts it to proper JSON dictionary format.
"""

import json

def convert_string_to_dict_format(input_file='response.json', output_file='response_converted.json'):
    """
    Convert JSON file with string entries to proper dictionary format.
    
    Args:
        input_file (str): Path to input JSON file with string entries
        output_file (str): Path to output JSON file with dictionary entries
    """
    try:
        # Read the original file
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"Original file loaded. Found {len(data)} entries.")
        
        # Convert each string entry to dictionary
        converted_data = []
        for i, entry in enumerate(data):
            if isinstance(entry, str):
                try:
                    # Parse the JSON string to dictionary
                    parsed_entry = json.loads(entry)
                    converted_data.append(parsed_entry)
                    print(f"Successfully converted entry {i+1}")
                except json.JSONDecodeError as e:
                    print(f"Error parsing entry {i+1}: {e}")
                    # Keep the original string if parsing fails
                    converted_data.append(entry)
            else:
                # If it's already a dictionary, keep it as is
                converted_data.append(entry)
                print(f"Entry {i+1} was already in dictionary format")
        
        # Write the converted data to new file
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(converted_data, f, indent=2, ensure_ascii=False)
        
        print(f"\nConversion completed!")
        print(f"Original file: {input_file}")
        print(f"Converted file: {output_file}")
        print(f"Total entries processed: {len(converted_data)}")
        
        return converted_data
        
    except FileNotFoundError:
        print(f"Error: File '{input_file}' not found.")
        return None
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in file '{input_file}': {e}")
        return None
    except Exception as e:
        print(f"Unexpected error: {e}")
        return None

def preview_conversion(input_file='response.json', num_entries=1):
    """
    Preview the conversion without writing to file.
    
    Args:
        input_file (str): Path to input JSON file
        num_entries (int): Number of entries to preview
    """
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"Preview of conversion for first {num_entries} entry(ies):")
        print("=" * 50)
        
        for i in range(min(num_entries, len(data))):
            entry = data[i]
            print(f"\nEntry {i+1}:")
            print("Before (string):")
            print(f"Type: {type(entry)}")
            if isinstance(entry, str):
                print(f"Length: {len(entry)} characters")
                print(f"First 100 chars: {entry[:100]}...")
                
                try:
                    parsed = json.loads(entry)
                    print("\nAfter (dictionary):")
                    print(f"Type: {type(parsed)}")
                    print(f"Keys: {list(parsed.keys()) if isinstance(parsed, dict) else 'Not a dict'}")
                    if isinstance(parsed, dict):
                        print(f"Number of top-level keys: {len(parsed.keys())}")
                except json.JSONDecodeError as e:
                    print(f"Error parsing: {e}")
            else:
                print("Entry is already in dictionary format")
        
    except Exception as e:
        print(f"Error during preview: {e}")

if __name__ == "__main__":
    print("JSON String to Dictionary Converter")
    print("=" * 40)
    
    # First, show a preview
    print("1. Previewing conversion...")
    preview_conversion()
    
    print("\n" + "=" * 40)
    
    # Ask user if they want to proceed
    response = input("Do you want to proceed with the conversion? (y/n): ").lower().strip()
    
    if response in ['y', 'yes']:
        print("\n2. Converting file...")
        result = convert_string_to_dict_format()
        
        if result:
            print("\n3. Conversion successful!")
            print("You can now use 'response_converted.json' which contains proper dictionary format.")
        else:
            print("\n3. Conversion failed. Please check the error messages above.")
    else:
        print("\nConversion cancelled.")
