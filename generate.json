["{\n  \"$schema\": \"http://json-schema.org/draft-07/schema#\",\n  \"title\": \"Comprehensive Product Information Schema\",\n  \"description\": \"A comprehensive schema that combines all product information including general data, nutritional information, serving sizes, ingredients, allergens, and product claims.\",\n  \"type\": \"object\",\n  \"properties\": {\n    \"generalData\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"gtinOnPack\": { \"type\": \"string\", \"value\": \"7706512558604\" },\n        \"gtin14\": { \"type\": \"string\", \"value\": \"0007706512558604\" },\n        \"upc12\": { \"type\": \"string\", \"value\": \"7706512558604\" },\n        \"brandOwner\": { \"type\": \"string\", \"value\": \"Matiz\" },\n        \"brandName\": { \"type\": \"string\", \"value\": \"Matiz (RUUFE)\" },\n        \"department\": { \"type\": \"string\", \"value\": \"Grocery & Gourmet Food\" },\n        \"superCategory\": { \"type\": \"string\", \"value\": \"Beverages\" },\n        \"category\": { \"type\": \"string\", \"value\": \"Coffee\" },\n        \"subCategory\": { \"type\": \"string\", \"value\": \"Instant Coffee\" },\n        \"segment\": { \"type\": \"string\", \"value\": \"Ground Coffee\" },\n        \"productTitle\": { \"type\": \"string\", \"value\": \"Matiz Colombian Light Coffee (2 Pack) Matiz Cafe Colombiano Suave roasted and ground coffee Matiz Suave\" },\n        \"variant\": { \"type\": \"string\", \"value\": \"Suave Marfil\" },\n        \"netWeight1Value\": { \"type\": \"number\", \"value\": 340 },\n        \"netWeight1UOM\": { \"type\": \"string\", \"value\": \"g\" },\n        \"netWeight2Value\": { \"type\": \"number\", \"value\": 340 },\n        \"netWeight2UOM\": { \"type\": \"string\", \"value\": \"g\" },\n        \"unitsPerPack\": { \"type\": \"integer\", \"value\": 2 },\n        \"unitsPerPackDescriptor\": { \"type\": \"string\", \"value\": \"Pack of 2\" },\n        \"storage\": { \"type\": \"string\", \"value\": \"Room Temperature\" },\n        \"numberOfIngredients\": { \"type\": \"integer\", \"value\": 1 }\n      },\n      \"required\": [\n        \"gtinOnPack\",\n        \"gtin14\",\n        \"upc12\",\n        \"brandOwner\",\n        \"brandName\",\n        \"department\",\n        \"superCategory\",\n        \"category\",\n        \"subCategory\",\n        \"segment\",\n        \"productTitle\",\n        \"variant\",\n        \"netWeight1Value\",\n        \"netWeight1UOM\",\n        \"netWeight2Value\",\n        \"netWeight2UOM\",\n        \"unitsPerPack\",\n        \"unitsPerPackDescriptor\",\n        \"storage\",\n        \"numberOfIngredients\"\n    },\n    \"servingSize\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"servingSize\": { \"type\": \"number\", \"value\": 11.0 },\n        \"servingSizeUnit\": { \"type\": \"string\", \"value\": \"Ounce\" },\n        \"servesPerPack\": { \"type\": \"integer\", \"value\": 2 },\n        \"servingDescription\": { \"type\": \"string\", \"value\": \"2 Pack\" },\n        \"servingSize2\": { \"type\": \"null\" },\n        \"servingSize2Unit\": { \"type\": \"null\" }\n      },\n      \"required\": [\n        \"servingSize\",\n        \"servingSizeUnit\",\n        \"servesPerPack\",\n        \"servingDescription\"\n    },\n    \"nutritionalInformation\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"stated\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"totalAmount\": { \"type\": \"number\", \"value\": null },\n            \"totalAmountUOM\": { \"type\": \"string\", \"value\": null },\n            \"source\": { \"type\": \"string\", \"value\": \"Amazon Images\" },\n            \"macronutrients\": {\n              \"type\": \"array\",\n              \"items\": [\n                {\n                  \"name\": { \"type\": \"string\", \"value\": \"Protein\" },\n                  \"amount\": { \"type\": \"number\", \"value\": null },\n                  \"totalAmount\": { \"type\": \"number\", \"value\": null },\n                  \"UOM\": { \"type\": \"string\", \"value\": null },\n                  \"dailyValue\": { \"type\": \"number\", \"value\": null },\n                  \"source\": { \"type\": \"string\", \"value\": \"Amazon Images\" }\n                },\n                {\n                  \"name\": { \"type\": \"string\", \"value\": \"Carbohydrates\" },\n                  \"amount\": { \"type\": \"number\", \"value\": null },\n                  \"totalAmount\": { \"type\": \"number\", \"value\": null },\n                  \"UOM\": { \"type\": \"string\", \"value\": null },\n                  \"dailyValue\": { \"type\": \"number\", \"value\": null },\n                  \"source\": { \"type\": \"string\", \"value\": \"Amazon Images\" }\n                },\n                {\n                  \"name\": { \"type\": \"string\", \"value\": \"Fat\" },\n                  \"amount\": { \"type\": \"number\", \"value\": null },\n                  \"totalAmount\": { \"type\": \"number\", \"value\": null },\n                  \"UOM\": { \"type\": \"string\", \"value\": null },\n                  \"dailyValue\": { \"type\": \"number\", \"value\": null },\n                  \"source\": { \"type\": \"string\", \"value\": \"Amazon Images\" }\n                },\n                {\n                  \"name\": { \"type\": \"string\", \"value\": \"Fiber\" },\n                  \"amount\": { \"type\": \"number\", \"value\": null },\n                  \"totalAmount\": { \"type\": \"number\", \"value\": null },\n                  \"UOM\": { \"type\": \"string\", \"value\": null },\n                  \"dailyValue\": { \"type\": \"number\", \"value\": null },\n                  \"source\": { \"type\": \"string\", \"value\": \"Amazon Images\" }\n                },\n                {\n                  \"name\": { \"type\": \"string\", \"value\": \"Sugar\" },\n                  \"amount\": { \"type\": \"number\", \"value\": null },\n                  \"totalAmount\": { \"type\": \"number\", \"value\": null },\n                  \"UOM\": { \"type\": \"string\", \"value\": null },\n                  \"dailyValue\": { \"type\": \"number\", \"value\": null },\n                  \"source\": { \"type\": \"string\", \"value\": \"Amazon Images\" }\n                },\n                {\n                  \"name\": { \"type\": \"string\", \"value\": \"Sodium\" },\n                  \"amount\": { \"type\": \"number\", \"value\": null },\n                  \"totalAmount\": { \"type\": \"number\", \"value\": null },\n                  \"UOM\": { \"type\": \"string\", \"value\": null },\n                  \"dailyValue\": { \"type\": \"number\", \"value\": null },\n                  \"source\": { \"type\": \"string\", \"value\": \"Amazon Images\" }\n                }\n              ]\n            },\n            \"micronutrients\": {\n              \"type\": \"array\",\n              \"items\": []\n            }\n          }\n        },\n        \"qualified\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"totalAmount\": { \"type\": \"number\", \"value\": null },\n            \"totalAmountUOM\": { \"type\": \"string\", \"value\": null },\n            \"source\": { \"type\": \"string\", \"value\": \"ScrapingQualificationAI\" },\n            \"macronutrients\": {\n              \"type\": \"array\",\n              \"items\": []\n            },\n            \"micronutrients\": {\n              \"type\": \"array\",\n              \"items\": []\n            }\n          }\n        }\n      }\n    },\n    \"ingredients\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"stated\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"source\": { \"type\": \"string\", \"value\": \"Amazon Images\" },\n            \"ingredientList\": {\n              \"type\": \"array\",\n              \"items\": [\n                {\n                  \"text\": { \"type\": \"string\", \"value\": \"100% Colombian Arabica Coffee\" },\n                  \"amount\": { \"type\": \"number\", \"value\": 500 },\n                  \"amountUOM\": { \"type\": \"string\", \"value\": \"g\" },\n                  \"dietary_preference\": { \"type\": \"object\", \"value\": {} },\n                  \"allergens\": { \"type\": \"object\", \"value\": {} },\n                  \"religious_labels\": { \"type\": \"object\", \"value\": {} },\n                  \"food_safety_labels\": { \"type\": \"object\", \"value\": {} },\n                  \"sustainability_labels\": { \"type\": \"object\", \"value\": {} },\n                  \"subIngredients\": { \"type\": \"array\", \"value\": [] }\n                }\n              ]\n            }\n          }\n        },\n        \"qualified\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"source\": { \"type\": \"string\", \"value\": \"ScrapingQualificationAI\" },\n            \"ingredientList\": {\n              \"type\": \"array\",\n              \"items\": []\n            }\n          }\n        }\n      }\n    },\n    \"claims\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"certifications\": { \"type\": \"array\", \"value\": [\"USDA Organic\"] },\n        \"nutritionalClaims\": { \"type\": \"array\", \"value\": [\"Light Roast Level\"] },\n        \"ingredientClaims\": { \"type\": \"array\", \"value\": [\"Premium Quality\"] },\n        \"preparation\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"storage\": { \"type\": \"string\", \"value\": \"Room Temperature\" },\n            \"heatingInstructions\": { \"type\": \"object\", \"value\": {} }\n          }\n        },\n        \"sustainability\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"packaging\": { \"type\": \"array\", \"value\": [\"Recycled\"] },\n            \"environmentalClaims\": { \"type\": \"array\", \"value\": [\"Sustainably Sourced\"] \n          }\n        },\n        \"contact\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"website\": { \"type\": \"string\", \"value\": \"https://www.matiz.com\" },\n            \"phone\": { \"type\": \"string\", \"value\": \"******-288-1089\" }\n          }\n        }\n      }\n    },\n    \"NPI 2.0 Food Packages - Allergens & Intolerances\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"fdaRegulatedAllergens\": { \"type\": \"string\", \"value\": \"Contains: Milk, Eggs, Fish, Shellfish, Tree Nuts, Peanuts, Soy\" },\n        \"caseinQualified\": { \"type\": \"string\", \"value\": \"No Casein\" },\n        \"caseinStated\": { \"type\": \"string\", \"value\": \"No Casein Added\" },\n        \"coconutQualified\": { \"type\": \"string\", \"value\": \"No Coconut\" },\n        \"coconutStated\": { \"type\": \"string\", \"value\": \"No Coconut Added\" },\n        \"cornQualified\": { \"type\": \"string\", \"value\": \"No Corn\" },\n        \"cornStated\": { \"type\": \"string\", \"value\": \"No Corn Added\" },\n        \"dairyLevelStated\": { \"type\": \"string\", \"value\": \"Lactose-Free\" },\n        \"dairyQualified\": { \"type\": \"string\", \"value\": \"Dairy Free\" },\n        \"dairyStated\": { \"type\": \"string\", \"value\": \"Dairy Free\" },\n        \"eggLevelStated\": { \"type\": \"string\", \"value\": \"Egg-Free\" },\n        \"eggQualified\": { \"type\": \"string\", \"value\": \"No Eggs\" },\n        \"eggStated\": { \"type\": \"string\", \"value\": \"No Eggs Added\" },\n        \"falcpaCommonAllergensQualified\": { \"type\": \"string\", \"value\": \"Major Allergens\" },\n        \"falcpaCommonAllergensStated\": { \"type\": \"string\", \"value\": \"Contains: Milk, Eggs, Fish, Shellfish, Tree Nuts, Peanuts, Soy\" },\n        \"fishLevelStated\": { \"type\": \"string\", \"value\": \"Fish-Free\" },\n        \"fishQualified\": { \"type\": \"string\", \"value\": \"No Fish\" },\n        \"fishStated\": { \"type\": \"string\", \"value\": \"No Fish Added\" },\n        \"glutenLevelStated\": { \"type\": \"string\", \"value\": \"Gluten-Free\" },\n        \"glutenQualified\": { \"type\": \"string\", \"value\": \"Gluten Free\" },\n        \"glutenStated\": { \"type\": \"string\", \"value\": \"Gluten Free\" },\n        \"dairyLevelStated\": { \"type\": \"string\", \"value\": \"Lactose-Free\" },\n        \"dairyQualified\": { \"type\": \"string\", \"value\": \"Dairy Free\" },\n        \"dairyStated\": { \"type\": \"string\", \"value\": \"Dairy Free\" },\n        \"eggLevelStated\": { \"type\": \"string\", \"value\": \"Egg-Free\" },\n        \"eggQualified\": { \"type\": \"string\", \"value\": \"No Eggs\" },\n        \"eggStated\": { \"type\": \"string\", \"value\": \"No Eggs Added\" },\n        \"falcpaCommonAllergensQualified\": { \"type\": \"string\", \"value\": \"Major Allergens\" },\n        \"falcpaCommonAllergensStated\": { \"type\": \"string\", \"value\": \"Contains: Milk, Eggs, Fish, Shellfish, Tree Nuts, Peanuts, Soy\" },\n        \"fishLevelStated\": { \"type\": \"string\", \"value\": \"Fish-Free\" },\n        \"fishQualified\": { \"type\": \"string\", \"value\": \"No Fish\" },\n        \"fishStated\": { \"type\": \"string\", \"value\": \"No Fish Added\" },\n        \"glutenLevelStated\": { \"type\": \"string\", \"value\": \"Gluten-Free\" },\n        \"glutenQualified\": { \"type\": \"string\", \"value\": \"Gluten Free\" },\n        \"dairyLevelStated\": { \"type\": \"string\", \"value\": \"Lactose-Free\" },\n        \"dairyQualified\": { \"type\": \"string\", \"value\": \"Dairy Free\" },\n        \"dairyStated\": { \"type\": \"string\", \"value\": \"Dairy Free\" },\n        \"eggLevelStated\": { \"type\": \"string\", \"value\": \"Egg-Free\" },\n        \"eggQualified\": { \"type\": \"string\", \"value\": \"No Eggs\" },\n        \"eggStated\": { \"type\": \"string\", \"value\": \"No Eggs Added\" },\n        \"falcpaCommonAllergensQualified\": { \"type\": \"string\", \"value\": \"Major Allergens\" },\n        \"falcpaCommonAllergensStated\": { \"type\": \"string\", \"value\": \"Contains: Milk, Eggs, Fish, Shellfish, Tree Nuts, Peanuts, Soy\" },\n        \"fishLevelStated\": { \"type\": \"string\", \"value\": \"Fish-Free\" },\n        \"fishQualified\": { \"type\": \"string\", \"value\": \"No Fish\" },\n        \"fishStated\": { \"type\": \"string\", \"value\": \"No Fish Added\" },\n        \"glutenLevelStated\": { \"type\": \"string\", \"value\": \"Gluten-Free\" },\n        \"glutenQualified\": { \"type\": \"string\", \"value\": \"Gluten Free\" },\n        \"dairyLevelStated\": { \"type\": \"string\", \"value\": \"Lactose-Free\" },\n        \"dairyQualified\": { \"type\": \"string\", \"value\": \"Dairy Free\" },\n        \"dairyStated\": { \"type\": \"string\", \"value\": \"Dairy Free\" },\n        \"eggLevelStated\": { \"type\": \"string\", \"value\": \"Egg-Free\" },\n        \"eggQualified\": { \"type\": \"string\", \"value\": \"No Eggs\" },\n        \"eggStated\": { \"type\": \"string\", \"value\": \"No Eggs Added\" },\n        \"cleanLabel\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"artisanalStated\": { \"type\": \"string\", \"value\": \"Handcrafted\" },\n            \"coldPressedIngredientsStated\": { \"type\": \"string\", \"value\": \"Cold Pressed\" },\n            \"craftStated\": { \"type\": \"string\", \"value\": \"Craft Brewed\" },\n            \"gourmetStated\": { \"type\": \"string\", \"value\": \"Gourmet Quality\" },\n            \"localStated\": { \"type\": \"string\", \"value\": \"Locally Sourced\" },\n            \"madeInUsaStated\": { \"type\": \"string\", \"value\": \"Made in USA\" },\n            \"animalByProductQualified\": { \"type\": \"string\", \"value\": \"No Animal By-Products\" },\n            \"antibioticsQualified\": { \"type\": \"string\", \"value\": \"No Antibiotics Ever\" },\n            \"artificialColorsQualified\": { \"type\": \"string\", \"value\": \"No Artificial Colors\" },\n            \"artificialFlavorsQualified\": { \"type\": \"string\", \"value\": \"No Artificial Flavors\" },\n            \"artificialIngredientsQualified\": { \"type\": \"string\", \"value\": \"No Artificial Ingredients\" },\n            \"artificialPreservativesQualified\": { \"type\": \"string\", \"value\": \"No Artificial Preservatives\" },\n            \"artificialSweetenersQualified\": { \"type\": \"string\", \"value\": \"No Artificial Sweeteners\" },\n            \"countOfIngredientsQualified\": { \"type\": \"string\", \"value\": \"10 Ingredients\" },\n            \"gmoPresenceQualified\": { \"type\": \"string\", \"value\": \"Non-GMO\" },\n            \"hormonesQualified\": { \"type\": \"string\", \"value\": \"No Hormones\" },\n            \"naturalColorsQualified\": { \"type\": \"string\", \"value\": \"Natural Colors\" },\n            \"naturalFlavorsQualified\": { \"type\": \"string\", \"value\": \"Natural Flavors\" },\n            \"naturalPreservativesQualified\": { \"type\": \"string\", \"value\": \"Natural Preservatives\" },\n            \"naturalSweetenersQualified\": { \"type\": \"string\", \"value\": \"Natural Sweeteners\" }\n          }\n        },\n        \"additionalInfo\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"energy\": { \"type\": \"number\", \"value\": 200 },\n            \"weight\": { \"type\": \"number\", \"value\": 340 },\n            \"categories\": { \"type\": \"string\", \"value\": \"Coffee\" },\n            \"packaging\": { \"type\": \"string\", \"value\": \"Plastic\" },\n            \"ecoscore\": { \"type\": \"string\", \"value\": \"B\" },\n            \"nova_group\": { \"type\": \"string\", \"value\": \"3\" },\n            \"nutriscore_grade\": { \"type\": \"string\", \"value\": \"C\" },\n            \"data_source\": { \"type\": \"string\", \"value\": \"Manufacturer_claims,FoodScanGenius_AI\" },\n            \"dietary_preference\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"Vegan\": { \"type\": \"boolean\", \"value\": false },\n                \"Vegetarian\": { \"type\": \"boolean\", \"value\": true },\n                \"Pescatarian\": { \"type\": \"boolean\", \"value\": false },\n                \"WhiteMeatOnly\": { \"type\": \"boolean\", \"value\": false },\n                \"KetoFriendly\": { \"type\": \"boolean\", \"value\": false },\n                \"LowFodmap\": { \"type\": \"boolean\", \"value\": false }\n              }\n            },\n            \"allergens\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"Sugar\": { \"type\": \"boolean\", \"value\": false },\n                \"Celery\": { \"type\": \"boolean\", \"value\": false },\n                \"Gluten\": { \"type\": \"boolean\", \"value\": false },\n                \"Crustaceans\": { \"type\": \"boolean\", \"value\": false },\n                \"Eggs\": { \"type\": \"boolean\", \"value\": false },\n                \"Fish\": { \"type\": \"boolean\", \"value\": false },\n                \"Lupin\": { \"type\": \"boolean\", \"value\": false },\n                \"Milk\": { \"type\": \"boolean\", \"value\": false },\n                \"Peanuts\": { \"type\": \"boolean\", \"value\": false },\n                \"Sesame\": { \"type\": \"boolean\", \"value\": false },\n                \"TreeNuts\": { \"type\": \"boolean\", \"value\": false }\n              }\n            },\n            \"religious_labels\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"Halal\": { \"type\": \"boolean\", \"value\": false },\n                \"Kosher\": { \"type\": \"boolean\", \"value\": false },\n                \"Hindu\": { \"type\": \"boolean\", \"value\": false },\n                \"Jain\": { \"type\": \"boolean\", \"value\": false }\n              }\n            },\n            \"food_safety_labels\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"GMO\": { \"type\": \"boolean\", \"value\": false },\n                \"NoGMO\": { \"type\": \"boolean\", \"value\": true },\n                \"Hormones\": { \"type\": \"boolean\", \"value\": false },\n                \"Carcinogenic\": { \"type\": \"boolean\", \"value\": false },\n                \"Organic\": { \"type\": \"boolean\", \"value\": true },\n                \"ProductRecalls\": { \"type\": \"boolean\", \"value\": false }\n              }\n            },\n            \"sustainability_labels\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"Recycled\": { \"type\": \"boolean\", \"value\": true },\n                \"AnimalWelfare\": { \"type\": \"boolean\", \"value\": true },\n                \"OrganicPositioning\": { \"type\": \"boolean\", \"value\": true },\n                \"PlantBased\": { \"type\": \"boolean\", \"value\": true },\n                \"SocialResponsibility\": { \"type\": \"boolean\", \"value\": true },\n                \"SustainablePackaging\": { \"type\": \"boolean\", \"value\": true }\n              }\n            },\n            \"average_customer_rating\": { \"type\": \"number\", \"value\": 4.5 },\n            \"ASIN\": { \"type\": \"string\", \"value\": \"B0BRGHLXHD\" },\n            \"traces\": { \"type\": \"string\", \"value\": \"None\" },\n            \"country_of_origin\": { \"type\": \"string\", \"value\": \"Colombia\" },\n            \"customerCareNumber\": { \"type\": \"string\", \"value\": \"******-288-1089\" },\n            \"email\": { \"type\": \"string\", \"value\": \"<EMAIL>\" },\n            \"websiteLink\": { \"type\": \"string\", \"value\": \"https://www.matiz.com\" }\n          }\n        }\n      }\n    }\n  }\n}\n}", "{\n  \"$schema\": \"http://json-schema.org/draft-07/schema#\",\n  \"title\": \"Comprehensive Product Information Schema\",\n  \"description\": \"A comprehensive schema that combines all product information including general data, nutritional information, serving sizes, ingredients, allergens, and product claims.\",\n  \"type\": \"object\",\n  \"properties\": {\n    \"generalData\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"gtinOnPack\": \"013562130627\",\n        \"gtin14\": \"00013562130627\",\n        \"upc12\": \"001356213062\",\n        \"brandOwner\": \"Glicks\",\n        \"brandName\": \"Glicks (Glicks)\",\n        \"department\": \"Grocery & Gourmet Food\",\n        \"superCategory\": \"Snacks & Sweets\",\n        \"category\": \"Chocolate Candy\",\n        \"subCategory\": \"Assortment Boxes\",\n        \"segment\": \"Premium Rich Kosher Dark Chocolate Coins\",\n        \"productTitle\": \"Glicks Dark Chocolate Silver Coins\",\n        \"variant\": \"\",\n        \"netWeight1Value\": 12.7,\n        \"netWeight1UOM\": \"oz\",\n        \"netWeight2Value\": 355,\n        \"netWeight2UOM\": \"g\",\n        \"unitsPerPack\": 24,\n        \"unitsPerPackDescriptor\": \"Bags\",\n        \"storage\": \"Room Temperature\",\n        \"numberOfIngredients\": 8\n      },\n      \"required\": [\"gtinOnPack\", \"brandOwner\", \"brandName\", \"department\", \"superCategory\", \"category\", \"segment\", \"productTitle\", \"netWeight1Value\", \"netWeight1UOM\", \"unitsPerPack\", \"unitsPerPackDescriptor\"]\n    },\n    \"servingSize\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"servingSize\": 1,\n        \"servingSizeUnit\": \"bag\",\n        \"servesPerPack\": 24,\n        \"servingDescription\": \"1 bag = 15g\"\n      },\n      \"required\": [\"servingSize\", \"servingSizeUnit\", \"servesPerPack\", \"servingDescription\"]\n    },\n    \"nutritionalInformation\": {\n      \"stated\": {\n        \"totalAmount\": 100,\n        \"totalAmountUOM\": \"g\",\n        \"macronutrients\": [\n          {\"name\": \"Total Fat\", \"amount\": 4.5, \"totalAmount\": 100, \"UOM\": \"g\", \"dailyValue\": 6},\n          {\"name\": \"Saturated Fat\", \"amount\": 2.5, \"totalAmount\": 100, \"UOM\": \"g\", \"dailyValue\": 13},\n          {\"name\": \"Trans Fat\", \"amount\": 0, \"totalAmount\": 100, \"UOM\": \"g\"},\n          {\"name\": \"Cholesterol\", \"amount\": 0, \"totalAmount\": 100, \"UOM\": \"mg\"},\n          {\"name\": \"Sodium\", \"amount\": 0, \"totalAmount\": 100, \"UOM\": \"mg\"},\n          {\"name\": \"Protein\", \"amount\": \"<1\", \"totalAmount\": 100, \"UOM\": \"g\"},\n          {\"name\": \"Calcium\", \"amount\": 5, \"totalAmount\": 100, \"UOM\": \"mg\"},\n          {\"name\": \"Potassium\", \"amount\": 0, \"totalAmount\": 100, \"UOM\": \"mg\"}\n        ],\n        \"micronutrients\": [\n          {\"name\": \"Vitamin D\", \"amount\": 0, \"totalAmount\": 100, \"UOM\": \"mcg\"},\n          {\"name\": \"Iron\", \"amount\": 1.3, \"totalAmount\": 100, \"UOM\": \"mg\"}\n        ]\n      },\n      \"qualified\": {\n        \"totalAmount\": 100,\n        \"totalAmountUOM\": \"g\",\n        \"macronutrients\": [],\n        \"micronutrients\": []\n      }\n    },\n    \"ingredients\": {\n      \"stated\": {\n        \"source\": \"Amazon Images\",\n        \"ingredientList\": [\n          {\"text\": \"Sugar\", \"amount\": 80, \"amountUOM\": \"g\", \"dietary_preference\": {}, \"allergens\": {}, \"religious_labels\": {}, \"food_safety_labels\": {}, \"sustainability_labels\": {}},\n          {\"text\": \"Cocoa Butter\", \"amount\": 10, \"amountUOM\": \"g\", \"dietary_preference\": {}, \"allergens\": {}, \"religious_labels\": {}, \"food_safety_labels\": {}, \"sustainability_labels\": {}},\n          {\"text\": \"Rapeseed Lecithin\", \"amount\": 1, \"amountUOM\": \"g\", \"dietary_preference\": {}, \"allergens\": {}, \"religious_labels\": {}, \"food_safety_labels\": {}, \"sustainability_labels\": {}},\n          {\"text\": \"Emulsifier (PGPR)\", \"amount\": 0.1, \"amountUOM\": \"g\", \"dietary_preference\": {}, \"allergens\": {}, \"religious_labels\": {}, \"food_safety_labels\": {}, \"sustainability_labels\": {}},\n          {\"text\": \"Artificial Flavor (Vanillin)\", \"amount\": 0.1, \"amountUOM\": \"g\", \"dietary_preference\": {}, \"allergens\": {}, \"religious_labels\": {}, \"food_safety_labels\": {}, \"sustainability_labels\": {}}\n        ]\n      },\n      \"qualified\": {\n        \"source\": \"ScrapingQualificationAI\",\n        \"ingredientList\": []\n      }\n    },\n    \"claims\": {\n      \"certifications\": [\"Certified Gluten Free\", \"Kosher\"],\n      \"nutritionalClaims\": [],\n      \"ingredientClaims\": [],\n      \"preparation\": {\n        \"storage\": \"Room Temperature\",\n        \"heatingInstructions\": {}\n      },\n      \"sustainability\": {\n        \"packaging\": [],\n        \"environmentalClaims\": []\n      },\n      \"contact\": {\n        \"website\": \"https://www.glicks.com/\",\n        \"phone\": \"******-288-1089\"\n      }\n    },\n    \"NPI 2.0 Food Packages - Allergens & Intolerances\": {\n      \"dairyLevelStated\": \"Contains Milk\",\n      \"glutenLevelStated\": \"Gluten-Free\",\n      \"caseinQualified\": \"No Casein\",\n      \"coconutQualified\": \"No Coconut\",\n      \"cornQualified\": \"No Corn\",\n      \"fishLevelStated\": \"\",\n      \"glutenQualified\": \"Gluten-Free\",\n      \"eggLevelStated\": \"\",\n      \"falcpaCommonAllergensQualified\": \"Contains Milk\",\n      \"animalByProductQualified\": \"\",\n      \"antibioticsQualified\": \"\",\n      \"artificialColorsQualified\": \"\",\n      \"artificialFlavorsQualified\": \"\",\n      \"artificialIngredientsQualified\": \"\",\n      \"gmoPresenceQualified\": \"\"\n    },\n    \"cleanLabel\": {\n      \"artisanalStated\": \"\",\n      \"coldPressedIngredientsStated\": \"\",\n      \"craftStated\": \"\",\n      \"premiumStated\": \"\",\n      \"localStated\": \"\",\n      \"madeInUsaStated\": \"Product of Israel\",\n      \"animalByProductQualified\": \"\",\n      \"antibioticsQualified\": \"\",\n      \"artificialColorsQualified\": \"\",\n      \"artificialFlavorsQualified\": \"\",\n      \"artificialIngredientsQualified\": \"\",\n      \"gmoPresenceQualified\": \"\"\n    },\n    \"additionalInfo\": {\n      \"energy\": 80,\n      \"weight\": 12.7,\n      \"categories\": \"Chocolate Candy\",\n      \"packaging\": \"Mixed Plastic Film-Net\",\n      \"ecoscore\": \"Not Available\",\n      \"nova_group\": 4,\n      \"nutriscore_grade\": \"e\",\n      \"data_source\": \"Manufacturer_claims,FoodScanGenius_AI\",\n      \"dietary_preference\": {\n        \"Vegan\": false,\n        \"Vegetarian\": false,\n        \"Pescatarian\": false,\n        \"WhiteMeatOnly\": false,\n        \"KetoFriendly\": false,\n        \"LowFodmap\": false\n      },\n      \"allergens\": {\n        \"Sugar\": true,\n        \"Celery\": false,\n        \"Gluten\": false,\n        \"Crustaceans\": false,\n        \"Eggs\": false,\n        \"Fish\": false,\n        \"Lupin\": false,\n        \"Milk\": true,\n        \"Peanuts\": false,\n        \"Sesame\": false,\n        \"TreeNuts\": false\n      },\n      \"religious_labels\": {\n        \"Halal\": false,\n        \"Kosher\": true,\n        \"Hindu\": false,\n        \"Jain\": false\n      },\n      \"food_safety_labels\": {\n        \"GMO\": false,\n        \"NoGMO\": true,\n        \"Hormones\": false,\n        \"Carcinogenic\": false,\n        \"Organic\": false,\n        \"ProductRecalls\": false\n      },\n      \"sustainability_labels\": {\n        \"Recycled\": false,\n        \"AnimalWelfare\": false,\n        \"OrganicPositioning\": false,\n        \"PlantBased\": false,\n        \"SocialResponsibility\": false,\n        \"SustainablePackaging\": false\n      },\n      \"average_customer_rating\": 3.9,\n      \"ASIN\": \"B0CKY1LY33\",\n      \"traces\": \"\",\n      \"country_of_origin\": \"Israel\",\n      \"customerCareNumber\": \"******-288-1089\",\n      \"email\": \"<EMAIL>\",\n      \"websiteLink\": \"https://www.glicks.com/\"\n    }\n  }\n}", "", "The image shows a product display featuring a bottle of Ritual Zero Proof Rum Alternative and five smaller bottles of Fever Tree Distillers Cola. The Ritual Zero Proof Rum Alternative is a non-alcoholic rum alternative, which is described as having a rich profile with toasted spices and flavors of ripe banana and burnt orange. It's designed for crafting cocktails and comes in a 750ml bottle. Accompanying it are five 200ml bottles of Fever Tree Distillers Cola, which is a non-alcoholic cola made with natural flavors including Caribbean kola nut. The overall setting suggests a focus on alcohol-free beverages, possibly aimed at consumers looking for alternatives to traditional alcoholic drinks.", "{\n  \"$schema\": \"http://json-schema.org/draft-07/schema#\",\n  \"title\": \"Comprehensive Product Information Schema\",\n  \"description\": \"A comprehensive schema that combines all product information including general data, nutritional information, serving sizes, ingredients, allergens, and product claims.\",\n  \"type\": \"object\",\n  \"properties\": {\n    \"generalData\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"gtinOnPack\": \"850048408043\",\n        \"gtin14\": \"850048408043\",\n        \"upc12\": \"850048408043\",\n        \"brandOwner\": \"FOREVER FOODS\",\n        \"brandName\": \"FOREVER FOODS\",\n        \"department\": \"Grocery & Gourmet Food\",\n        \"superCategory\": \"Meat & Seafood\",\n        \"category\": \"Beef\",\n        \"subCategory\": \"Stew Meat\",\n        \"segment\": \"Stew Meat\",\n        \"productTitle\": \"Uncooked Freeze-Dried Beef Stew Cubes\",\n        \"variant\": \"\",\n        \"netWeight1Value\": 8.5,\n        \"netWeight1UOM\": \"oz\",\n        \"netWeight2Value\": 241,\n        \"netWeight2UOM\": \"g\",\n        \"unitsPerPack\": 6,\n        \"unitsPerPackDescriptor\": \"CUPS\",\n        \"storage\": \"Refrigerated\",\n        \"numberOfIngredients\": 1\n      },\n      \"required\": [\n        \"gtinOnPack\",\n        \"gtin14\",\n        \"upc12\",\n        \"brandOwner\",\n        \"brandName\",\n        \"department\",\n        \"superCategory\",\n        \"category\",\n        \"subCategory\",\n        \"segment\",\n        \"productTitle\",\n        \"variant\",\n        \"netWeight1Value\",\n        \"netWeight1UOM\",\n        \"netWeight2Value\",\n        \"netWeight2UOM\",\n        \"unitsPerPack\",\n        \"unitsPerPackDescriptor\",\n        \"storage\",\n        \"numberOfIngredients\"\n      ]\n    },\n    \"servingSize\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"servingSize\": 49,\n        \"servingSizeUnit\": \"g\",\n        \"servesPerPack\": 6,\n        \"servingDescription\": \"1 cup uncooked cubes (49g)\",\n        \"servingSize2\": null,\n        \"servingSize2Unit\": null\n      },\n      \"required\": [\n        \"servingSize\",\n        \"servingSizeUnit\",\n        \"servesPerPack\",\n        \"servingDescription\"\n      ]\n    },\n    \"nutritionalInformation\": {\n      \"stated\": {\n        \"totalAmount\": 100,\n        \"totalAmountUOM\": \"g\",\n        \"source\": \"Amazon Images\",\n        \"macronutrients\": [\n          {\n            \"name\": \"Protein\",\n            \"amount\": 35,\n            \"totalAmount\": 100,\n            \"UOM\": \"g\",\n            \"dailyValue\": 68\n          },\n          {\n            \"name\": \"Total Fat\",\n            \"amount\": 5,\n            \"totalAmount\": 100,\n            \"UOM\": \"g\",\n            \"dailyValue\": 6\n          },\n          {\n            \"name\": \"Saturated Fat\",\n            \"amount\": 1.5,\n            \"totalAmount\": 100,\n            \"UOM\": \"g\",\n            \"dailyValue\": 8\n          },\n          {\n            \"name\": \"Cholesterol\",\n            \"amount\": 90,\n            \"totalAmount\": 100,\n            \"UOM\": \"mg\",\n            \"dailyValue\": 30\n          },\n          {\n            \"name\": \"Sodium\",\n            \"amount\": 80,\n            \"totalAmount\": 100,\n            \"UOM\": \"mg\",\n            \"dailyValue\": 3\n          },\n          {\n            \"name\": \"Total Carbohydrate\",\n            \"amount\": 0,\n            \"totalAmount\": 100,\n            \"UOM\": \"g\",\n            \"dailyValue\": 0\n          },\n          {\n            \"name\": \"Dietary Fiber\",\n            \"amount\": 0,\n            \"totalAmount\": 100,\n            \"UOM\": \"g\",\n            \"dailyValue\": 0\n          },\n          {\n            \"name\": \"Total Sugars\",\n            \"amount\": 0,\n            \"totalAmount\": 100,\n            \"UOM\": \"g\",\n            \"dailyValue\": 0\n          }\n        ],\n        \"micronutrients\": [\n          {\n            \"name\": \"Vitamin D\",\n            \"amount\": 0,\n            \"totalAmount\": 100,\n            \"UOM\": \"mcg\",\n            \"dailyValue\": 0\n          },\n          {\n            \"name\": \"Calcium\",\n            \"amount\": 20,\n            \"totalAmount\": 100,\n            \"UOM\": \"mg\",\n            \"dailyValue\": 2\n          },\n          {\n            \"name\": \"Iron\",\n            \"amount\": 2.2,\n            \"totalAmount\": 100,\n            \"UOM\": \"mg\",\n            \"dailyValue\": 10\n          },\n          {\n            \"name\": \"Potassium\",\n            \"amount\": 490,\n            \"totalAmount\": 100,\n            \"UOM\": \"mg\",\n            \"dailyValue\": 10\n          }\n        ]\n      },\n      \"qualified\": {\n        \"totalAmount\": 100,\n        \"totalAmountUOM\": \"g\",\n        \"source\": \"ScrapingQualificationAI\",\n        \"macronutrients\": [],\n        \"micronutrients\": []\n      }\n    },\n    \"ingredients\": {\n      \"stated\": {\n        \"source\": \"Product Scrape\",\n        \"ingredientList\": [\n          {\n            \"text\": \"Potato\",\n            \"amount\": 100,\n            \"amountUOM\": \"g\",\n            \"dietary_preference\": {},\n            \"allergens\": {},\n            \"religious_labels\": {},\n            \"food_safety_labels\": {},\n            \"sustainability_labels\": {},\n            \"subIngredients\": []\n          },\n          {\n            \"text\": \"Cooked Beef\",\n            \"amount\": 100,\n            \"amountUOM\": \"g\",\n            \"dietary_preference\": {},\n            \"allergens\": {},\n            \"religious_labels\": {},\n            \"food_safety_labels\": {},\n            \"sustainability_labels\": {}\n          },\n          {\n            \"text\": \"Carrot\",\n            \"amount\": 100,\n            \"amountUOM\": \"g\",\n            \"dietary_preference\": {},\n            \"allergens\": {},\n            \"religious_labels\": {},\n            \"food_safety_labels\": {},\n            \"sustainability_labels\": {}\n          },\n          {\n            \"text\": \"Green Peas\",\n            \"amount\": 100,\n            \"amountUOM\": \"g\",\n            \"dietary_preference\": {},\n            \"allergens\": {},\n            \"religious_labels\": {},\n            \"food_safety_labels\": {},\n            \"sustainability_labels\": {}\n          },\n          {\n            \"text\": \"Corn Oil with Spice Extract\",\n            \"amount\": 100,\n            \"amountUOM\": \"g\",\n            \"dietary_preference\": {},\n            \"allergens\": {},\n            \"religious_labels\": {},\n            \"food_safety_labels\": {},\n            \"sustainability_labels\": {}\n          },\n          {\n            \"text\": \"Modified Corn Starch\",\n            \"amount\": 100,\n            \"amountUOM\": \"g\",\n            \"dietary_preference\": {},\n            \"allergens\": {},\n            \"religious_labels\": {},\n            \"food_safety_labels\": {},\n            \"sustainability_labels\": {}\n          },\n          {\n            \"text\": \"Hydrolyzed Vegetable Protein\",\n            \"amount\": 100,\n            \"amountUOM\": \"g\",\n            \"dietary_preference\": {},\n            \"allergens\": {},\n            \"religious_labels\": {},\n            \"food_safety_labels\": {},\n            \"sustainability_labels\": {}\n          },\n          {\n            \"text\": \"Dehydrated Onion\",\n            \"amount\": 100,\n            \"amountUOM\": \"g\",\n            \"dietary_preference\": {},\n            \"allergens\": {},\n            \"religious_labels\": {},\n            \"food_safety_labels\": {},\n            \"sustainability_labels\": {}\n          },\n          {\n            \"text\": \"Sugar\",\n            \"amount\": 100,\n            \"amountUOM\": \"g\",\n            \"dietary_preference\": {},\n            \"allergens\": {},\n            \"religious_labels\": {},\n            \"food_safety_labels\": {},\n            \"sustainability_labels\": {}\n          },\n          {\n            \"text\": \"Spice\",\n            \"amount\": 100,\n            \"amountUOM\": \"g\",\n            \"dietary_preference\": {},\n            \"allergens\": {},\n            \"religious_labels\": {},\n            \"food_safety_labels\": {},\n            \"sustainability_labels\": {}\n          },\n          {\n            \"text\": \"Garlic Powder\",\n            \"amount\": 100,\n            \"amountUOM\": \"g\",\n            \"dietary_preference\": {},\n            \"allergens\": {},\n            \"religious_labels\": {},\n            \"food_safety_labels\": {},\n            \"sustainability_labels\": {}\n          }\n        ]\n      },\n      \"qualified\": {\n        \"source\": \"ScrapingQualificationAI\",\n        \"ingredientList\": []\n      }\n    },\n    \"claims\": {\n      \"certifications\": [],\n      \"nutritionalClaims\": [],\n      \"ingredientClaims\": [],\n      \"preparation\": {\n        \"storage\": \"Refrigerated\",\n        \"heatingInstructions\": {\n          \"toaster\": \"\",\n          \"oven\": \"\"\n        }\n      },\n      \"sustainability\": {\n        \"packaging\": \"\",\n        \"environmentalClaims\": []\n      },\n      \"contact\": {\n        \"website\": \"https://www.foreverfoods.com/\",\n        \"phone\": \"******-288-1089\"\n      }\n    },\n    \"NPI 2.0 Food Packages - Allergens & Intolerances\": {\n      \"fdaRegulatedAllergens\": \"Milk, Eggs, Wheat, Soy, Sesame, Almond, Cashew, Walnut, Pecan, Coconut\",\n      \"caseinQualified\": \"No\",\n      \"caseinStated\": \"No Casein Added\",\n      \"coconutQualified\": \"No\",\n      \"coconutStated\": \"No Coconut Added\",\n      \"cornQualified\": \"No\",\n      \"cornStated\": \"No Corn Added\",\n      \"dairyLevelStated\": \"Contains Milk\",\n      \"dairyQualified\": \"Yes\",\n      \"dairyStated\": \"Contains Milk\",\n      \"eggLevelStated\": \"Contains Eggs\",\n      \"eggQualified\": \"Yes\",\n      \"eggStated\": \"Contains Eggs\",\n      \"falcpaCommonAllergensQualified\": \"Yes\",\n      \"falcpaCommonAllergensStated\": \"Contains Milk, Eggs, Fish, Shellfish, Tree Nuts, Peanuts, Wheat, Soybeans\",\n      \"fishLevelStated\": \"Contains Fish\",\n      \"fishQualified\": \"Yes\",\n      \"fishStated\": \"Contains Fish\",\n      \"glutenLevelStated\": \"Gluten-Free\",\n      \"glutenQualified\": \"Yes\",\n      \"glutenStated\": \"Gluten-Free\",\n      \"animalByProductQualified\": \"No\",\n      \"animalByProductStated\": \"No Animal By-Products Added\",\n      \"antibioticsQualified\": \"No\",\n      \"antibioticsStated\": \"No Antibiotics Ever\",\n      \"artificialColorsQualified\": \"No\",\n      \"artificialColorsStated\": \"No Artificial Colors Added\",\n      \"artificialFlavorsQualified\": \"No\",\n      \"artificialFlavorsStated\": \"No Artificial Flavors Added\",\n      \"artificialIngredientsQualified\": \"No\",\n      \"artificialPreservativesQualified\": \"No\",\n      \"artificialPreservativesStated\": \"No Artificial Preservatives Added\",\n      \"artificialSweetenersQualified\": \"No\",\n      \"artificialSweetenersStated\": \"No Artificial Sweeteners Added\",\n      \"countOfIngredientsQualified\": \"Multiple\",\n      \"gmoPresenceQualified\": \"Non-GMO\",\n      \"gmoPresenceStated\": \"Non-GMO\",\n      \"highFructoseCornSyrupQualified\": \"No\",\n      \"highFructoseCornSyrupStated\": \"No High Fructose Corn Syrup Added\",\n      \"hormonesQualified\": \"No\",\n      \"hormonesStated\": \"No Hormones Added\",\n      \"naturalColorsQualified\": \"Yes\",\n      \"naturalColorsStated\": \"Natural Colors\",\n      \"naturalFlavorsQualified\": \"Yes\",\n      \"naturalFlavorsStated\": \"Natural Flavors\",\n      \"naturalPreservativesQualified\": \"No\",\n      \"naturalPreservativesStated\": \"No Natural Preservatives Added\",\n      \"naturalSweetenersQualified\": \"No\",\n      \"naturalSweetenersStated\": \"No Natural Sweeteners Added\",\n      \"preservativesQualified\": \"No\",\n      \"preservativesStated\": \"No Preservatives Added\",\n      \"rbstQualified\": \"No\",\n      \"rbstStated\": \"No rBST Added\",\n      \"recognizableIngredientsQualified\": \"Yes\",\n      \"sugarAlcoholsQualified\": \"No\",\n      \"sugarAlcoholsStated\": \"No Sugar Alcohols Added\",\n      \"additionalInfo\": {\n        \"energy\": 190,\n        \"weight\": 8.5,\n        \"categories\": \"Meals\",\n        \"packaging\": \"Plastic\",\n        \"ecoscore\": \"B\",\n        \"nova_group\": \"4\",\n        \"nutriscore_grade\": \"e\",\n        \"data_source\": \"Manufacturer_claims,FoodScanGenius_AI\",\n        \"dietary_preference\": {\n          \"Vegan\": false,\n          \"Vegetarian\": false,\n          \"Pescatarian\": false,\n          \"WhiteMeatOnly\": false,\n          \"KetoFriendly\": false,\n          \"LowFodmap\": false\n        },\n        \"allergens\": {\n          \"Sugar\": false,\n          \"Celery\": false,\n          \"Gluten\": false,\n          \"Crustaceans\": false,\n          \"Eggs\": true,\n          \"Fish\": false,\n          \"Lupin\": false,\n          \"Milk\": true,\n          \"Peanuts\": false,\n          \"Sesame\": false,\n          \"TreeNuts\": false\n        },\n        \"religious_labels\": {\n          \"Halal\": false,\n          \"Kosher\": false,\n          \"Hindu\": false,\n          \"Jain\": false\n        },\n        \"food_safety_labels\": {\n          \"GMO\": false,\n          \"NoGMO\": true,\n          \"Hormones\": false,\n          \"Carcinogenic\": false,\n          \"Organic\": false,\n          \"ProductRecalls\": false\n        },\n        \"sustainability_labels\": {\n          \"Recycled\": false,\n          \"AnimalWelfare\": false,\n          \"OrganicPositioning\": false,\n          \"PlantBased\": false,\n          \"SocialResponsibility\": false,\n          \"SustainablePackaging\": false\n        },\n        \"average_customer_rating\": 4.4,\n        \"ASIN\": \"B0C6NXHWBJ\",\n        \"traces\": \"\",\n        \"country_of_origin\": \"United States\",\n        \"customerCareNumber\": \"******-288-1089\",\n        \"email\": \"\",\n        \"websiteLink\": \"https://www.foreverfoods.com/\"\n      }\n    }\n  }\n}"]