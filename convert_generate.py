#!/usr/bin/env python3
"""
Script to convert generate.json from string format to dictionary format.
This script reads the generate.json file where data is stored as strings
and converts it to proper JSON dictionary format.
"""

import json

def convert_generate_json(input_file='generate.json', output_file='generate_converted.json'):
    """
    Convert generate.json file with string entries to proper dictionary format.
    
    Args:
        input_file (str): Path to input JSON file with string entries
        output_file (str): Path to output JSON file with dictionary entries
    """
    try:
        # Read the original file
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"Original file loaded. Found {len(data)} entries.")
        
        # Convert each string entry to dictionary
        converted_data = []
        for i, entry in enumerate(data):
            if isinstance(entry, str):
                # Skip empty strings
                if entry.strip() == "":
                    print(f"Skipping empty entry {i+1}")
                    continue
                    
                try:
                    # Parse the JSON string to dictionary
                    parsed_entry = json.loads(entry)
                    converted_data.append(parsed_entry)
                    print(f"Successfully converted entry {i+1}")
                except json.JSONDecodeError as e:
                    print(f"Error parsing entry {i+1}: {e}")
                    print(f"Entry content preview: {entry[:100]}...")
                    # Keep the original string if parsing fails
                    converted_data.append(entry)
            else:
                # If it's already a dictionary, keep it as is
                converted_data.append(entry)
                print(f"Entry {i+1} was already in dictionary format")
        
        # Write the converted data to new file
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(converted_data, f, indent=2, ensure_ascii=False)
        
        print(f"\nConversion completed!")
        print(f"Original file: {input_file}")
        print(f"Converted file: {output_file}")
        print(f"Total entries processed: {len(converted_data)}")
        
        return converted_data
        
    except FileNotFoundError:
        print(f"Error: File '{input_file}' not found.")
        return None
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in file '{input_file}': {e}")
        return None
    except Exception as e:
        print(f"Unexpected error: {e}")
        return None

def convert_generate_json_simple(input_file='generate.json'):
    """
    Simple conversion that directly modifies the original generate.json file.
    """
    try:
        # Read the original file
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"Loaded {len(data)} entries from {input_file}")
        
        # Convert each string entry to dictionary
        converted_data = []
        for i, entry in enumerate(data):
            if isinstance(entry, str):
                # Skip empty strings
                if entry.strip() == "":
                    print(f"Skipping empty entry {i+1}")
                    continue
                    
                try:
                    # Parse the JSON string to dictionary
                    parsed_entry = json.loads(entry)
                    converted_data.append(parsed_entry)
                    print(f"Converted entry {i+1} from string to dictionary")
                except json.JSONDecodeError as e:
                    print(f"Error parsing entry {i+1}: {e}")
                    # Keep the original string if parsing fails
                    converted_data.append(entry)
            else:
                # Keep as is if already a dictionary
                converted_data.append(entry)
        
        # Write back to the same file
        with open(input_file, 'w', encoding='utf-8') as f:
            json.dump(converted_data, f, indent=2, ensure_ascii=False)
        
        print(f"Successfully converted {input_file}!")
        print(f"All {len(converted_data)} entries are now in dictionary format.")
        
        return converted_data
        
    except Exception as e:
        print(f"Error during conversion: {e}")
        return None

def preview_generate_conversion(input_file='generate.json', num_entries=2):
    """
    Preview the conversion without writing to file.
    
    Args:
        input_file (str): Path to input JSON file
        num_entries (int): Number of entries to preview
    """
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"Preview of conversion for first {num_entries} entry(ies):")
        print("=" * 50)
        
        for i in range(min(num_entries, len(data))):
            entry = data[i]
            print(f"\nEntry {i+1}:")
            print("Before (string):")
            print(f"Type: {type(entry)}")
            
            if isinstance(entry, str):
                if entry.strip() == "":
                    print("Empty string - will be skipped")
                    continue
                    
                print(f"Length: {len(entry)} characters")
                print(f"First 100 chars: {entry[:100]}...")
                
                try:
                    parsed = json.loads(entry)
                    print("\nAfter (dictionary):")
                    print(f"Type: {type(parsed)}")
                    if isinstance(parsed, dict):
                        print(f"Top-level keys: {list(parsed.keys())}")
                        print(f"Number of top-level keys: {len(parsed.keys())}")
                    else:
                        print(f"Parsed to: {type(parsed)}")
                except json.JSONDecodeError as e:
                    print(f"Error parsing: {e}")
            else:
                print("Entry is already in dictionary format")
        
    except Exception as e:
        print(f"Error during preview: {e}")

if __name__ == "__main__":
    print("Generate.json String to Dictionary Converter")
    print("=" * 45)
    
    # First, show a preview
    print("1. Previewing conversion...")
    preview_generate_conversion()
    
    print("\n" + "=" * 45)
    
    # Ask user which conversion method they prefer
    print("Choose conversion method:")
    print("1. Safe conversion (creates generate_converted.json)")
    print("2. Direct conversion (modifies original generate.json)")
    
    choice = input("Enter your choice (1 or 2): ").strip()
    
    if choice == "1":
        print("\n2. Converting file (safe method)...")
        result = convert_generate_json()
        
        if result:
            print("\n3. Conversion successful!")
            print("You can now use 'generate_converted.json' which contains proper dictionary format.")
        else:
            print("\n3. Conversion failed. Please check the error messages above.")
            
    elif choice == "2":
        response = input("This will modify the original file. Are you sure? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            print("\n2. Converting file (direct method)...")
            result = convert_generate_json_simple()
            
            if result:
                print("\n3. Conversion successful!")
                print("Your generate.json file now contains proper dictionary format.")
            else:
                print("\n3. Conversion failed. Please check the error messages above.")
        else:
            print("Conversion cancelled.")
    else:
        print("Invalid choice. Conversion cancelled.")
