#!/usr/bin/env python3
"""
Test script to verify the output format of the generated JSONL file.
"""

import json
import sys

def test_jsonl_file(file_path: str):
    """Test the JSONL file format and content."""
    
    print(f"Testing JSONL file: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"Total lines: {len(lines)}")
        
        # Test first few entries
        for i, line in enumerate(lines[:3]):
            print(f"\n--- Entry {i+1} ---")
            
            try:
                entry = json.loads(line.strip())
                
                # Check required keys
                required_keys = ['prompt', 'response', 'images']
                for key in required_keys:
                    if key not in entry:
                        print(f"ERROR: Missing key '{key}'")
                    else:
                        print(f"✓ Has key '{key}'")
                
                # Check prompt
                prompt = entry.get('prompt', '')
                print(f"Prompt length: {len(prompt)} characters")
                if 'RESPONSE SCHEMA' in prompt:
                    print("✓ Prompt contains response schema")
                if 'Product details:' in prompt:
                    print("✓ Prompt contains product details")
                
                # Check response
                response = entry.get('response', '')
                print(f"Response length: {len(response)} characters")
                try:
                    response_data = json.loads(response)
                    print(f"✓ Response is valid JSON with keys: {list(response_data.keys())}")
                except json.JSONDecodeError:
                    print("ERROR: Response is not valid JSON")
                
                # Check images
                images = entry.get('images', [])
                print(f"Images count: {len(images)}")
                if images:
                    print(f"First image: {images[0][:100]}...")
                    
                    # Check if images are URLs or base64
                    if images[0].startswith('data:'):
                        print("✓ Images are in base64 format")
                    elif images[0].startswith('http'):
                        print("✓ Images are URLs")
                    else:
                        print("? Unknown image format")
                
            except json.JSONDecodeError as e:
                print(f"ERROR: Invalid JSON on line {i+1}: {e}")
        
        print(f"\n--- Summary ---")
        print(f"Total entries: {len(lines)}")
        print("Format appears to be correct for prompt-response training data")
        
    except Exception as e:
        print(f"Error reading file: {e}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python test_output.py <jsonl_file>")
        sys.exit(1)
    
    test_jsonl_file(sys.argv[1])
