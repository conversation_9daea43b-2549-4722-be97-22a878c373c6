{"prompt": "\nYou are a helpful assistant who can extract product information from images and provide it in JSON format based on the schema below\n\nRESPONSE SCHEMA\n{\n  \"$schema\": \"http://json-schema.org/draft-07/schema#\",\n  \"title\": \"Comprehensive Product Information Schema\",\n  \"description\": \"A comprehensive schema that combines all product information including general data, nutritional information, serving sizes, ingredients, allergens, and product claims. This schema represents a complete view of product attributes, certifications, and nutritional data in a structured format.\",\n  \"type\": \"object\",\n  \"properties\": {\n    \"generalData\": {\n      \"type\": \"object\",\n      \"description\": \"General product identification and classification information\",\n      \"properties\": {\n        \"gtinOnPack\": {\n          \"type\": \"string\",\n          \"description\": \"Global Trade Item Number (GTIN) as it appears on the product packaging. This is the standard barcode number used for product identification. Example: '013562130627' for a specific product variant.\"\n        },\n        \"gtin14\": {\n          \"type\": \"string\",\n          \"description\": \"14-digit GTIN format with leading zeros. This is the standardized format for GTIN numbers, always 14 digits long. Example: '00013562130627' (same as GTIN On Pack but with leading zeros to make it 14 digits).\"\n        },\n        \"upc12\": {\n          \"type\": \"string\",\n          \"description\": \"12-digit Universal Product Code (UPC) format without check digit. This is the standard retail barcode format used in North America. Example: '001356213062' (first digit is typically 0 for standard UPC).\"\n        },\n        \"brandOwner\": {\n          \"type\": \"string\",\n          \"description\": \"NielsenIQ Brand Owner/Manufacturer name. This is the legal entity that owns the brand. Example: 'ANNIE'S HOMEGROWN INC.' for Annie's products.\"\n        },\n        \"brandName\": {\n          \"type\": \"string\",\n          \"description\": \"NielsenIQ Brand name including parent company. This combines the brand name with its parent company for clarity. Example: 'ANNIE'S (ANNIE'S HOMEGROWN INC.)' shows both the consumer-facing brand and its corporate owner.\"\n        },\n        \"department\": {\n          \"type\": \"string\",\n          \"description\": \"NielsenIQ Department category - the highest level of product classification. Example: 'FROZEN' for frozen food products, 'DAIRY' for dairy products, 'BAKERY' for bakery items.\"\n        },\n        \"superCategory\": {\n          \"type\": \"string\",\n          \"description\": \"NielsenIQ Super Category classification - the second level of product classification. Example: 'PREPARED FOODS' for ready-to-eat items, 'BEVERAGES' for drinks, 'SNACKS' for snack products.\"\n        },\n        \"category\": {\n          \"type\": \"string\",\n          \"description\": \"NielsenIQ Category classification - a more specific product grouping. Example: 'WAFFLE' for waffle products, 'YOGURT' for yogurt products, 'CHIPS' for potato chips.\"\n        },\n        \"subCategory\": {\n          \"type\": \"string\",\n          \"description\": \"NielsenIQ Sub Category classification - a detailed product grouping. Example: 'REGULAR' for standard products, 'LIGHT' for reduced-fat items, 'ORGANIC' for organic products.\"\n        },\n        \"segment\": {\n          \"type\": \"string\",\n          \"description\": \"NielsenIQ Segment classification - the most specific product grouping. Example: 'REGULAR' for standard products, 'FLAVORED' for flavored variants, 'WHOLE GRAIN' for whole grain products.\"\n        },\n        \"productTitle\": {\n          \"type\": \"string\",\n          \"description\": \"Product title from identifying header information. This is the full product name as it appears in the system. Example: 'BIRTHDAY CAKE ORGANIC WAFFLES' or 'STRAWBERRY SHORTCAKE ORGANIC WAFFLES'.\"\n        },\n        \"variant\": {\n          \"type\": \"string\",\n          \"description\": \"Product variant or flavor information. This specifies the specific version or flavor of the product. Example: 'BIRTHDAY CAKE' or 'STRAWBERRY SHORTCAKE' for different waffle flavors.\"\n        },\n        \"netWeight1Value\": {\n          \"type\": \"number\",\n          \"description\": \"Primary net weight value of the product. This is the main weight measurement. Example: 9.8 for 9.8 ounces, 280 for 280 grams.\"\n        },\n        \"netWeight1UOM\": {\n          \"type\": \"string\",\n          \"description\": \"Primary net weight unit of measurement. This specifies the unit used for the primary weight. Example: 'oz' for ounces, 'g' for grams, 'lb' for pounds.\"\n        },\n        \"netWeight2Value\": {\n          \"type\": \"number\",\n          \"description\": \"Secondary net weight value of the product. This is an alternative weight measurement, often in a different unit. Example: 280 for 280 grams when primary is in ounces.\"\n        },\n        \"netWeight2UOM\": {\n          \"type\": \"string\",\n          \"description\": \"Secondary net weight unit of measurement. This specifies the unit used for the secondary weight. Example: 'g' for grams when primary is in ounces, or 'ml' for milliliters for liquids.\"\n        },\n        \"unitsPerPack\": {\n          \"type\": \"integer\",\n          \"description\": \"Number of individual packaging units per pack. This indicates how many individual items are in the package. Example: 8 for a pack of 8 waffles, 6 for a 6-pack of yogurt.\"\n        },\n        \"unitsPerPackDescriptor\": {\n          \"type\": \"string\",\n          \"description\": \"Description of the individual packaging units. This specifies what each unit represents. Example: 'WAFFLES' for waffle products, 'BARS' for snack bars, 'POUCHES' for individual pouches.\"\n        },\n        \"storage\": {\n          \"type\": \"string\",\n          \"description\": \"Product storage requirements. This indicates how the product should be stored. Example: 'Frozen' for frozen products, 'Refrigerated' for cold items, 'Room Temperature' for shelf-stable products.\"\n        },\n        \"numberOfIngredients\": {\n          \"type\": \"integer\",\n          \"description\": \"Total number of ingredients in the product, including all nested ingredients. This should be a count of all individual ingredients at all levels of nesting.\"\n        }\n      },\n      \"required\": [\n        \"gtinOnPack\",\n        \"gtin14\",\n        \"upc12\",\n        \"brandOwner\",\n        \"brandName\",\n        \"department\",\n        \"superCategory\",\n        \"category\",\n        \"subCategory\",\n        \"segment\",\n        \"productTitle\",\n        \"variant\",\n        \"netWeight1Value\",\n        \"netWeight1UOM\",\n        \"netWeight2Value\",\n        \"netWeight2UOM\",\n        \"unitsPerPack\",\n        \"unitsPerPackDescriptor\",\n        \"storage\",\n        \"numberOfIngredients\"\n      ]\n    },\n    \"servingSize\": {\n      \"type\": \"object\",\n      \"description\": \"Product serving size information\",\n      \"properties\": {\n        \"servingSize\": {\n          \"type\": \"number\",\n          \"description\": \"Primary serving size value. This represents the standard serving amount for the product. For example: 70 for a 70g serving, 1 for a 1 cup serving, or 2 for a 2-piece serving.\"\n        },\n        \"servingSizeUnit\": {\n          \"type\": \"string\",\n          \"description\": \"Unit of measure for the primary serving size. Common units include: 'g' for grams, 'oz' for ounces, 'ml' for milliliters, 'cup' for cups, or 'piece' for individual items. Example: 'g' for a 70g serving.\"\n        },\n        \"servesPerPack\": {\n          \"type\": \"integer\",\n          \"description\": \"Number of servings per package. This indicates how many standard servings are contained in the entire package. For example: 4 means the package contains 4 servings, 6 means 6 servings, etc.\"\n        },\n        \"servingDescription\": {\n          \"type\": \"string\",\n          \"description\": \"Textual description of the serving size that provides context about how the serving size should be interpreted. Examples: '2 Waffles', '1/2 cup (120g)', '1 bar (40g)', '1 pouch (28g)'.\"\n        },\n        \"servingSize2\": {\n          \"type\": [\n            \"number\",\n            \"null\"\n          ],\n          \"description\": \"Optional secondary serving size value. This is used when a product provides an alternative serving size measurement. For example: if primary is in grams (70g), secondary might be in pieces (2 pieces). Can be null if no secondary serving size is provided.\"\n        },\n        \"servingSize2Unit\": {\n          \"type\": [\n            \"string\",\n            \"null\"\n          ],\n          \"description\": \"Unit of measure for the secondary serving size. Used in conjunction with servingSize2. Examples: 'piece' for number of items, 'oz' for ounces, 'ml' for milliliters. Can be null if no secondary serving size is provided.\"\n        }\n      },\n      \"required\": [\n        \"servingSize\",\n        \"servingSizeUnit\",\n        \"servesPerPack\",\n        \"servingDescription\"\n      ]\n    },\n    \"nutritionalInformation\": {\n      \"type\": \"object\",\n      \"description\": \"Nutritional information divided into stated (explicitly mentioned) and qualified (AI-inferred) values\",\n      \"properties\": {\n        \"stated\": {\n          \"type\": \"object\",\n          \"description\": \"Nutritional information as explicitly stated on the product packaging\",\n          \"properties\": {\n            \"totalAmount\": {\n              \"type\": \"number\",\n              \"description\": \"The total amount that all nutrient values are measured against as stated on packaging. For example, if nutrients are measured per 100g, this would be 100. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n            },\n            \"totalAmountUOM\": {\n              \"type\": \"string\",\n              \"description\": \"Unit of measurement for the total amount as stated on packaging. Common units include: 'g' for grams, 'oz' for ounces, 'ml' for milliliters. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n            },\n            \"source\": {\n              \"type\": \"string\",\n              \"enum\": [\n                \"Amazon Images\",\n                \"Product Scrape\",\n                \"ScrapingQualificationAI\"\n              ],\n              \"description\": \"Source of the stated nutritional information\"\n            },\n            \"macronutrients\": {\n              \"type\": \"array\",\n              \"description\": \"Primary nutrients essential in larger quantities as stated on packaging\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Name of the nutrient as stated. Like 'Protein', 'Carbohydrates', 'Fat', 'Fibre', 'Sugar', 'Salt', etc.\"\n                  },\n                  \"amount\": {\n                    \"type\": \"number\",\n                    \"description\": \"Amount of the nutrient as stated on packaging. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"totalAmount\": {\n                    \"type\": \"number\",\n                    \"description\": \"The total amount that the nutrient value is measured against as stated. For example, if the nutrient is 10g out of 100g, this would be 100. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"UOM\": {\n                    \"type\": \"string\",\n                    \"description\": \"Unit of measurement for the nutrient as stated, for example 'g' or 'mg' or 'oz'. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"dailyValue\": {\n                    \"type\": \"number\",\n                    \"description\": \"Percentage of recommended daily intake as stated. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"source\": {\n                    \"type\": \"string\",\n                    \"enum\": [\n                      \"Amazon Images\",\n                      \"Product Scrape\",\n                      \"ScrapingQualificationAI\"\n                    ],\n                    \"description\": \"Source of this stated nutrient information\"\n                  }\n                },\n                \"required\": [\n                  \"name\",\n                  \"amount\",\n                  \"totalAmount\",\n                  \"UOM\",\n                  \"dailyValue\",\n                  \"source\"\n                ]\n              }\n            },\n            \"micronutrients\": {\n              \"type\": \"array\",\n              \"description\": \"Essential nutrients required in smaller quantities as stated on packaging\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Name of the nutrient as stated. Like 'Vitamin A', 'Vitamin C', 'Calcium', 'Iron', 'Zinc', etc.\"\n                  },\n                  \"amount\": {\n                    \"type\": \"number\",\n                    \"description\": \"Amount of the nutrient as stated on packaging. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"totalAmount\": {\n                    \"type\": \"number\",\n                    \"description\": \"The total amount that the nutrient value is measured against as stated. For example, if the nutrient is 10mg out of 100g, this would be 100. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"UOM\": {\n                    \"type\": \"string\",\n                    \"description\": \"Unit of measurement for the nutrient as stated, for example 'g' or 'mg' or 'oz'. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"dailyValue\": {\n                    \"type\": \"number\",\n                    \"description\": \"Percentage of recommended daily intake as stated. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"source\": {\n                    \"type\": \"string\",\n                    \"enum\": [\n                      \"Amazon Images\",\n                      \"Product Scrape\",\n                      \"ScrapingQualificationAI\"\n                    ],\n                    \"description\": \"Source of this stated nutrient information\"\n                  }\n                },\n                \"required\": [\n                  \"name\",\n                  \"amount\",\n                  \"totalAmount\",\n                  \"UOM\",\n                  \"dailyValue\",\n                  \"source\"\n                ]\n              }\n            }\n          },\n          \"required\": [\n            \"totalAmount\",\n            \"totalAmountUOM\",\n            \"source\",\n            \"macronutrients\",\n            \"micronutrients\"\n          ]\n        },\n        \"qualified\": {\n          \"type\": \"object\",\n          \"description\": \"Nutritional information inferred or calculated by AI based on ingredients and product knowledge. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\",\n          \"properties\": {\n            \"totalAmount\": {\n              \"type\": \"number\",\n              \"description\": \"The total amount that all nutrient values are measured against as inferred by AI. For example, if nutrients are estimated per 100g, this would be 100. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n            },\n            \"totalAmountUOM\": {\n              \"type\": \"string\",\n              \"description\": \"Unit of measurement for the total amount as inferred by AI. Common units include: 'g' for grams, 'oz' for ounces, 'ml' for milliliters. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n            },\n            \"source\": {\n              \"type\": \"string\",\n              \"enum\": [\n                \"Amazon Images\",\n                \"Product Scrape\",\n                \"ScrapingQualificationAI\"\n              ],\n              \"description\": \"Source of the qualified nutritional information (typically 'ScrapingQualificationAI' for inferred values)\"\n            },\n            \"macronutrients\": {\n              \"type\": \"array\",\n              \"description\": \"Primary nutrients essential in larger quantities as inferred by AI. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Name of the nutrient as inferred. Like 'Protein', 'Carbohydrates', 'Fat', 'Fibre', 'Sugar', 'Salt', etc.\"\n                  },\n                  \"amount\": {\n                    \"type\": \"number\",\n                    \"description\": \"Amount of the nutrient as estimated by AI based on ingredients and product knowledge. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"totalAmount\": {\n                    \"type\": \"number\",\n                    \"description\": \"The total amount that the nutrient value is measured against as inferred. For example, if the nutrient is estimated as 10g out of 100g, this would be 100. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"UOM\": {\n                    \"type\": \"string\",\n                    \"description\": \"Unit of measurement for the nutrient as inferred, for example 'g' or 'mg' or 'oz'. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"dailyValue\": {\n                    \"type\": \"number\",\n                    \"description\": \"Percentage of recommended daily intake as calculated by AI. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"source\": {\n                    \"type\": \"string\",\n                    \"enum\": [\n                      \"Amazon Images\",\n                      \"Product Scrape\",\n                      \"ScrapingQualificationAI\"\n                    ],\n                    \"description\": \"Source of this qualified nutrient information (typically 'ScrapingQualificationAI' for inferred values)\"\n                  }\n                },\n                \"required\": [\n                  \"name\",\n                  \"amount\",\n                  \"totalAmount\",\n                  \"UOM\",\n                  \"dailyValue\",\n                  \"source\"\n                ]\n              }\n            },\n            \"micronutrients\": {\n              \"type\": \"array\",\n              \"description\": \"Essential nutrients required in smaller quantities as inferred by AI. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Name of the nutrient as inferred. Like 'Vitamin A', 'Vitamin C', 'Calcium', 'Iron', 'Zinc', etc.\"\n                  },\n                  \"amount\": {\n                    \"type\": \"number\",\n                    \"description\": \"Amount of the nutrient as estimated by AI based on ingredients and product knowledge. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"totalAmount\": {\n                    \"type\": \"number\",\n                    \"description\": \"The total amount that the nutrient value is measured against as inferred. For example, if the nutrient is estimated as 10mg out of 100g, this would be 100. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"UOM\": {\n                    \"type\": \"string\",\n                    \"description\": \"Unit of measurement for the nutrient as inferred, for example 'g' or 'mg' or 'oz'. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"dailyValue\": {\n                    \"type\": \"number\",\n                    \"description\": \"Percentage of recommended daily intake as calculated by AI. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"source\": {\n                    \"type\": \"string\",\n                    \"enum\": [\n                      \"Amazon Images\",\n                      \"Product Scrape\",\n                      \"ScrapingQualificationAI\"\n                    ],\n                    \"description\": \"Source of this qualified nutrient information (typically 'ScrapingQualificationAI' for inferred values)\"\n                  }\n                },\n                \"required\": [\n                  \"name\",\n                  \"amount\",\n                  \"totalAmount\",\n                  \"UOM\",\n                  \"dailyValue\",\n                  \"source\"\n                ]\n              }\n            }\n          },\n          \"required\": [\n            \"totalAmount\",\n            \"totalAmountUOM\",\n            \"source\",\n            \"macronutrients\",\n            \"micronutrients\"\n          ]\n        }\n      },\n      \"required\": [\n        \"stated\",\n        \"qualified\"\n      ]\n    },\n    \"ingredients\": {\n      \"type\": \"object\",\n      \"description\": \"Ingredient information divided into stated (explicitly mentioned) and qualified (AI-inferred) values\",\n      \"properties\": {\n        \"stated\": {\n          \"type\": \"object\",\n          \"description\": \"Ingredients as explicitly stated on the product packaging\",\n          \"properties\": {\n            \"source\": {\n              \"type\": \"string\",\n              \"enum\": [\n                \"Amazon Images\",\n                \"Product Scrape\",\n                \"ScrapingQualificationAI\"\n              ],\n              \"description\": \"Source of the stated ingredient information\"\n            },\n            \"ingredientList\": {\n              \"type\": \"array\",\n              \"description\": \"List of ingredients as explicitly stated on packaging. If you find the ingredients to be nested, extract the nested ingredients as well. For example, if the text is 'Chocolate (sugar, whole milk, cocoa butter, chocolate liquor, soy lecithin 'an emulsifier' and pure vanilla)' then 'Chocolate' is the parent ingredient and 'sugar', 'whole milk', 'cocoa butter', 'chocolate liquor', 'soy lecithin 'an emulsifier' and 'pure vanilla' are the nested ingredients. All the properties of ingredients like amount, amountUOM, dietary_preference, allergens, religious_labels, food_safety_labels, sustainability_labels, etc. should be extracted for the parent as well as the nested ingredients.\",\n              \"items\": {\n                \"$ref\": \"#/definitions/Ingredient\"\n              }\n            }\n          },\n          \"required\": [\n            \"source\",\n            \"ingredientList\"\n          ]\n        },\n        \"qualified\": {\n          \"type\": \"object\",\n          \"description\": \"Ingredients as analyzed and inferred by AI based on product knowledge and additional research. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\",\n          \"properties\": {\n            \"source\": {\n              \"type\": \"string\",\n              \"enum\": [\n                \"Amazon Images\",\n                \"Product Scrape\",\n                \"ScrapingQualificationAI\"\n              ],\n              \"description\": \"Source of the qualified ingredient information (typically 'ScrapingQualificationAI' for inferred values)\"\n            },\n            \"ingredientList\": {\n              \"type\": \"array\",\n              \"description\": \"List of ingredients as analyzed by AI, including potential hidden ingredients, processing aids, or ingredients that may not be explicitly listed but are commonly found in similar products. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\",\n              \"items\": {\n                \"$ref\": \"#/definitions/Ingredient\"\n              }\n            }\n          },\n          \"required\": [\n            \"source\",\n            \"ingredientList\"\n          ]\n        }\n      },\n      \"required\": [\n        \"stated\",\n        \"qualified\"\n      ]\n    },\n    \"claims\": {\n      \"type\": \"object\",\n      \"description\": \"Product claims and marketing information\",\n      \"properties\": {\n        \"certifications\": {\n          \"type\": \"array\",\n          \"description\": \"Product certifications and approvals from recognized organizations. Examples include: ['USDA Organic', 'Non-GMO Project Verified', 'Gluten-Free Certified', 'Kosher', 'Vegan Certified']. These certifications indicate that the product meets specific standards set by certifying bodies.\",\n          \"items\": {\n            \"type\": \"string\"\n          }\n        },\n        \"nutritionalClaims\": {\n          \"type\": \"array\",\n          \"description\": \"Nutritional claims and benefits stated on the product. Examples include: ['Good Source of Fiber', 'Low Sodium', 'High in Protein', 'No Added Sugar', 'Reduced Fat']. These claims must comply with regulatory guidelines for nutritional labeling.\",\n          \"items\": {\n            \"type\": \"string\"\n          }\n        },\n        \"ingredientClaims\": {\n          \"type\": \"array\",\n          \"description\": \"Claims about ingredients and their sourcing. Examples include: ['Made with Real Fruit', '100% Whole Grain', 'No Artificial Colors', 'No Preservatives', 'Locally Sourced Ingredients']. These claims highlight specific aspects of the ingredients used.\",\n          \"items\": {\n            \"type\": \"string\"\n          }\n        },\n        \"preparation\": {\n          \"type\": \"object\",\n          \"description\": \"Product preparation instructions and storage requirements\",\n          \"properties\": {\n            \"storage\": {\n              \"type\": \"string\",\n              \"description\": \"Storage instructions for maintaining product quality. Examples: 'Keep frozen until ready to use', 'Store in a cool, dry place', 'Refrigerate after opening'. These instructions are crucial for food safety and product quality.\"\n            },\n            \"heatingInstructions\": {\n              \"type\": \"object\",\n              \"description\": \"Detailed heating/cooking instructions for different preparation methods\",\n              \"properties\": {\n                \"toaster\": {\n                  \"type\": \"string\",\n                  \"description\": \"Toaster preparation instructions. Example: 'Toast on medium setting for 2-3 minutes until golden brown'. These instructions are specific to toaster preparation.\"\n                },\n                \"oven\": {\n                  \"type\": \"string\",\n                  \"description\": \"Oven preparation instructions. Example: 'Preheat oven to 375\\u00b0F, bake for 8-10 minutes until crispy'. These instructions are specific to oven preparation.\"\n                }\n              },\n              \"required\": [\n                \"toaster\",\n                \"oven\"\n              ]\n            }\n          },\n          \"required\": [\n            \"storage\",\n            \"heatingInstructions\"\n          ]\n        },\n        \"sustainability\": {\n          \"type\": \"object\",\n          \"description\": \"Environmental and sustainability claims about the product and its packaging\",\n          \"properties\": {\n            \"packaging\": {\n              \"type\": \"array\",\n              \"description\": \"Information about packaging materials and recyclability. Examples include: ['100% Recyclable Packaging', 'Made from 30% Post-Consumer Recycled Material', 'Compostable Packaging']. These claims indicate the environmental attributes of the packaging.\",\n              \"items\": {\n                \"type\": \"string\"\n              }\n            },\n            \"environmentalClaims\": {\n              \"type\": \"array\",\n              \"description\": \"Environmental impact claims about the product and its production. Examples include: ['Carbon Neutral', 'Sustainably Sourced', 'Water Conservation Practices', 'Reduced Carbon Footprint']. These claims highlight the product's environmental benefits.\",\n              \"items\": {\n                \"type\": \"string\"\n              }\n            }\n          },\n          \"required\": [\n            \"packaging\",\n            \"environmentalClaims\"\n          ]\n        },\n        \"contact\": {\n          \"type\": \"object\",\n          \"description\": \"Company contact information for customer inquiries and support\",\n          \"properties\": {\n            \"website\": {\n              \"type\": \"string\",\n              \"description\": \"Company website URL for additional information. Example: 'https://www.annies.com'. This is the primary online resource for product information.\"\n            },\n            \"phone\": {\n              \"type\": \"string\",\n              \"description\": \"Customer service phone number for inquiries. Example: '**************'. This provides direct access to customer support.\"\n            }\n          },\n          \"required\": [\n            \"website\",\n            \"phone\"\n          ]\n        }\n      },\n      \"required\": [\n        \"certifications\",\n        \"nutritionalClaims\",\n        \"ingredientClaims\",\n        \"preparation\",\n        \"sustainability\",\n        \"contact\"\n      ]\n    },\n    \"NPI 2.0 Food Packages - Allergens & Intolerances\": {\n      \"type\": \"object\",\n      \"description\": \"Allergen and intolerance information\",\n      \"properties\": {\n        \"fdaRegulatedAllergens\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI All FDA Regulated Allergens. This field lists all allergens that are regulated by the FDA and present in the product. Example: 'Contains: Milk, Eggs, Wheat, Soy' or 'May contain traces of: Tree Nuts, Peanuts'.\"\n        },\n        \"caseinQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Casein Qualified. Indicates if the product contains casein or casein derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        },\n        \"caseinStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Casein Stated. Explicit statement about casein content on the product label. Example: 'Made with Casein' or 'No Casein Added'.\"\n        },\n        \"coconutQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Coconut Qualified. Indicates if the product contains coconut or coconut derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        },\n        \"coconutStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Coconut Stated. Explicit statement about coconut content on the product label. Example: 'Made with Coconut' or 'No Coconut Added'.\"\n        },\n        \"cornQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Corn Qualified. Indicates if the product contains corn or corn derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        },\n        \"cornStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Corn Stated. Explicit statement about corn content on the product label. Example: 'Made with Corn' or 'No Corn Added'.\"\n        },\n        \"dairyLevelStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Dairy Level Stated. Specifies the level of dairy content in the product. Example: 'Contains 2% Milk' or 'Lactose-Free'.\"\n        },\n        \"dairyQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Dairy Qualified. Indicates if the product contains dairy or dairy derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        },\n        \"dairyStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Dairy Stated. Explicit statement about dairy content on the product label. Example: 'Made with Real Dairy' or 'No Dairy Added'.\"\n        },\n        \"eggLevelStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Egg Level Stated. Specifies the level of egg content in the product. Example: 'Contains Whole Eggs' or 'Egg-Free'.\"\n        },\n        \"eggQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Egg Qualified. Indicates if the product contains eggs or egg derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        },\n        \"eggStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Egg Stated. Explicit statement about egg content on the product label. Example: 'Made with Real Eggs' or 'No Eggs Added'.\"\n        },\n        \"falcpaCommonAllergensQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI FALCPA Common Allergens Qualified. Indicates if the product contains any of the major food allergens as defined by FALCPA. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        },\n        \"falcpaCommonAllergensStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI FALCPA Common Allergens Stated. Explicit statement about FALCPA-defined allergen content on the product label. Example: 'Contains: Milk, Eggs, Fish, Shellfish, Tree Nuts, Peanuts, Wheat, Soybeans'.\"\n        },\n        \"fishLevelStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Fish Level Stated. Specifies the level of fish content in the product. Example: 'Contains Fish' or 'Fish-Free'.\"\n        },\n        \"fishQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Fish Qualified. Indicates if the product contains fish or fish derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        },\n        \"fishStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Fish Stated. Explicit statement about fish content on the product label. Example: 'Made with Real Fish' or 'No Fish Added'.\"\n        },\n        \"glutenLevelStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Gluten Level Stated. Specifies the level of gluten content in the product. Example: 'Gluten-Free' or 'Contains Gluten'.\"\n        },\n        \"glutenQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Gluten Qualified. Indicates if the product contains gluten or gluten derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        }\n      }\n    },\n    \"cleanLabel\": {\n      \"type\": \"object\",\n      \"description\": \"Clean label and ingredient quality claims\",\n      \"properties\": {\n        \"artisanalStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Artisanal Stated. Explicit statement about artisanal production methods. Example: 'Handcrafted' or 'Artisan Made'.\"\n        },\n        \"coldPressedIngredientsStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Cold Pressed Ingredients Stated. Explicit statement about cold-pressed ingredients. Example: 'Made with Cold-Pressed Oils' or 'Cold-Pressed Juices'.\"\n        },\n        \"craftStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Craft Stated. Explicit statement about craft production methods. Example: 'Craft Brewed' or 'Small Batch Crafted'.\"\n        },\n        \"gourmetStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Gourmet Stated. Explicit statement about gourmet quality. Example: 'Gourmet Quality' or 'Premium Gourmet'.\"\n        },\n        \"localStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Local Stated. Explicit statement about local sourcing. Example: 'Locally Sourced' or 'Made with Local Ingredients'.\"\n        },\n        \"madeInUsaStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Made In USA Stated. Explicit statement about USA origin. Example: 'Made in USA' or 'Product of USA'.\"\n        },\n        \"premiumStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Premium Stated. Explicit statement about premium quality. Example: 'Premium Quality' or 'Premium Ingredients'.\"\n        },\n        \"smallBatchStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Small Batch Stated. Explicit statement about small batch production. Example: 'Small Batch Produced' or 'Crafted in Small Batches'.\"\n        },\n        \"animalByProductQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Animal By Product Qualified. Indicates if the product contains animal by-products. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"animalByProductStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Animal By Product Stated. Explicit statement about animal by-product content. Example: 'Made with Animal By-Products' or 'No Animal By-Products Added'.\"\n        },\n        \"antibioticsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Antibiotics Qualified. Indicates if the product contains ingredients from animals treated with antibiotics. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"antibioticsStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Antibiotics Stated. Explicit statement about antibiotic use. Example: 'No Antibiotics Ever' or 'Raised Without Antibiotics'.\"\n        },\n        \"artificialColorsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Colors Qualified. Indicates if the product contains artificial colors. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"artificialColorsStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Colors Stated. Explicit statement about artificial color content. Example: 'No Artificial Colors Added' or 'Colored with Artificial Dyes'.\"\n        },\n        \"artificialFlavorsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Flavors Qualified. Indicates if the product contains artificial flavors. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"artificialFlavorsStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Flavors Stated. Explicit statement about artificial flavor content. Example: 'No Artificial Flavors Added' or 'Flavored with Artificial Ingredients'.\"\n        },\n        \"artificialIngredientsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Ingredients Qualified. Indicates if the product contains any artificial ingredients. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"artificialPreservativesQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Preservatives Qualified. Indicates if the product contains artificial preservatives. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"artificialPreservativesStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Preservatives Stated. Explicit statement about artificial preservative content. Example: 'No Artificial Preservatives Added' or 'Preserved with Artificial Ingredients'.\"\n        },\n        \"artificialSweetenersQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Sweeteners Qualified. Indicates if the product contains artificial sweeteners. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"artificialSweetenersStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Sweeteners Stated. Explicit statement about artificial sweetener content. Example: 'No Artificial Sweeteners Added' or 'Sweetened with Artificial Sweeteners'.\"\n        },\n        \"countOfIngredientsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Count Of Ingredients Qualified. Indicates the number of ingredients in the product. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"gmoPresenceQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI GMO Presence Qualified. Indicates if the product contains genetically modified organisms. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"gmoPresenceStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI GMO Presence Stated. Explicit statement about GMO content. Example: 'Non-GMO Project Verified' or 'Made with GMO Ingredients'.\"\n        },\n        \"highFructoseCornSyrupQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI High Fructose Corn Syrup Qualified. Indicates if the product contains high fructose corn syrup. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"highFructoseCornSyrupStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI High Fructose Corn Syrup Stated. Explicit statement about high fructose corn syrup content. Example: 'No High Fructose Corn Syrup Added' or 'Sweetened with High Fructose Corn Syrup'.\"\n        },\n        \"hormonesQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Hormones Qualified. Indicates if the product contains ingredients from animals treated with hormones. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"hormonesStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Hormones Stated. Explicit statement about hormone use. Example: 'No Hormones Added' or 'Raised Without Added Hormones'.\"\n        },\n        \"naturalColorsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Colors Qualified. Indicates if the product contains natural colors. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"naturalColorsStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Colors Stated. Explicit statement about natural color content. Example: 'Colored with Natural Ingredients' or 'Naturally Colored with Fruit and Vegetable Extracts'.\"\n        },\n        \"naturalFlavorsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Flavors Qualified. Indicates if the product contains natural flavors. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"naturalFlavorsStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Flavors Stated. Explicit statement about natural flavor content. Example: 'Flavored with Natural Ingredients' or 'Naturally Flavored with Real Fruit'.\"\n        },\n        \"naturalPreservativesQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Preservatives Qualified. Indicates if the product contains natural preservatives. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"naturalPreservativesStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Preservatives Stated. Explicit statement about natural preservative content. Example: 'Preserved with Natural Ingredients' or 'Naturally Preserved with Vitamin E'.\"\n        },\n        \"naturalSweetenersQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Sweeteners Qualified. Indicates if the product contains natural sweeteners. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"naturalSweetenersStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Sweeteners Stated. Explicit statement about natural sweetener content. Example: 'Sweetened with Natural Ingredients' or 'Naturally Sweetened with Honey'.\"\n        },\n        \"preservativesQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Preservatives Qualified. Indicates if the product contains any preservatives. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"preservativesStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Preservatives Stated. Explicit statement about preservative content. Example: 'No Preservatives Added' or 'Preserved to Maintain Freshness'.\"\n        },\n        \"rbstQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI RBST Qualified. Indicates if the product contains ingredients from cows treated with rBST. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"rbstStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI RBST Stated. Explicit statement about rBST content. Example: 'No rBST Added' or 'From Cows Not Treated with rBST'.\"\n        },\n        \"recognizableIngredientsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Recognizable Ingredients Qualified. Indicates if the product contains easily recognizable ingredients. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"sugarAlcoholsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Sugar Alcohols Qualified. Indicates if the product contains sugar alcohols. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"sugarAlcoholsStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Sugar Alcohols Stated. Explicit statement about sugar alcohol content. Example: 'No Sugar Alcohols Added' or 'Sweetened with Sugar Alcohols'.\"\n        }\n      }\n    },\n    \"additionalInfo\": {\n      \"type\": \"object\",\n      \"description\": \"Additional product information and characteristics\",\n      \"properties\": {\n        \"energy\": {\n          \"type\": \"number\",\n          \"description\": \"Energy content in calories. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n        },\n        \"weight\": {\n          \"type\": \"number\",\n          \"description\": \"Total product weight in grams. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n        },\n        \"categories\": {\n          \"type\": \"string\",\n          \"description\": \"Product category classifications\"\n        },\n        \"packaging\": {\n          \"type\": \"string\",\n          \"description\": \"Type of packaging material or container. For example, 'Plastic', 'Glass', 'Paper', 'Aluminum', 'Other'\"\n        },\n        \"ecoscore\": {\n          \"type\": \"string\",\n          \"description\": \"Environmental impact score. Try to compute this from the ingredients and other product information. Follow the calculation method from Open Food Facts. Do not let this be null. Provide your best guess if not mentioned.\"\n        },\n        \"nova_group\": {\n          \"type\": \"string\",\n          \"description\": \"NOVA food classification group. Try to compute this from the ingredients and other product information. Follow the calculation method from Open Food Facts. Do not let this be null. Provide your best guess if not mentioned.\"\n        },\n        \"nutriscore_grade\": {\n          \"type\": \"string\",\n          \"description\": \"Nutri-Score grade (A to E) indicating nutritional quality. Try to compute this from the ingredients and other product information. Follow the calculation method from Open Food Facts. Do not let this be null. Provide your best guess if not mentioned.\"\n        },\n        \"data_source\": {\n          \"type\": \"string\",\n          \"description\": \"Always respond with 'Manufactruer_claims,FoodScanGenius_AI'\"\n        },\n        \"dietary_preference\": {\n          \"type\": \"object\",\n          \"description\": \"Dietary preferences of the combined ingredients\",\n          \"properties\": {\n            \"Vegan\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a vegan diet\"\n            },\n            \"Vegetarian\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a vegetarian diet\"\n            },\n            \"Pescatarian\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a pescatarian diet\"\n            },\n            \"WhiteMeatOnly\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain only white meat\"\n            },\n            \"KetoFriendly\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a ketogenic diet\"\n            },\n            \"LowFodmap\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a low FODMAP diet\"\n            }\n          }\n        },\n        \"allergens\": {\n          \"type\": \"object\",\n          \"description\": \"Allergens present in the combined ingredients\",\n          \"properties\": {\n            \"Sugar\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain sugar\"\n            },\n            \"Celery\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain celery\"\n            },\n            \"Gluten\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain gluten\"\n            },\n            \"Crustaceans\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain crustaceans\"\n            },\n            \"Eggs\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain eggs\"\n            },\n            \"Fish\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain fish\"\n            },\n            \"Lupin\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain lupin\"\n            },\n            \"Milk\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain milk\"\n            },\n            \"Peanuts\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain peanuts\"\n            },\n            \"Sesame\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain sesame\"\n            },\n            \"TreeNuts\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain tree nuts\"\n            }\n          }\n        },\n        \"religious_labels\": {\n          \"type\": \"object\",\n          \"description\": \"Religious labels present in the combined ingredients\",\n          \"properties\": {\n            \"Halal\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are Halal certified\"\n            },\n            \"Kosher\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are Kosher certified\"\n            },\n            \"Hindu\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for Hindu dietary requirements\"\n            },\n            \"Jain\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for Jain dietary requirements\"\n            }\n          }\n        },\n        \"food_safety_labels\": {\n          \"type\": \"object\",\n          \"description\": \"Food safety labels present in the combined ingredients\",\n          \"properties\": {\n            \"GMO\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain GMO components\"\n            },\n            \"NoGMO\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are GMO-free\"\n            },\n            \"Hormones\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain hormones\"\n            },\n            \"Carcinogenic\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are potentially carcinogenic\"\n            },\n            \"Organic\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are organic\"\n            },\n            \"ProductRecalls\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients have been subject to product recalls\"\n            }\n          }\n        },\n        \"sustainability_labels\": {\n          \"type\": \"object\",\n          \"description\": \"Sustainability labels present in the combined ingredients\",\n          \"properties\": {\n            \"Recycled\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are made from recycled materials\"\n            },\n            \"AnimalWelfare\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients meet animal welfare standards\"\n            },\n            \"OrganicPositioning\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are positioned as organic\"\n            },\n            \"PlantBased\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are plant-based\"\n            },\n            \"SocialResponsibility\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients meet social responsibility standards\"\n            },\n            \"SustainablePackaging\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients use sustainable packaging\"\n            }\n          }\n        },\n        \"average_customer_rating\": {\n          \"type\": \"number\",\n          \"description\": \"Average customer rating of the product. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n        },\n        \"ASIN\": {\n          \"type\": \"string\",\n          \"description\": \"Amazon Standard Identification Number. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n        },\n        \"traces\": {\n          \"type\": \"string\",\n          \"description\": \"Potential trace ingredients or contamination warnings\"\n        },\n        \"country_of_origin\": {\n          \"type\": \"string\",\n          \"description\": \"Country where the product was manufactured or produced. For example, 'United States', 'United Kingdom', 'France', 'Germany', 'Italy', 'Spain', 'Portugal', 'Greece', 'Turkey', 'Other'\"\n        },\n        \"customerCareNumber\": {\n          \"type\": \"string\",\n          \"description\": \"Contact number for customer support\"\n        },\n        \"email\": {\n          \"type\": \"string\",\n          \"description\": \"Contact email address for the manufacturer\"\n        },\n        \"websiteLink\": {\n          \"type\": \"string\",\n          \"description\": \"Official product or manufacturer website URL\"\n        }\n      },\n      \"required\": [\n        \"data_source\",\n        \"dietary_preference\",\n        \"allergens\",\n        \"religious_labels\",\n        \"food_safety_labels\",\n        \"sustainability_labels\",\n        \"country_of_origin\",\n        \"customerCareNumber\",\n        \"email\",\n        \"websiteLink\"\n      ]\n    }\n  },\n  \"definitions\": {\n    \"Ingredient\": {\n      \"type\": \"object\",\n      \"description\": \"Nested structure of ingredients used in the product\",\n      \"additionalProperties\": false,\n      \"properties\": {\n        \"text\": {\n          \"type\": \"string\",\n          \"description\": \"Name of the ingredient. Do not add more than one ingredient in the text field. For example, text can be 'Basmati Rice' or 'Biryani Paste' or 'Whole Spices' or 'Mango' or 'Chocolate' etc. and not 'Basmati Rice, Biryani Paste, Whole Spices, Mango, Chocolate'.\"\n        },\n        \"amount\": {\n          \"type\": \"number\",\n          \"description\": \"Total amount of all ingredients combined. Guesstimate if not mentioned. Do not let this be null and provide your best guess.\"\n        },\n        \"amountUOM\": {\n          \"type\": \"string\",\n          \"description\": \"Unit of measurement for the total amount. Guesstimate if not mentioned. Do not let this be null and provide your best guess.\"\n        },\n        \"dietary_preference\": {\n          \"type\": \"object\",\n          \"description\": \"Dietary preferences of the combined ingredients\",\n          \"properties\": {\n            \"Vegan\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a vegan diet\"\n            },\n            \"Vegetarian\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a vegetarian diet\"\n            },\n            \"Pescatarian\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a pescatarian diet\"\n            },\n            \"WhiteMeatOnly\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain only white meat\"\n            },\n            \"KetoFriendly\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a ketogenic diet\"\n            },\n            \"LowFodmap\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a low FODMAP diet\"\n            }\n          }\n        },\n        \"allergens\": {\n          \"type\": \"object\",\n          \"description\": \"Allergens present in the combined ingredients\",\n          \"properties\": {\n            \"Sugar\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain sugar\"\n            },\n            \"Celery\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain celery\"\n            },\n            \"Gluten\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain gluten\"\n            },\n            \"Crustaceans\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain crustaceans\"\n            },\n            \"Eggs\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain eggs\"\n            },\n            \"Fish\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain fish\"\n            },\n            \"Lupin\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain lupin\"\n            },\n            \"Milk\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain milk\"\n            },\n            \"Peanuts\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain peanuts\"\n            },\n            \"Sesame\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain sesame\"\n            },\n            \"TreeNuts\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain tree nuts\"\n            }\n          }\n        },\n        \"religious_labels\": {\n          \"type\": \"object\",\n          \"description\": \"Religious labels present in the combined ingredients\",\n          \"properties\": {\n            \"Halal\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are Halal certified\"\n            },\n            \"Kosher\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are Kosher certified\"\n            },\n            \"Hindu\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for Hindu dietary requirements\"\n            },\n            \"Jain\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for Jain dietary requirements\"\n            }\n          }\n        },\n        \"food_safety_labels\": {\n          \"type\": \"object\",\n          \"description\": \"Food safety labels present in the combined ingredients\",\n          \"properties\": {\n            \"GMO\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain GMO components\"\n            },\n            \"NoGMO\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are GMO-free\"\n            },\n            \"Hormones\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain hormones\"\n            },\n            \"Carcinogenic\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are potentially carcinogenic\"\n            },\n            \"Organic\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are organic\"\n            },\n            \"ProductRecalls\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients have been subject to product recalls\"\n            }\n          }\n        },\n        \"sustainability_labels\": {\n          \"type\": \"object\",\n          \"description\": \"Sustainability labels present in the combined ingredients\",\n          \"properties\": {\n            \"Recycled\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are made from recycled materials\"\n            },\n            \"AnimalWelfare\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients meet animal welfare standards\"\n            },\n            \"OrganicPositioning\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are positioned as organic\"\n            },\n            \"PlantBased\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are plant-based\"\n            },\n            \"SocialResponsibility\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients meet social responsibility standards\"\n            },\n            \"SustainablePackaging\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients use sustainable packaging\"\n            }\n          }\n        },\n        \"subIngredients\": {\n          \"type\": \"array\",\n          \"description\": \"Any ingredients nested inside this one\",\n          \"items\": {\n            \"$ref\": \"#/definitions/Ingredient\"\n          }\n        },\n        \"source\": {\n          \"type\": \"string\",\n          \"enum\": [\n            \"Amazon Images\",\n            \"Product Scrape\",\n            \"ScrapingQualificationAI\"\n          ],\n          \"description\": \"Source of this ingredient information\"\n        }\n      },\n      \"required\": [\n        \"text\",\n        \"amount\",\n        \"amountUOM\",\n        \"dietary_preference\",\n        \"allergens\",\n        \"religious_labels\",\n        \"food_safety_labels\",\n        \"sustainability_labels\",\n        \"source\"\n      ]\n    }\n  },\n  \"required\": [\n    \"generalData\",\n    \"servingSize\",\n    \"nutritionalInformation\",\n    \"ingredients\",\n    \"claims\",\n    \"NPI 2.0 Food Packages - Allergens & Intolerances\",\n    \"cleanLabel\",\n    \"additionalInfo\"\n  ]\n}\n\nProduct details:\n$PRODUCT_DATA\n{\n  \"g\": 500,\n  \"url\": \"https://www.amazon.com/dp/B0BRGHLXHD\",\n  \"asin\": \"B0BRGHLXHD\",\n  \"size\": \"5.5 Ounce (Pack of 2)\",\n  \"type\": \"COFFEE\",\n  \"brand\": \"RUUFE\",\n  \"color\": null,\n  \"isB2B\": false,\n  \"isSNS\": false,\n  \"model\": null,\n  \"stats\": null,\n  \"title\": \"Matiz Colombian Light Coffee (2 Pack) Matiz Cafe Colombiano Suave roasted and ground coffee Matiz Suave\",\n  \"author\": null,\n  \"coupon\": null,\n  \"format\": null,\n  \"images\": [\n    {\n      \"m\": \"51h7aTHB51L.jpg\",\n      \"mH\": 500,\n      \"mW\": 500\n    },\n    {\n      \"m\": \"41IEemPPiQL.jpg\",\n      \"mH\": 500,\n      \"mW\": 500\n    },\n    {\n      \"m\": \"51eXNUQDwML.jpg\",\n      \"mH\": 500,\n      \"mW\": 500\n    },\n    {\n      \"m\": \"51NaxHcy7iL.jpg\",\n      \"mH\": 500,\n      \"mW\": 500\n    },\n    {\n      \"m\": \"41szrlvu-rL.jpg\",\n      \"mH\": 500,\n      \"mW\": 500\n    },\n    {\n      \"m\": \"61C5itIkxDL.jpg\",\n      \"mH\": 500,\n      \"mW\": 500\n    }\n  ],\n  \"offers\": null,\n  \"binding\": null,\n  \"eanList\": [\n    \"7706512558604\"\n  ],\n  \"edition\": null,\n  \"fbaFees\": null,\n  \"upcList\": null,\n  \"urlSlug\": \"Colombian-Coffee-Colombiano-roasted-ground\",\n  \"domainId\": 1,\n  \"features\": [\n    \"QUALITY: Premium 100% Colombian Coffee.\",\n    \"FLAVOR: Light\",\n    \"ROAST LEVEL: Roast Level: Light\",\n    \"HOW TO PREPARE: It can be diluted in milk or hot or cold water.\",\n    \"100% COLOMBIAN ARABICA COFFEE: Pure Arabica Colombian coffee. No blends. Features a medium roast & grind, which produces a smooth balanced body with notes of sweetness.\",\n    \"PRIME CLIMATE: The Andes Green Mountains provide the perfect recipe of elevation, soil, and climate for our coffee's globally recognized quality.\",\n    \"BEHIND THE BEANS: This coffee is sourced directly from family farms who take pride in their meticulously hand-picked coffee beans. Harvested at their peak, our roasted coffee beans are pristinely washed and sundried to perfection\"\n  ],\n  \"itemForm\": \"Powder\",\n  \"imagesCSV\": \"51h7aTHB51L.jpg,41IEemPPiQL.jpg,51eXNUQDwML.jpg,51NaxHcy7iL.jpg,41szrlvu-rL.jpg,61C5itIkxDL.jpg\",\n  \"itemWidth\": 0,\n  \"languages\": null,\n  \"launchpad\": false,\n  \"unitCount\": {\n    \"unitType\": \"Ounce\",\n    \"unitValue\": 11.0,\n    \"eachUnitCount\": 1.0\n  },\n  \"hasReviews\": false,\n  \"itemHeight\": 0,\n  \"itemLength\": 0,\n  \"itemWeight\": 0,\n  \"lastUpdate\": 7534854,\n  \"parentAsin\": null,\n  \"partNumber\": null,\n  \"promotions\": null,\n  \"description\": \"Premium 100% Colombian Coffee. Light coffee that has been subjected to a coffee dehydration process at low temperatures that allow the aroma and flavor notes to stand out. It can be diluted in milk or hot or cold water. Manufacturer : Matiz Item Form: Ground Brand: Matiz Flavor: Intense Caffeine Content: Yes Roast Level: Medium_roast Organic, local, global and fair trade options available\",\n  \"listedSince\": 6128524,\n  \"productType\": 0,\n  \"releaseDate\": -1,\n  \"categoryTree\": [\n    {\n      \"name\": \"Grocery & Gourmet Food\",\n      \"catId\": 16310101\n    },\n    {\n      \"name\": \"Beverages\",\n      \"catId\": 16310231\n    },\n    {\n      \"name\": \"Coffee\",\n      \"catId\": 16318031\n    },\n    {\n      \"name\": \"Instant Coffee\",\n      \"catId\": 2251594011\n    }\n  ],\n  \"manufacturer\": \"Matiz\",\n  \"packageWidth\": 0,\n  \"productGroup\": \"Grocery\",\n  \"rootCategory\": 16310101,\n  \"variationCSV\": null,\n  \"brandStoreUrl\": \"/stores/RUUFE/page/E7C1E31F-8174-45D6-93E6-DEF59FD23571\",\n  \"newPriceIsMAP\": false,\n  \"numberOfItems\": 2,\n  \"numberOfPages\": -1,\n  \"packageHeight\": 0,\n  \"packageLength\": 0,\n  \"packageWeight\": 0,\n  \"trackingSince\": 6321850,\n  \"brandStoreName\": \"RUUFE\",\n  \"ebayListingIds\": null,\n  \"isAdultProduct\": false,\n  \"isRedirectASIN\": false,\n  \"lastEbayUpdate\": 0,\n  \"isHeatSensitive\": false,\n  \"itemTypeKeyword\": \"instant-coffee\",\n  \"lastPriceChange\": 7419718,\n  \"liveOffersOrder\": null,\n  \"packageQuantity\": -1,\n  \"publicationDate\": -1,\n  \"lastRatingUpdate\": 7530588,\n  \"offersSuccessful\": false,\n  \"brandStoreUrlName\": \"RUUFE\",\n  \"availabilityAmazon\": -1,\n  \"salesRankReference\": 16310101,\n  \"websiteDisplayGroup\": \"grocery_display_on_website\",\n  \"isEligibleForTradeIn\": false,\n  \"buyBoxSellerIdHistory\": null,\n  \"websiteDisplayGroupName\": \"Grocery\",\n  \"frequentlyBoughtTogether\": [\n    \"B000LXB9TC\",\n    \"B00AYLWSOQ\"\n  ],\n  \"buyBoxEligibleOfferCounts\": [\n    0,\n    1,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0\n  ],\n  \"isEligibleForSuperSaverShipping\": false\n}\n\nScraped Data:\n$SCRAPED_DATA\n![](https://fls-\nna.amazon.com/1/batch/1/OP/ATVPDKIKX0DER:138-5110418-8151749:4EKYGM2V36EZJDZEZTFS$uedata=s:%2Frd%2Fuedata%3Fstaticb%26id%3D4EKYGM2V36EZJDZEZTFS:0)\n![](https://m.media-amazon.com/images/G/01/gno/sprites/nav-sprite-\nglobal-1x-reorg-privacy._CB546805360_.png) Shortcuts menu\n\n## Skip to\n\n  * [ Main content ](https://www.amazon.com/dp/B0BRGHLXHD#skippedLink)\n  * [ About this item ](https://www.amazon.com/dp/B0BRGHLXHD#featurebullets_feature_div)\n  * [ About this item ](https://www.amazon.com/dp/B0BRGHLXHD#nic-po-expander-heading)\n  * [ About this item ](https://www.amazon.com/dp/B0BRGHLXHD#productFactsDesktopExpander)\n  * [ Buying options ](https://www.amazon.com/dp/B0BRGHLXHD#buybox)\n  * [ Compare with similar items ](https://www.amazon.com/dp/B0BRGHLXHD#product-comparison_feature_div)\n  * [ Videos ](https://www.amazon.com/dp/B0BRGHLXHD#va-related-videos-widget_feature_div)\n  * [ Reviews ](https://www.amazon.com/dp/B0BRGHLXHD#customerReviews)\n\n* * *\n##  Keyboard shortcuts\n\n  * [ Search alt + / ](https://www.amazon.com/dp/B0BRGHLXHD#twotabsearchtextbox)\n  * [ Cart shift + alt + C ](https://www.amazon.com/gp/cart/view.html/?ref_=nav_assist)\n  * [ Home shift + alt + H ](https://www.amazon.com/?ref_=nav_assist)\n  * [ Orders shift + alt + O ](https://www.amazon.com/gp/css/order-history/?ref_=nav_assist)\n  * Add to cart\nshift + alt + K\n\n  * Open/close shortcuts menu\nshift + alt + Z\n\nTo move between items, use your keyboard's up or down arrows.\n\n[ .us ](https://www.amazon.com/ref=nav_logo)\n\n[ Deliver to  India  ](https://www.amazon.com/dp/B0BRGHLXHD)\n\nAll\n\nSelect the department you want to search in All Departments Arts & Crafts\nAutomotive Baby Beauty & Personal Care Books Boys' Fashion Computers Deals\nDigital Music Electronics Girls' Fashion Health & Household Home & Kitchen\nIndustrial & Scientific Kindle Store Luggage Men's Fashion Movies & TV Music,\nCDs & Vinyl Pet Supplies Prime Video Software Sports & Outdoors Tools & Home\nImprovement Toys & Games Video Games Women's Fashion\n\nSearch Amazon\n\n[ EN ](https://www.amazon.com/customer-\npreferences/edit?ie=UTF8&preferencesReturnUrl=%2F&ref_=topnav_lang_ais)\n\n[ Hello, sign in Account & Lists\n](https://www.amazon.com/ap/signin?openid.pape.max_auth_age=0&openid.return_to=https%3A%2F%2Fwww.amazon.com%2Fdp%2FB0BRGHLXHD%2F%3F_encoding%3DUTF8%26ref_%3Dnav_ya_signin&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.assoc_handle=usflex&openid.mode=checkid_setup&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0)\n\n[ Returns & Orders ](https://www.amazon.com/gp/css/order-\nhistory?ref_=nav_orders_first) [ 0 Cart\n](https://www.amazon.com/gp/cart/view.html?ref_=nav_cart)\n\n[ All ](javascript:%20void\\(0\\))\n\n  * [Today's Deals](https://www.amazon.com/gp/goldbox?ref_=nav_cs_gb)\n  * [Registry](https://www.amazon.com/gp/browse.html?node=***********&ref_=nav_cs_registry)\n  * [Prime Video](https://www.amazon.com/Amazon-Video/b/?ie=UTF8&node=**********&ref_=nav_cs_prime_video)\n  * [Gift Cards](https://www.amazon.com/gift-cards/b/?ie=UTF8&node=2238192011&ref_=nav_cs_gc)\n  * [Customer Service](https://www.amazon.com/gp/help/customer/display.html?nodeId=508510&ref_=nav_cs_customerservice)\n  * [Sell](https://www.amazon.com/b/?_encoding=UTF8&ld=AZUSSOA-sell&node=12766669011&ref_=nav_cs_sell)\n[Disability Customer\nSupport](https://www.amazon.com/gp/help/customer/accessibility)\n\n[ Grocery  ](https://www.amazon.com/grocery-breakfast-foods-snacks-\norganic/b/?ie=UTF8&node=16310101&ref_=topnav_storetab_grocery_sn_fo) [ Deals\n](https://www.amazon.com/Sales-\nGrocery/b/?ie=UTF8&node=52129011&ref_=sv_grocery_sn_fo_1) [ Snacks\n](https://www.amazon.com/Snacks-Chips-Cookies-Gum-Gluten-\nFree/b/?ie=UTF8&node=16322721&ref_=sv_grocery_sn_fo_2) [ Breakfast\n](https://www.amazon.com/Breakfast-Foods-\nGrocery/b/?ie=UTF8&node=16310251&ref_=sv_grocery_sn_fo_3) [ Warm Beverages\n](https://www.amazon.com/b/?ie=UTF8&node=16521305011&ref_=sv_grocery_sn_fo_4) [\nCold Beverages  ](https://www.amazon.com/Cold-\nBeverages/b/?ie=UTF8&node=14808787011&ref_=sv_grocery_sn_fo_5) [ Cooking Staples\n](https://www.amazon.com/Canned-Jarred-Packaged-\nFoods/b/?ie=UTF8&node=6464939011&ref_=sv_grocery_sn_fo_6) [ Baby Food\n](https://www.amazon.com/Baby-Food-Formula-\nPouches/b/?ie=UTF8&node=16323111&ref_=sv_grocery_sn_fo_7) [ Candy & Chocolate\n](https://www.amazon.com/Candy-\nChocolate/b/?ie=UTF8&node=16322461&ref_=sv_grocery_sn_fo_8) [ Subscribe & Save\n](https://www.amazon.com/Subscribe-\nSave/b/?ie=UTF8&node=5856181011&ref_=sv_grocery_sn_fo_9) [ International Foods\n](https://www.amazon.com/b/?ie=UTF8&node=17428419011&ref_=sv_grocery_sn_fo_10) [\nSNAP-eligible Groceries  ](https://www.amazon.com/snap-\nebt/b/?ie=UTF8&node=19097785011&ref_=sv_grocery_sn_fo_11)\n\nAmazon.com : Matiz Colombian Light Coffee (2 Pack) Matiz Cafe Colombiano Suave\nroasted and ground coffee Matiz Suave : Grocery & Gourmet Food\n\nSponsored\n\n  * [Grocery & Gourmet Food](https://www.amazon.com/grocery-breakfast-foods-snacks-organic/b/ref=dp_bc_1?ie=UTF8&node=16310101)\n  * ›\n  * [Beverages](https://www.amazon.com/Beverages-Coffee-Tea-Water-Grocery/b/ref=dp_bc_2?ie=UTF8&node=16310231)\n  * ›\n  * [Coffee](https://www.amazon.com/Gourmet-Coffee-Tea-Cocoa-k-cup/b/ref=dp_bc_3?ie=UTF8&node=16318031)\n  * ›\n  * [Instant Coffee](https://www.amazon.com/Instant-Coffee/b/ref=dp_bc_4?ie=UTF8&node=2251594011)\n\nNo featured offers available  \n[ Learn more ](javascript:void\\(0\\))\n\nNo featured offers available\n\nWe feature offers with an Add to Cart button when an offer meets our high\nstandards for:\n\n  * Quality Price,\n  * Reliable delivery option, and\n  * Seller who offers good customer service\n\n“No featured offers available” means no offers currently meet all of these\nexpectations. Select See All Buying Options to shop available offers.\n\nThis item cannot be shipped to your selected delivery location. Please choose a\ndifferent delivery location.  \n[ Deliver to India ](https://www.amazon.com/dp/B0BRGHLXHD)\n\n* * *\n[ Add to List\n](https://www.amazon.com/ap/signin?openid.return_to=https%3A%2F%2Fwww.amazon.com%2Fgp%2Faw%2Fd%2FB0BRGHLXHD&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.assoc_handle=usflex&openid.mode=checkid_setup&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0&\n\"Add to List\")\n\nAdded to\n\n[ ](https://www.amazon.com/gp/registry/wishlist/)\n\nUnable to add item to List. Please try again.\n\n###  Sorry, there was a problem.\n\nThere was an error retrieving your Wish Lists. Please try again.\n\n###  Sorry, there was a problem.\n\nList unavailable.\n\n[](javascript:void\\(0\\) \"Share\")\n\nSponsored\n\n  * ![](https://m.media-amazon.com/images/G/01/HomeCustomProduct/360_icon_73x73v2._CB485971279_SX38_SY50_CR,0,0,38,50_FMpng_RI_.png)\n  *   * ![](https://m.media-amazon.com/images/I/51h7aTHB51L._SX38_SY50_CR,0,0,38,50_.jpg) 6+\n  * ![](https://m.media-amazon.com/images/I/41IEemPPiQL._SX38_SY50_CR,0,0,38,50_.jpg) 5+\n  * ![](https://m.media-amazon.com/images/I/51eXNUQDwML._SX38_SY50_CR,0,0,38,50_.jpg) 4+\n  * ![](https://m.media-amazon.com/images/I/51NaxHcy7iL._SX38_SY50_CR,0,0,38,50_.jpg) 3+\n  * ![](https://m.media-amazon.com/images/I/41szrlvu-rL._SX38_SY50_CR,0,0,38,50_.jpg) 2+\n  * ![](https://m.media-amazon.com/images/I/61C5itIkxDL._SX38_SY50_CR,0,0,38,50_.jpg) 1+\n  * ![](https://m.media-amazon.com/images/I/41PQqoz2xML.SX38_SY50_CR,0,0,38,50_BG85,85,85_BR-120_PKdp-play-icon-overlay__.jpg) VIDEO\n\n[](javascript:void\\(0\\))\n\nThe video showcases the product in use.The video guides you through product\nsetup.The video compares multiple products.The video shows the product being\nunpacked.\n\n![](https://images-na.ssl-images-amazon.com/images/G/01/x-locale/common/grey-\npixel.gif)\n\nmatiz un sabor colombianoRuufe\n\n#### Image Unavailable\n\nImage not available for  \nColor:\n\n  * ![Matiz Colombian Light Coffee \\(2 Pack\\) Matiz Cafe Colombiano Suave roasted and ground coffee Matiz Suave](https://m.media-amazon.com/images/I/61-5mo3UrlL._SX425_PIbundle-2,TopRight,0,0_AA425SH20_.jpg)\n  * To view this video download [ Flash Player ](https://get.adobe.com/flashplayer)\n\nRoll over image to zoom in\n\n  * [ VIDEOS ](https://www.amazon.com/dp/B0BRGHLXHD)\n  * [ 360° VIEW ](https://www.amazon.com/dp/B0BRGHLXHD)\n  * [ IMAGES ](https://www.amazon.com/dp/B0BRGHLXHD)\n  * [ ](https://www.amazon.com/dp/B0BRGHLXHD)\n\n[](javascript:void\\(0\\))\n\nThe video showcases the product in use.The video guides you through product\nsetup.The video compares multiple products.The video shows the product being\nunpacked.\n\n[](javascript:void\\(0\\))\n\n  * [](javascript:void\\(0\\))\n  * [](javascript:void\\(0\\))\n  * [](javascript:void\\(0\\))\n  * [](javascript:void\\(0\\))\n\nCustomer Review: matiz un sabor colombiano\n\n[See full review](https://www.amazon.com/dp/B0BRGHLXHD)\n\nRuufe\n\n[ Ruufe ](https://www.amazon.com/dp/B0BRGHLXHD) •  Verified Purchase\n\nEarns Commissions\n\n[ Ruufe ](https://www.amazon.com/dp/B0BRGHLXHD) •  Verified Purchase\n\nEarns Commissions [](https://www.amazon.com/shop/info)\n\n![](https://m.media-amazon.com/images/S/sash//pLB3SkYb3bHZzHQ.svg)\n\n![](https://m.media-amazon.com/images/S/sash//swRPyHOrgnz358_.svg)\n\n#  Matiz Colombian Light Coffee (2 Pack) Matiz Cafe Colombiano Suave roasted and\nground coffee Matiz Suave\n\n[Visit the RUUFE\nStore](https://www.amazon.com/stores/RUUFE/page/E7C1E31F-8174-45D6-93E6-DEF59FD23571?lp_asin=B0BRGHLXHD&ref_=ast_bln&store_ref=bl_ast_dp_brandLogo_sto)\n\n[ Search this page  ](https://www.amazon.com/dp/B0BRGHLXHD#Ask)\n\n* * *\nThis item cannot be shipped to your selected delivery location. Please choose a\ndifferent delivery location.  \n\nDiet type\n\nPlant Based\n\n* * *\n[ About this item ](javascript:void\\(0\\))\n\nBrand |  RUUFE  \n---|---  \nItem Form |  Powder  \nFlavor |  Light  \nCaffeine Content Description |  Caffeinated  \nRoast Level |  Medium Roast  \n* * *\n#  About this item\n\n  * QUALITY: Premium 100% Colombian Coffee. \n  * FLAVOR: Light \n  * ROAST LEVEL: Roast Level: Light \n  * HOW TO PREPARE: It can be diluted in milk or hot or cold water. \n  * 100% COLOMBIAN ARABICA COFFEE: Pure Arabica Colombian coffee. No blends. Features a medium roast & grind, which produces a smooth balanced body with notes of sweetness. \n  * PRIME CLIMATE: The Andes Green Mountains provide the perfect recipe of elevation, soil, and climate for our coffee's globally recognized quality. \n  * BEHIND THE BEANS: This coffee is sourced directly from family farms who take pride in their meticulously hand-picked coffee beans. Harvested at their peak, our roasted coffee beans are pristinely washed and sundried to perfection \n\n› [ See more product details\n](https://www.amazon.com/dp/B0BRGHLXHD#productDetails)\n\n[](https://www.amazon.com/dp/B0BRGHLXHD)\n\n* * *\nSponsored\n\n* * *\n## Products related to this item\n\nPage 1 of 1[Start over](https://www.amazon.com/dp/B0BRGHLXHD)Page 1 of 1\n\n[ Sponsored  ](https://www.amazon.com/dp/B0BRGHLXHD#sp_detail_feedbackForm)\n\n[_Previous page of related Sponsored\nProducts_](https://www.amazon.com/dp/B0BRGHLXHD)\n\n  1. [Feedback](javascript:void\\(0\\))\n[ ![NüSpira - Latte Mushroom Coffee - Premium Blend of 6 Adaptogen Mushrooms for\nVitality, Brain Clarity, Gut Health & Immune Support - 12oz\nPack](https://m.media-\namazon.com/images/I/41POeDdHf2L._AC_UF480,480_SR480,480_.jpg) NüSpira - Latte\nMushroom Coffee - Premium Blend of 6 Adaptogen Mushrooms…\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0DK24ZSHD%2Fref%3Dsspa_dk_detail_0%3Fpsc%3D1%26pd_rd_i%3DB0DK24ZSHD%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw \"NüSpira -\nLatte Mushroom Coffee - Premium Blend of 6 Adaptogen Mushrooms for Vitality,\nBrain Clarity, Gut Health & Immune Support - 12oz Pack\")\n\n[ 28\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0DK24ZSHD%2Fref%3Dsspa_dk_detail_0%3Fpsc%3D1%26pd_rd_i%3DB0DK24ZSHD%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw#customerReviews)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0DK24ZSHD%2Fref%3Dsspa_dk_detail_0%3Fpsc%3D1%26pd_rd_i%3DB0DK24ZSHD%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n  2. [Feedback](javascript:void\\(0\\))\n[ ![Sello Rojo, Tradicional Single Serve Coffee K-Cup Pods, 12 ct, Pack of\n6](https://m.media-amazon.com/images/I/41K4YUPSsEL._AC_UF480,480_SR480,480_.jpg)\nSello Rojo, Tradicional Single Serve Coffee K-Cup Pods, 12 ct, Pack of 6\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0CP2NLTNB%2Fref%3Dsspa_dk_detail_1%3Fpsc%3D1%26pd_rd_i%3DB0CP2NLTNB%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw \"Sello Rojo,\nTradicional Single Serve Coffee K-Cup Pods, 12 ct, Pack of 6\")\n\n[ 32\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0CP2NLTNB%2Fref%3Dsspa_dk_detail_1%3Fpsc%3D1%26pd_rd_i%3DB0CP2NLTNB%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw#customerReviews)\n\n[ _ Amazon's Choice _\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0CP2NLTNB%2Fref%3Dsspa_dk_detail_1%3Fpsc%3D1%26pd_rd_i%3DB0CP2NLTNB%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n[ $51.71$51.71($0.72$0.72 / Count)\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0CP2NLTNB%2Fref%3Dsspa_dk_detail_1%3Fpsc%3D1%26pd_rd_i%3DB0CP2NLTNB%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0CP2NLTNB%2Fref%3Dsspa_dk_detail_1%3Fpsc%3D1%26pd_rd_i%3DB0CP2NLTNB%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n  3. [Feedback](javascript:void\\(0\\))\n[ ![Mushroom Coffee, 90 Servings Organic Mushrooms Instant Coffea, Great Tasting\nColombian Keto Coffee, 7 Superfood Mishrooms Lions Mane, Chaga, Reishi,\nCordyceps, Maitake, Shiitake, and Turkey Tail](https://m.media-\namazon.com/images/I/51Ab8OgfKZL._AC_UF480,480_SR480,480_.jpg) Mushroom Coffee,\n90 Servings Organic Mushrooms Instant Coffea, Great Tasting…\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0CLT4SLGD%2Fref%3Dsspa_dk_detail_2%3Fpsc%3D1%26pd_rd_i%3DB0CLT4SLGD%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw \"Mushroom\nCoffee, 90 Servings Organic Mushrooms Instant Coffea, Great Tasting Colombian\nKeto Coffee, 7 Superfood Mishrooms Lions Mane, Chaga, Reishi, Cordyceps,\nMaitake, Shiitake, and Turkey Tail\")\n\n[ 866\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0CLT4SLGD%2Fref%3Dsspa_dk_detail_2%3Fpsc%3D1%26pd_rd_i%3DB0CLT4SLGD%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw#customerReviews)\n\n[ _ Amazon's Choice _\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0CLT4SLGD%2Fref%3Dsspa_dk_detail_2%3Fpsc%3D1%26pd_rd_i%3DB0CLT4SLGD%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0CLT4SLGD%2Fref%3Dsspa_dk_detail_2%3Fpsc%3D1%26pd_rd_i%3DB0CLT4SLGD%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n  4. [Feedback](javascript:void\\(0\\))\n[ ![Blackout Coffee Colombian Arabica Instant Coffee, Strong Aromatic, Fresh\nRoasted in the USA, Premium Coffee Single Serve Packets, 32\nCount](https://m.media-\namazon.com/images/I/51+QII54m4L._AC_UF480,480_SR480,480_.jpg) Blackout Coffee\nColombian Arabica Instant Coffee, Strong Aromatic, Fresh…\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0D1VN9691%2Fref%3Dsspa_dk_detail_3%3Fpsc%3D1%26pd_rd_i%3DB0D1VN9691%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw \"Blackout\nCoffee Colombian Arabica Instant Coffee, Strong Aromatic, Fresh Roasted in the\nUSA, Premium Coffee Single Serve Packets, 32 Count\")\n\n[ 22\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0D1VN9691%2Fref%3Dsspa_dk_detail_3%3Fpsc%3D1%26pd_rd_i%3DB0D1VN9691%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw#customerReviews)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0D1VN9691%2Fref%3Dsspa_dk_detail_3%3Fpsc%3D1%26pd_rd_i%3DB0D1VN9691%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n  5. [Feedback](javascript:void\\(0\\))\n[ ![Jim’s Organic Coffee – Hazelnut, All Natural Flavored Blend – Light Roast,\nGround Coffee, 12 oz Bag](https://m.media-\namazon.com/images/I/41QPSCvDcQL._AC_UF480,480_SR480,480_.jpg) Jim’s Organic\nCoffee – Hazelnut, All Natural Flavored Blend – Light Roast, Ground C...\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB00A282ZEE%2Fref%3Dsspa_dk_detail_4%3Fpsc%3D1%26pd_rd_i%3DB00A282ZEE%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw \"Jim’s Organic\nCoffee – Hazelnut, All Natural Flavored Blend – Light Roast, Ground Coffee, 12\noz Bag\")\n\n[ 1,840\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB00A282ZEE%2Fref%3Dsspa_dk_detail_4%3Fpsc%3D1%26pd_rd_i%3DB00A282ZEE%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw#customerReviews)\n\n[ $17.99$17.99($1.50$1.50 / Ounce)\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB00A282ZEE%2Fref%3Dsspa_dk_detail_4%3Fpsc%3D1%26pd_rd_i%3DB00A282ZEE%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB00A282ZEE%2Fref%3Dsspa_dk_detail_4%3Fpsc%3D1%26pd_rd_i%3DB00A282ZEE%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n[ ![](https://m.media-amazon.com/images/I/216-OX9rBaL.png) Climate Pledge\nFriendly\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB00A282ZEE%2Fref%3Dsspa_dk_detail_4%3Fpsc%3D1%26pd_rd_i%3DB00A282ZEE%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n  6. [Feedback](javascript:void\\(0\\))\n[ ![Rowdy Mushroom Coffee - Instant Coffee that Chills you Out - 6 Functional\nMushroom Blend, Ready in Seconds - Drink Hot or Cold - Lasts 1+ Months\n\\(6oz\\)](https://m.media-\namazon.com/images/I/41d0QB+qb2L._AC_UF480,480_SR480,480_.jpg) Rowdy Mushroom\nCoffee - Instant Coffee that Chills you Out - 6 Functional Mushroom ...\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxODAwNzUyNTYyMDA3MzA5OjE3NDcwNjcxODQ6c3BfZGV0YWlsOjMwMDQxMzU3NDc5MTgwMjo6Ojo&url=%2Fdp%2FB0CV5PWT6C%2Fref%3Dsspa_dk_detail_5%3Fpsc%3D1%26pd_rd_i%3DB0CV5PWT6C%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw \"Rowdy\nMushroom Coffee - Instant Coffee that Chills you Out - 6 Functional Mushroom\nBlend, Ready in Seconds - Drink Hot or Cold - Lasts 1+ Months \\(6oz\\)\")\n\n[ 58\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxODAwNzUyNTYyMDA3MzA5OjE3NDcwNjcxODQ6c3BfZGV0YWlsOjMwMDQxMzU3NDc5MTgwMjo6Ojo&url=%2Fdp%2FB0CV5PWT6C%2Fref%3Dsspa_dk_detail_5%3Fpsc%3D1%26pd_rd_i%3DB0CV5PWT6C%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw#customerReviews)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxODAwNzUyNTYyMDA3MzA5OjE3NDcwNjcxODQ6c3BfZGV0YWlsOjMwMDQxMzU3NDc5MTgwMjo6Ojo&url=%2Fdp%2FB0CV5PWT6C%2Fref%3Dsspa_dk_detail_5%3Fpsc%3D1%26pd_rd_i%3DB0CV5PWT6C%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n  7. [Feedback](javascript:void\\(0\\))\n[ ![Casitika Colombian Gifts. Have No Fear Colombia Coffee Mug. Novelty Cups\nWith Flag For Proud Husband Or Wife. \\(11 oz Black\nHandle/Rim\\)](https://m.media-\namazon.com/images/I/41gQV1KKQ7L._AC_UF480,480_SR480,480_.jpg) Casitika Colombian\nGifts. Have No Fear Colombia Coffee Mug. Novelty Cups With Flag ...\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxODAwNzUyNTYyMDA3MzA5OjE3NDcwNjcxODQ6c3BfZGV0YWlsOjIwMDE1MDkwMTU0MjM5ODo6Ojo&url=%2Fdp%2FB0BTR38M9C%2Fref%3Dsspa_dk_detail_6%3Fpsc%3D1%26pd_rd_i%3DB0BTR38M9C%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw \"Casitika\nColombian Gifts. Have No Fear Colombia Coffee Mug. Novelty Cups With Flag For\nProud Husband Or Wife. \\(11 oz Black Handle/Rim\\)\")\n\n[ 8\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxODAwNzUyNTYyMDA3MzA5OjE3NDcwNjcxODQ6c3BfZGV0YWlsOjIwMDE1MDkwMTU0MjM5ODo6Ojo&url=%2Fdp%2FB0BTR38M9C%2Fref%3Dsspa_dk_detail_6%3Fpsc%3D1%26pd_rd_i%3DB0BTR38M9C%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw#customerReviews)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxODAwNzUyNTYyMDA3MzA5OjE3NDcwNjcxODQ6c3BfZGV0YWlsOjIwMDE1MDkwMTU0MjM5ODo6Ojo&url=%2Fdp%2FB0BTR38M9C%2Fref%3Dsspa_dk_detail_6%3Fpsc%3D1%26pd_rd_i%3DB0BTR38M9C%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n[_Next page of related Sponsored\nProducts_](https://www.amazon.com/dp/B0BRGHLXHD)\n\n##  Product Description\n\nPremium 100% Colombian Coffee. Light coffee that has been subjected to a coffee\ndehydration process at low temperatures that allow the aroma and flavor notes to\nstand out. It can be diluted in milk or hot or cold water. Manufacturer : Matiz\nItem Form: Ground Brand: Matiz Flavor: Intense Caffeine Content: Yes Roast\nLevel: Medium_roast Organic, local, global and fair trade options available\n\n* * *\n## Product details\n\n  * Manufacturer ‏ : ‎  Matiz\n  * ASIN ‏ : ‎  B0BRGHLXHD\n  * Units ‏ : ‎  11.0 Ounce\n\n  * Best Sellers Rank:  #298,246 in Grocery & Gourmet Food ([See Top 100 in Grocery & Gourmet Food](https://www.amazon.com/gp/bestsellers/grocery/ref=pd_zg_ts_grocery)) \n    * #2,298 in [Instant Coffee](https://www.amazon.com/gp/bestsellers/grocery/2251594011/ref=pd_zg_hrsr_grocery)\n\nBrief content visible, double tap to read full content.\n\nFull content visible, double tap to read brief content.\n\n## Videos\n\nPage 1 of 1[Start Over](https://www.amazon.com/dp/B0BRGHLXHD)Page 1 of 1\n\n[_Previous page_](https://www.amazon.com/dp/B0BRGHLXHD)\n\n  1. #### Videos for this product\n[ ![Video Widget Card](https://images-na.ssl-images-\namazon.com/images/G/01/x-locale/common/grey-pixel.gif)![Video Widget\nCard](https://m.media-\namazon.com/images/I/41PQqoz2xML._CR1,0,638,360_SR342,193_.jpg) 0:20  Click to\nplay video\n](https://www.amazon.com/vdp/0a6e40ba54194528a33046f71e0700e5?ref=dp_vse_rvc_0)\n\n![Video Widget Video Title Section](https://images-na.ssl-images-\namazon.com/images/G/01/x-locale/common/grey-pixel.gif)\n\nmatiz un sabor colombiano\n\nRuufe\n\n[_Next page_](https://www.amazon.com/dp/B0BRGHLXHD)\n\n[Upload your\nvideo](https://www.amazon.com/creatorhub/video/upload?productASIN=B0BRGHLXHD&referringURL=ZHAvQjBCUkdITFhIRA%3D%3D&ref=RVSW)\n\n* * *\n## Important information\n\nLegal Disclaimer\n\nStatements regarding dietary supplements have not been evaluated by the FDA and\nare not intended to diagnose, treat, cure, or prevent any disease or health\ncondition.\n\n* * *\n## Products related to this item\n\nPage 1 of 1[Start over](https://www.amazon.com/dp/B0BRGHLXHD)Page 1 of 1\n\n[ Sponsored  ](https://www.amazon.com/dp/B0BRGHLXHD#sp_detail2_feedbackForm)\n\n[_Previous page of related Sponsored\nProducts_](https://www.amazon.com/dp/B0BRGHLXHD)\n\n  1. [Feedback](javascript:void\\(0\\))\n[ ![Sello Rojo, Tradicional Single Serve Coffee K-Cup Pods, 12\nct](https://m.media-\namazon.com/images/I/41K4YUPSsEL._AC_UF480,480_SR480,480_.jpg) Sello Rojo,\nTradicional Single Serve Coffee K-Cup Pods, 12 ct\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDAxMzQ4MTc4NjY3MDI6Ojo6&url=%2Fdp%2FB0CP2PSG7Z%2Fref%3Dsspa_dk_detail_0%3Fpsc%3D1%26pd_rd_i%3DB0CP2PSG7Z%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy \"Sello Rojo,\nTradicional Single Serve Coffee K-Cup Pods, 12 ct\")\n\n[ 32\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDAxMzQ4MTc4NjY3MDI6Ojo6&url=%2Fdp%2FB0CP2PSG7Z%2Fref%3Dsspa_dk_detail_0%3Fpsc%3D1%26pd_rd_i%3DB0CP2PSG7Z%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy#customerReviews)\n\n[ _ Amazon's Choice _\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDAxMzQ4MTc4NjY3MDI6Ojo6&url=%2Fdp%2FB0CP2PSG7Z%2Fref%3Dsspa_dk_detail_0%3Fpsc%3D1%26pd_rd_i%3DB0CP2PSG7Z%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n[ $8.99$8.99($0.75$0.75 / Count)\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDAxMzQ4MTc4NjY3MDI6Ojo6&url=%2Fdp%2FB0CP2PSG7Z%2Fref%3Dsspa_dk_detail_0%3Fpsc%3D1%26pd_rd_i%3DB0CP2PSG7Z%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDAxMzQ4MTc4NjY3MDI6Ojo6&url=%2Fdp%2FB0CP2PSG7Z%2Fref%3Dsspa_dk_detail_0%3Fpsc%3D1%26pd_rd_i%3DB0CP2PSG7Z%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n  2. [Feedback](javascript:void\\(0\\))\n[ ![Casitika Colombian Gifts. Have No Fear Colombia Coffee Mug. Novelty Cups\nWith Flag For Proud Husband Or Wife. \\(11 oz Black\nHandle/Rim\\)](https://m.media-\namazon.com/images/I/41gQV1KKQ7L._AC_UF480,480_SR480,480_.jpg) Casitika Colombian\nGifts. Have No Fear Colombia Coffee Mug. Novelty Cups With Flag ...\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjoyMDAxNTA5MDE1NDIzOTg6Ojo6&url=%2Fdp%2FB0BTR38M9C%2Fref%3Dsspa_dk_detail_1%3Fpsc%3D1%26pd_rd_i%3DB0BTR38M9C%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy \"Casitika\nColombian Gifts. Have No Fear Colombia Coffee Mug. Novelty Cups With Flag For\nProud Husband Or Wife. \\(11 oz Black Handle/Rim\\)\")\n\n[ 8\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjoyMDAxNTA5MDE1NDIzOTg6Ojo6&url=%2Fdp%2FB0BTR38M9C%2Fref%3Dsspa_dk_detail_1%3Fpsc%3D1%26pd_rd_i%3DB0BTR38M9C%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy#customerReviews)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjoyMDAxNTA5MDE1NDIzOTg6Ojo6&url=%2Fdp%2FB0BTR38M9C%2Fref%3Dsspa_dk_detail_1%3Fpsc%3D1%26pd_rd_i%3DB0BTR38M9C%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n  3. [Feedback](javascript:void\\(0\\))\n[ ![Rowdy Mushroom Coffee - Instant Coffee that Chills you Out - 6 Functional\nMushroom Blend, Ready in Seconds - Drink Hot or Cold - Lasts 1+ Months\n\\(6oz\\)](https://m.media-\namazon.com/images/I/41d0QB+qb2L._AC_UF480,480_SR480,480_.jpg) Rowdy Mushroom\nCoffee - Instant Coffee that Chills you Out - 6 Functional Mushroom ...\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA0MTM1NzQ3OTE4MDI6Ojo6&url=%2Fdp%2FB0CV5PWT6C%2Fref%3Dsspa_dk_detail_2%3Fpsc%3D1%26pd_rd_i%3DB0CV5PWT6C%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy \"Rowdy\nMushroom Coffee - Instant Coffee that Chills you Out - 6 Functional Mushroom\nBlend, Ready in Seconds - Drink Hot or Cold - Lasts 1+ Months \\(6oz\\)\")\n\n[ 58\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA0MTM1NzQ3OTE4MDI6Ojo6&url=%2Fdp%2FB0CV5PWT6C%2Fref%3Dsspa_dk_detail_2%3Fpsc%3D1%26pd_rd_i%3DB0CV5PWT6C%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy#customerReviews)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA0MTM1NzQ3OTE4MDI6Ojo6&url=%2Fdp%2FB0CV5PWT6C%2Fref%3Dsspa_dk_detail_2%3Fpsc%3D1%26pd_rd_i%3DB0CV5PWT6C%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n  4. [Feedback](javascript:void\\(0\\))\n[ ![Mushroom Coffee, Instant Organic Mushroom Coffee with Cordyceps, Reish, King\nTrumpet, Shitake, Turkey Tail, Lions Mane for Energy, Focus, Positive Mood &\nImmune Support \\(30 Servings\\)](https://m.media-\namazon.com/images/I/41A1Z0yyksL._AC_UF480,480_SR480,480_.jpg) Mushroom Coffee,\nInstant Organic Mushroom Coffee with Cordyceps, Reish,…\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA1MzU1MzM2MDMzMDI6Ojo6&url=%2Fdp%2FB0DJSKQRPM%2Fref%3Dsspa_dk_detail_3%3Fpsc%3D1%26pd_rd_i%3DB0DJSKQRPM%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy \"Mushroom\nCoffee, Instant Organic Mushroom Coffee with Cordyceps, Reish, King Trumpet,\nShitake, Turkey Tail, Lions Mane for Energy, Focus, Positive Mood & Immune\nSupport \\(30 Servings\\)\")\n\n[ 392\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA1MzU1MzM2MDMzMDI6Ojo6&url=%2Fdp%2FB0DJSKQRPM%2Fref%3Dsspa_dk_detail_3%3Fpsc%3D1%26pd_rd_i%3DB0DJSKQRPM%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy#customerReviews)\n\n[ _ Amazon's Choice _\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA1MzU1MzM2MDMzMDI6Ojo6&url=%2Fdp%2FB0DJSKQRPM%2Fref%3Dsspa_dk_detail_3%3Fpsc%3D1%26pd_rd_i%3DB0DJSKQRPM%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA1MzU1MzM2MDMzMDI6Ojo6&url=%2Fdp%2FB0DJSKQRPM%2Fref%3Dsspa_dk_detail_3%3Fpsc%3D1%26pd_rd_i%3DB0DJSKQRPM%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n  5. [Feedback](javascript:void\\(0\\))\n[ ![NüSpira - Latte Mushroom Coffee - Premium Blend of 6 Adaptogen Mushrooms for\nVitality, Brain Clarity, Gut Health & Immune Support - 12oz\nPack](https://m.media-\namazon.com/images/I/41POeDdHf2L._AC_UF480,480_SR480,480_.jpg) NüSpira - Latte\nMushroom Coffee - Premium Blend of 6 Adaptogen Mushrooms…\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA3MDQ0OTkxNzgwMDI6Ojo6&url=%2Fdp%2FB0DK24ZSHD%2Fref%3Dsspa_dk_detail_4%3Fpsc%3D1%26pd_rd_i%3DB0DK24ZSHD%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy \"NüSpira -\nLatte Mushroom Coffee - Premium Blend of 6 Adaptogen Mushrooms for Vitality,\nBrain Clarity, Gut Health & Immune Support - 12oz Pack\")\n\n[ 28\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA3MDQ0OTkxNzgwMDI6Ojo6&url=%2Fdp%2FB0DK24ZSHD%2Fref%3Dsspa_dk_detail_4%3Fpsc%3D1%26pd_rd_i%3DB0DK24ZSHD%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy#customerReviews)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA3MDQ0OTkxNzgwMDI6Ojo6&url=%2Fdp%2FB0DK24ZSHD%2Fref%3Dsspa_dk_detail_4%3Fpsc%3D1%26pd_rd_i%3DB0DK24ZSHD%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n  6. [Feedback](javascript:void\\(0\\))\n[ ![Mushroom Coffee, 90 Servings Organic Mushrooms Instant Coffea, Great Tasting\nColombian Keto Coffee, 7 Superfood Mishrooms Lions Mane, Chaga, Reishi,\nCordyceps, Maitake, Shiitake, and Turkey Tail](https://m.media-\namazon.com/images/I/51Ab8OgfKZL._AC_UF480,480_SR480,480_.jpg) Mushroom Coffee,\n90 Servings Organic Mushrooms Instant Coffea, Great Tasting…\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDAzMjA0NDU5MzMyMDI6Ojo6&url=%2Fdp%2FB0CLT4SLGD%2Fref%3Dsspa_dk_detail_5%3Fpsc%3D1%26pd_rd_i%3DB0CLT4SLGD%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy \"Mushroom\nCoffee, 90 Servings Organic Mushrooms Instant Coffea, Great Tasting Colombian\nKeto Coffee, 7 Superfood Mishrooms Lions Mane, Chaga, Reishi, Cordyceps,\nMaitake, Shiitake, and Turkey Tail\")\n\n[ 866\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDAzMjA0NDU5MzMyMDI6Ojo6&url=%2Fdp%2FB0CLT4SLGD%2Fref%3Dsspa_dk_detail_5%3Fpsc%3D1%26pd_rd_i%3DB0CLT4SLGD%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy#customerReviews)\n\n[ _ Amazon's Choice _\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDAzMjA0NDU5MzMyMDI6Ojo6&url=%2Fdp%2FB0CLT4SLGD%2Fref%3Dsspa_dk_detail_5%3Fpsc%3D1%26pd_rd_i%3DB0CLT4SLGD%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDAzMjA0NDU5MzMyMDI6Ojo6&url=%2Fdp%2FB0CLT4SLGD%2Fref%3Dsspa_dk_detail_5%3Fpsc%3D1%26pd_rd_i%3DB0CLT4SLGD%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n  7. [Feedback](javascript:void\\(0\\))\n[ ![Blackout Coffee Colombian Arabica Instant Coffee, Strong Aromatic, Fresh\nRoasted in the USA, Premium Coffee Single Serve Packets, 32\nCount](https://m.media-\namazon.com/images/I/51+QII54m4L._AC_UF480,480_SR480,480_.jpg) Blackout Coffee\nColombian Arabica Instant Coffee, Strong Aromatic, Fresh…\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA3Mzc1MDExMDUzMDI6Ojo6&url=%2Fdp%2FB0D1VN9691%2Fref%3Dsspa_dk_detail_6%3Fpsc%3D1%26pd_rd_i%3DB0D1VN9691%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy \"Blackout\nCoffee Colombian Arabica Instant Coffee, Strong Aromatic, Fresh Roasted in the\nUSA, Premium Coffee Single Serve Packets, 32 Count\")\n\n[ 22\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA3Mzc1MDExMDUzMDI6Ojo6&url=%2Fdp%2FB0D1VN9691%2Fref%3Dsspa_dk_detail_6%3Fpsc%3D1%26pd_rd_i%3DB0D1VN9691%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy#customerReviews)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA3Mzc1MDExMDUzMDI6Ojo6&url=%2Fdp%2FB0D1VN9691%2Fref%3Dsspa_dk_detail_6%3Fpsc%3D1%26pd_rd_i%3DB0D1VN9691%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n[_Next page of related Sponsored\nProducts_](https://www.amazon.com/dp/B0BRGHLXHD)\n\n* * *\nSponsored\n\n* * *\n## Customer reviews\n\n  * 5 star4 star3 star2 star1 star5 star\n0%0%0%0%0%0%\n\n  * 5 star4 star3 star2 star1 star4 star\n0%0%0%0%0%0%\n\n  * 5 star4 star3 star2 star1 star3 star\n0%0%0%0%0%0%\n\n  * 5 star4 star3 star2 star1 star2 star\n0%0%0%0%0%0%\n\n  * 5 star4 star3 star2 star1 star1 star\n0%0%0%0%0%0%\n\n[How customer reviews and ratings work](javascript:void\\(0\\))\n\nCustomer Reviews, including Product Star Ratings help customers to learn more\nabout the product and decide whether it is the right product for them.\n\nTo calculate the overall star rating and percentage breakdown by star, we don’t\nuse a simple average. Instead, our system considers things like how recent a\nreview is and if the reviewer bought the item on Amazon. It also analyzed\nreviews to verify trustworthiness.\n\n[Learn more how customers reviews work on\nAmazon](https://www.amazon.com/gp/help/customer/display.html/ref=cm_cr_dp_d_omni_lm_btn?nodeId=G8UYX7LALQC8V9KA)\n\n* * *\n### Review this product\n\nShare your thoughts with other customers\n\n[Write a customer review](https://www.amazon.com/review/create-\nreview/ref=cm_cr_dp_d_wr_but_top?ie=UTF8&channel=glance-detail&asin=B0BRGHLXHD)\n\n* * *\nSponsored\n\n[ View Image Gallery  ](javascript:toggleSeeAllRankingView\\(\\))\n\n![Customer image](https://images-na.ssl-images-\namazon.com/images/G/01/x-locale/common/transparent-pixel._V192234675_.gif)\n\n[![](https://images-na.ssl-images-amazon.com/images/G/01/x-locale/common/grey-\npixel.gif) Amazon Customer](javascript:void\\(0\\))\n\n_5.0 out of 5 stars_  \n\n######  Images in this review\n\n### No customer reviews\n\nSponsored\n\n* * *\n**Disclaimer** : While we work to ensure that product information is correct, on\noccasion manufacturers may alter their ingredient lists. Actual product\npackaging and materials may contain more and/or different information than that\nshown on our Web site. We recommend that you do not solely rely on the\ninformation presented and that you always read labels, warnings, and directions\nbefore using or consuming a product. For additional information about a product,\nplease contact the manufacturer. Content on this site is for reference purposes\nand is not intended to substitute for advice given by a physician, pharmacist,\nor other licensed health-care professional. You should not use this information\nas self-diagnosis or for treating a health problem or disease. Contact your\nhealth-care provider immediately if you suspect that you have a medical problem.\nInformation and statements regarding dietary supplements have not been evaluated\nby the Food and Drug Administration and are not intended to diagnose, treat,\ncure, or prevent any disease or health condition. Amazon.com assumes no\nliability for inaccuracies or misstatements about products.\n\n[ Top ](https://www.amazon.com/dp/B0BRGHLXHD) [ About this item\n](https://www.amazon.com/dp/B0BRGHLXHD) [ Similar\n](https://www.amazon.com/dp/B0BRGHLXHD) [ Product information\n](https://www.amazon.com/dp/B0BRGHLXHD) [ Questions\n](https://www.amazon.com/dp/B0BRGHLXHD) [ Reviews\n](https://www.amazon.com/dp/B0BRGHLXHD)\n\n  \n\n![](https://m.media-\namazon.com/images/G/01/personalization/ybh/loading-4x-gray._CB485916920_.gif)  \n---  \nYour recently viewed items and featured recommendations\n\n›\n\n[ View or edit your browsing history ](https://www.amazon.com/gp/history)\n\nAfter viewing product detail pages, look here to find an easy way to navigate\nback to pages you are interested in.\n\n  \n\nBack to top\n\nGet to Know Us\n\n  * [Careers](https://www.amazon.jobs)\n  * [Blog](https://blog.aboutamazon.com/?utm_source=gateway&utm_medium=footer)\n  * [About Amazon](https://www.aboutamazon.com/?utm_source=gateway&utm_medium=footer)\n  * [Investor Relations](https://www.amazon.com/ir)\n  * [Amazon Devices](https://www.amazon.com/gp/browse.html?node=2102313011&ref_=footer_devices)\n  * [Amazon Science](https://www.amazon.science)\n\nMake Money with Us\n\n  * [Sell products on Amazon](https://services.amazon.com/sell.html?ld=AZFSSOA&ref_=footer_soa)\n  * [Sell on Amazon Business](https://services.amazon.com/amazon-business.html?ld=usb2bunifooter&ref_=footer_b2b)\n  * [Sell apps on Amazon](https://developer.amazon.com)\n  * [Become an Affiliate](https://affiliate-program.amazon.com/)\n  * [Advertise Your Products](https://advertising.amazon.com/?ref=ext_amzn_ftr)\n  * [Self-Publish with Us](https://www.amazon.com/gp/seller-account/mm-summary-page.html?ld=AZFooterSelfPublish&topic=*********&ref_=footer_publishing)\n  * [Host an Amazon Hub](https://go.thehub-amazon.com/amazon-hub-locker)\n  * ›[See More Make Money with Us](https://www.amazon.com/b/?node=***********&ld=AZUSSOA-seemore&ref_=footer_seemore)\n\nAmazon Payment Products\n\n  * [Amazon Business Card](https://www.amazon.com/dp/B07984JN3L?plattr=ACOMFO&ie=UTF-8)\n  * [Shop with Points](https://www.amazon.com/gp/browse.html?node=***********&ref_=footer_swp)\n  * [Reload Your Balance](https://www.amazon.com/dp/B0CHTVMXZJ?th=1?ref_=footer_reload_us)\n  * [Amazon Currency Converter](https://www.amazon.com/gp/browse.html?node=*********&ref_=footer_tfx)\n\nLet Us Help You\n\n  * [Amazon and COVID-19](https://www.amazon.com/gp/help/customer/display.html?nodeId=GDFU3JS5AL6SYHRD&ref_=footer_covid)\n  * [Your Account](https://www.amazon.com/gp/css/homepage.html?ref_=footer_ya)\n  * [Your Orders](https://www.amazon.com/gp/css/order-history?ref_=footer_yo)\n  * [Shipping Rates & Policies](https://www.amazon.com/gp/help/customer/display.html?nodeId=468520&ref_=footer_shiprates)\n  * [Returns & Replacements](https://www.amazon.com/gp/css/returns/homepage.html?ref_=footer_hy_f_4)\n  * [Manage Your Content and Devices](https://www.amazon.com/gp/digital/fiona/manage?ref_=footer_myk)\n  * [Help](https://www.amazon.com/gp/help/customer/display.html?nodeId=508510&ref_=footer_gw_m_b_he)\n\n[ ](https://www.amazon.com/?ref_=footer_logo)\n\n[ English](https://www.amazon.com/customer-\npreferences/edit?ie=UTF8&preferencesReturnUrl=%2F&ref_=footer_lang) [ $USD -\nU.S. Dollar ](https://www.amazon.com/customer-\npreferences/edit?ie=UTF8&ref_=footer_cop&preferencesReturnUrl=%2Fdp%2FB0BRGHLXHD)\n[ United States ](https://www.amazon.com/customer-\npreferences/country?ie=UTF8&preferencesReturnUrl=%2F&ref_=footer_icp_cp)\n\n  * ##### [Amazon Music Stream millions  \nof songs](https://music.amazon.com?ref=dm_aff_amz_com)\n\n  * ##### [Amazon Ads Reach customers  \nwherever they  \nspend their time](https://advertising.amazon.com/?ref=footer_advtsing_amzn_com)\n\n  * ##### [6pm Score deals  \non fashion brands](https://www.6pm.com)\n\n  * ##### [AbeBooks Books, art  \n& collectibles](https://www.abebooks.com)\n\n  * ##### [ACX  Audiobook Publishing  \nMade Easy](https://www.acx.com/)\n\n  * ##### [Sell on Amazon Start a Selling Account](https://sell.amazon.com/?ld=AZUSSOA-footer-aff&ref_=footer_sell)\n  * ##### [Veeqo Shipping Software  \nInventory\nManagement](https://www.veeqo.com/?utm_source=amazon&utm_medium=website&utm_campaign=footer)\n\n  * ##### [Amazon Business Everything For  \nYour Business](https://www.amazon.com/business?ref_=footer_retail_b2b)\n\n  * ##### [AmazonGlobal Ship Orders  \nInternationally](https://www.amazon.com/gp/browse.html?node=*********&ref_=footer_amazonglobal)\n\n  * ##### [Amazon Web Services Scalable Cloud  \nComputing Services](https://aws.amazon.com/what-is-cloud-\ncomputing/?sc_channel=EL&sc_campaign=amazonfooter)\n\n  * ##### [Audible Listen to Books & Original  \nAudio Performances](https://www.audible.com)\n\n  * ##### [Box Office Mojo Find Movie  \nBox Office Data](https://www.boxofficemojo.com/?ref_=amzn_nav_ftr)\n\n  * ##### [Goodreads Book reviews  \n& recommendations](https://www.goodreads.com)\n\n  * ##### [IMDb Movies, TV  \n& Celebrities](https://www.imdb.com)\n\n  * ##### [IMDbPro Get Info Entertainment  \nProfessionals Need](https://pro.imdb.com?ref_=amzn_nav_ftr)\n\n  * ##### [Kindle Direct Publishing Indie Digital & Print Publishing  \nMade Easy ](https://kdp.amazon.com)\n\n  * ##### [Prime Video Direct Video Distribution  \nMade Easy](https://videodirect.amazon.com/home/<USER>\n\n  * ##### [Shopbop Designer  \nFashion Brands](https://www.shopbop.com)\n\n  * ##### [Woot! Deals and   \nShenanigans](https://www.woot.com/)\n\n  * ##### [Zappos Shoes &  \nClothing](https://www.zappos.com)\n\n  * ##### [Ring Smart Home  \nSecurity Systems ](https://ring.com)\n\n  * ##### [eero WiFi Stream 4K Video  \nin Every Room](https://eero.com/)\n\n  * ##### [Blink Smart Security  \nfor Every Home ](https://blinkforhome.com/?ref=nav_footer)\n\n  * ##### [Neighbors App  Real-Time Crime  \n& Safety Alerts ](https://shop.ring.com/pages/neighbors-app)\n\n  * ##### [Amazon Subscription Boxes Top subscription boxes – right to your door](https://www.amazon.com/gp/browse.html?node=14498690011&ref_=amzn_nav_ftr_swa)\n  * ##### [PillPack Pharmacy Simplified](https://www.pillpack.com)\n\n  * [Conditions of Use](https://www.amazon.com/gp/help/customer/display.html?nodeId=508088&ref_=footer_cou)\n  * [Privacy Notice](https://www.amazon.com/gp/help/customer/display.html?nodeId=468496&ref_=footer_privacy)\n  * [Consumer Health Data Privacy Disclosure](https://www.amazon.com/gp/help/customer/display.html?ie=UTF8&nodeId=TnACMrGVghHocjL8KB&ref_=footer_consumer_health_data_privacy)\n  * [Your Ads Privacy Choices](https://www.amazon.com/privacyprefs?ref_=footer_iba)\n\n© 1996-2025, Amazon.com, Inc. or its affiliates\n\n\n\nEnriched data:\n$ENRICHED_DATA\n=== OPENFOODFACTS RESULTS ===\n\n--- OpenFoodFacts Rank 1 (BM25 Score: 18.77) ---\ncode: 7702032111046\nurl: http://world-en.openfoodfacts.org/product/7702032111046/cafe-matiz\ncreator: smoothie-app\ncreated_t: **********\ncreated_datetime: 2024-01-31T20:32:16Z\nlast_modified_t: **********\nlast_modified_datetime: 2024-01-31T20:38:17Z\nlast_modified_by: smoothie-app\nlast_updated_t: **********.0\nlast_updated_datetime: 2025-02-06T07:03:54Z\nproduct_name: Café Matiz\ncategories: Plant-based foods and beverages, Plant-based foods, Coffees, Ground coffees\ncategories_tags: en:plant-based-foods-and-beverages,en:plant-based-foods,en:coffees,en:ground-coffees\ncategories_en: Plant-based foods and beverages,Plant-based foods,Coffees,Ground coffees\norigins: Colombia\norigins_tags: en:colombia\norigins_en: Colombia\ncountries: en:Panama\ncountries_tags: en:panama\ncountries_en: Panama\nstates: en:to-be-completed, en:nutrition-facts-to-be-completed, en:ingredients-to-be-completed, en:expiration-date-to-be-completed, en:packaging-code-to-be-completed, en:characteristics-to-be-completed, en:origins-completed, en:categories-completed, en:brands-to-be-completed, en:packaging-to-be-completed, en:quantity-to-be-completed, en:product-name-completed, en:photos-to-be-validated, en:packaging-photo-to-be-selected, en:nutrition-photo-to-be-selected, en:ingredients-photo-selected, en:front-photo-selected, en:photos-uploaded\nstates_tags: en:to-be-completed,en:nutrition-facts-to-be-completed,en:ingredients-to-be-completed,en:expiration-date-to-be-completed,en:packaging-code-to-be-completed,en:characteristics-to-be-completed,en:origins-completed,en:categories-completed,en:brands-to-be-completed,en:packaging-to-be-completed,en:quantity-to-be-completed,en:product-name-completed,en:photos-to-be-validated,en:packaging-photo-to-be-selected,en:nutrition-photo-to-be-selected,en:ingredients-photo-selected,en:front-photo-selected,en:photos-uploaded\nstates_en: To be completed,Nutrition facts to be completed,Ingredients to be completed,Expiration date to be completed,Packaging code to be completed,Characteristics to be completed,Origins completed,Categories completed,Brands to be completed,Packaging to be completed,Quantity to be completed,Product name completed,Photos to be validated,Packaging photo to be selected,Nutrition photo to be selected,Ingredients photo selected,Front photo selected,Photos uploaded\nunique_scans_n: 1.0\npopularity_tags: top-75-percent-scans-2024,top-80-percent-scans-2024,top-85-percent-scans-2024,top-90-percent-scans-2024,top-500-pa-scans-2024,top-1000-pa-scans-2024,top-5000-pa-scans-2024,top-10000-pa-scans-2024,top-50000-pa-scans-2024,top-100000-pa-scans-2024,top-country-pa-scans-2024\ncompleteness: 0.375\nlast_image_t: 1706733326.0\nlast_image_datetime: 2024-01-31T20:35:26Z\nmain_category: en:ground-coffees\nmain_category_en: Ground coffees\nimage_url: https://images.openfoodfacts.org/images/products/770/203/211/1046/front_en.4.400.jpg\nimage_small_url: https://images.openfoodfacts.org/images/products/770/203/211/1046/front_en.4.200.jpg\nimage_ingredients_url: https://images.openfoodfacts.org/images/products/770/203/211/1046/ingredients_en.6.400.jpg\nimage_ingredients_small_url: https://images.openfoodfacts.org/images/products/770/203/211/1046/ingredients_en.6.200.jpg\n\n--- OpenFoodFacts Rank 2 (BM25 Score: 15.96) ---\ncode: 7302032117453\nurl: http://world-en.openfoodfacts.org/product/7302032117453/cafe-matiz-baileys\ncreator: app-kaki\ncreated_t: 1736890235\ncreated_datetime: 2025-01-14T21:30:35Z\nlast_modified_t: 1736890235\nlast_modified_datetime: 2025-01-14T21:30:35Z\nlast_modified_by: app-kaki\nlast_updated_t: 1736890235.0\nlast_updated_datetime: 2025-01-14T21:30:35Z\nproduct_name: Café Matiz Baileys\nbrands: Matiz Baileys\nbrands_tags: matiz-baileys\nbrands_en: Matiz-baileys\ncountries: en:Colombia\ncountries_tags: en:colombia\ncountries_en: Colombia\nstates: en:to-be-completed, en:nutrition-facts-to-be-completed, en:ingredients-to-be-completed, en:expiration-date-to-be-completed, en:packaging-code-to-be-completed, en:characteristics-to-be-completed, en:origins-to-be-completed, en:categories-to-be-completed, en:brands-completed, en:packaging-to-be-completed, en:quantity-to-be-completed, en:product-name-completed, en:photos-to-be-uploaded\nstates_tags: en:to-be-completed,en:nutrition-facts-to-be-completed,en:ingredients-to-be-completed,en:expiration-date-to-be-completed,en:packaging-code-to-be-completed,en:characteristics-to-be-completed,en:origins-to-be-completed,en:categories-to-be-completed,en:brands-completed,en:packaging-to-be-completed,en:quantity-to-be-completed,en:product-name-completed,en:photos-to-be-uploaded\nstates_en: To be completed,Nutrition facts to be completed,Ingredients to be completed,Expiration date to be completed,Packaging code to be completed,Characteristics to be completed,Origins to be completed,Categories to be completed,Brands completed,Packaging to be completed,Quantity to be completed,Product name completed,Photos to be uploaded\ncompleteness: 0.2\n\n--- OpenFoodFacts Rank 3 (BM25 Score: 15.96) ---\ncode: 7702032117925\nurl: http://world-en.openfoodfacts.org/product/7702032117925/cafe-matiz-baileys\ncreator: app-kaki\ncreated_t: 1736890194\ncreated_datetime: 2025-01-14T21:29:54Z\nlast_modified_t: 1736890194\nlast_modified_datetime: 2025-01-14T21:29:54Z\nlast_modified_by: app-kaki\nlast_updated_t: 1736890194.0\nlast_updated_datetime: 2025-01-14T21:29:54Z\nproduct_name: Café Matiz Baileys\nbrands: Matiz Baileys\nbrands_tags: matiz-baileys\nbrands_en: Matiz-baileys\ncountries: en:Colombia\ncountries_tags: en:colombia\ncountries_en: Colombia\nstates: en:to-be-completed, en:nutrition-facts-to-be-completed, en:ingredients-to-be-completed, en:expiration-date-to-be-completed, en:packaging-code-to-be-completed, en:characteristics-to-be-completed, en:origins-to-be-completed, en:categories-to-be-completed, en:brands-completed, en:packaging-to-be-completed, en:quantity-to-be-completed, en:product-name-completed, en:photos-to-be-uploaded\nstates_tags: en:to-be-completed,en:nutrition-facts-to-be-completed,en:ingredients-to-be-completed,en:expiration-date-to-be-completed,en:packaging-code-to-be-completed,en:characteristics-to-be-completed,en:origins-to-be-completed,en:categories-to-be-completed,en:brands-completed,en:packaging-to-be-completed,en:quantity-to-be-completed,en:product-name-completed,en:photos-to-be-uploaded\nstates_en: To be completed,Nutrition facts to be completed,Ingredients to be completed,Expiration date to be completed,Packaging code to be completed,Characteristics to be completed,Origins to be completed,Categories to be completed,Brands completed,Packaging to be completed,Quantity to be completed,Product name completed,Photos to be uploaded\ncompleteness: 0.2\n\n--- OpenFoodFacts Rank 4 (BM25 Score: 14.01) ---\ncode: 832924008066\nurl: http://world-en.openfoodfacts.org/product/0832924008066/matiz-lightly-smoked-wild-sardines\ncreator: macrofactor\ncreated_t: 1743885481\ncreated_datetime: 2025-04-05T20:38:01Z\nlast_modified_t: 1743885481\nlast_modified_datetime: 2025-04-05T20:38:01Z\nlast_modified_by: macrofactor\nlast_updated_t: 1743885481.0\nlast_updated_datetime: 2025-04-05T20:38:01Z\nproduct_name: Matiz Lightly Smoked Wild Sardines\nbrands: Matiz\nbrands_tags: xx:matiz\nbrands_en: matiz\ncountries: en:Canada\ncountries_tags: en:canada\ncountries_en: Canada\nserving_size: 84 g\nserving_quantity: 84.0\nstates: en:to-be-completed, en:nutrition-facts-completed, en:ingredients-to-be-completed, en:expiration-date-to-be-completed, en:packaging-code-to-be-completed, en:characteristics-to-be-completed, en:origins-to-be-completed, en:categories-to-be-completed, en:brands-completed, en:packaging-to-be-completed, en:quantity-to-be-completed, en:product-name-completed, en:photos-to-be-uploaded\nstates_tags: en:to-be-completed,en:nutrition-facts-completed,en:ingredients-to-be-completed,en:expiration-date-to-be-completed,en:packaging-code-to-be-completed,en:characteristics-to-be-completed,en:origins-to-be-completed,en:categories-to-be-completed,en:brands-completed,en:packaging-to-be-completed,en:quantity-to-be-completed,en:product-name-completed,en:photos-to-be-uploaded\nstates_en: To be completed,Nutrition facts completed,Ingredients to be completed,Expiration date to be completed,Packaging code to be completed,Characteristics to be completed,Origins to be completed,Categories to be completed,Brands completed,Packaging to be completed,Quantity to be completed,Product name completed,Photos to be uploaded\ncompleteness: 0.3\nenergy-kcal_100g: 202.0\nenergy_100g: 846.0\nfat_100g: 11.9\nsaturated-fat_100g: 2.98\ntrans-fat_100g: 0.0\ncholesterol_100g: 0.0714\ncarbohydrates_100g: 0.0\nsugars_100g: 0.0\nfiber_100g: 0.0\nproteins_100g: 23.8\nsalt_100g: 0.625\nsodium_100g: 0.25\npotassium_100g: 0.417\ncalcium_100g: 0.417\niron_100g: 0.00357\n\n--- OpenFoodFacts Rank 5 (BM25 Score: 12.27) ---\ncode: 832924008035\nurl: http://world-en.openfoodfacts.org/product/0832924008035/matiz-small-wild-pepper-sardines\ncreator: macrofactor\ncreated_t: 1743358785\ncreated_datetime: 2025-03-30T18:19:45Z\nlast_modified_t: 1743358785\nlast_modified_datetime: 2025-03-30T18:19:45Z\nlast_modified_by: macrofactor\nlast_updated_t: 1743358785.0\nlast_updated_datetime: 2025-03-30T18:19:45Z\nproduct_name: Matiz Small wild Pepper Sardines\nbrands: Matiz\nbrands_tags: xx:matiz\nbrands_en: matiz\ncountries: en:Canada\ncountries_tags: en:canada\ncountries_en: Canada\nserving_size: 60 g\nserving_quantity: 60.0\nstates: en:to-be-completed, en:nutrition-facts-completed, en:ingredients-to-be-completed, en:expiration-date-to-be-completed, en:packaging-code-to-be-completed, en:characteristics-to-be-completed, en:origins-to-be-completed, en:categories-to-be-completed, en:brands-completed, en:packaging-to-be-completed, en:quantity-to-be-completed, en:product-name-completed, en:photos-to-be-uploaded\nstates_tags: en:to-be-completed,en:nutrition-facts-completed,en:ingredients-to-be-completed,en:expiration-date-to-be-completed,en:packaging-code-to-be-completed,en:characteristics-to-be-completed,en:origins-to-be-completed,en:categories-to-be-completed,en:brands-completed,en:packaging-to-be-completed,en:quantity-to-be-completed,en:product-name-completed,en:photos-to-be-uploaded\nstates_en: To be completed,Nutrition facts completed,Ingredients to be completed,Expiration date to be completed,Packaging code to be completed,Characteristics to be completed,Origins to be completed,Categories to be completed,Brands completed,Packaging to be completed,Quantity to be completed,Product name completed,Photos to be uploaded\ncompleteness: 0.3\nenergy-kcal_100g: 183.0\nenergy_100g: 767.0\nfat_100g: 11.7\nsaturated-fat_100g: 6.67\ntrans-fat_100g: 0.0\ncholesterol_100g: 0.0667\ncarbohydrates_100g: 0.0\nsugars_100g: 0.0\nfiber_100g: 0.0\nproteins_100g: 21.7\nsalt_100g: 1.17\nsodium_100g: 0.467\npotassium_100g: 0.333\ncalcium_100g: 0.5\niron_100g: 0.00167\n\n=== FDA RESULTS ===\n\n--- FDA Rank 1 (BM25 Score: 10.79) ---\nfdc_id: 530121\nfdc_id_food: 530121\nbrand_owner: NATIVA YERBA MATE\ngtin_upc: 830033002821\ningredients: 100% NATURAL GROUND MATE LEAVES GENUS: ILEX PARAGUARIENSIS\nserving_size: 6.0\nserving_size_unit: g\nhousehold_serving_fulltext: 1 Tbsp\nbranded_food_category: Coffee\ndata_source: LI\nmodified_date: 2018-02-14\navailable_date: 2019-04-01\nmarket_country: United States\nid_x: 1367662\nfdc_id_nutrient: 168529\nnutrient_id: 1051\namount: 91.3\nid_y: 1051\nname: Water\nunit_name: G\nnutrient_nbr: 255.0\nrank: 100.0\ndata_type: branded_food\ndescription: SUAVE BLEND MILD YERBA MATE TEA, HERBAL COFFEE ALTERNATIVE\nfood_category_id: Coffee\npublication_date: 2019-04-01\n\n--- FDA Rank 2 (BM25 Score: 10.79) ---\nfdc_id: 1796312\nfdc_id_food: 1796312\nbrand_owner: Nativa Yerba Mate Inc.\nbrand_name: NATIVA YERBA MATE\ngtin_upc: 830033002821\ningredients: 100% NATURAL GROUND MATE LEAVES GENUS: ILEX PARAGUARIENSIS\nnot_a_significant_source_of: NOT A SIGNIFICANT SOURCE OF CALORIES FROM FAT, SATURATED FAT, CHOLESTEROL, SUGARS, VITAMIN A, OR VITAMIN C.\nserving_size: 6.0\nserving_size_unit: g\nhousehold_serving_fulltext: 1 Tbsp\nbranded_food_category: Coffee\ndata_source: LI\npackage_weight: 1 lbs\nmodified_date: 2018-02-14\navailable_date: 2021-06-17\nmarket_country: United States\nid_x: 5758140\nfdc_id_nutrient: 389563\nnutrient_id: 1093\namount: 58824.0\nderivation_id: 70.0\npercent_daily_value: 2.0\nid_y: 1093\nname: Sodium, Na\nunit_name: MG\nnutrient_nbr: 307.0\nrank: 5800.0\ndata_type: branded_food\ndescription: SUAVE BLEND MILD YERBA MATE TEA, HERBAL COFFEE ALTERNATIVE\nfood_category_id: Coffee\npublication_date: 2021-06-17\n\n--- FDA Rank 3 (BM25 Score: 10.43) ---\nfdc_id: 1801938\nfdc_id_food: 1801938\nbrand_owner: Fante Inc\nbrand_name: RANCHERA\ngtin_upc: 78732004665\ningredients: TOMATOES, ONIONS, PEPPERS, WATER, VINEGAR, CILANTRO, SEA SALT, GARLIC, OREGANO, CITRIC ACID & CUMIN.\nserving_size: 30.0\nserving_size_unit: g\nhousehold_serving_fulltext: 2 Tbsp\nbranded_food_category: Dips & Salsa\ndata_source: LI\npackage_weight: 425 g\nmodified_date: 2017-10-13\navailable_date: 2021-06-17\nmarket_country: United States\nid_x: 3762299\nfdc_id_nutrient: 389995\nnutrient_id: 1253\namount: 0.0\nderivation_id: 75.0\npercent_daily_value: 0.0\nid_y: 1253\nname: Cholesterol\nunit_name: MG\nnutrient_nbr: 601.0\nrank: 15700.0\ndata_type: branded_food\ndescription: HOME STYLE SALSA, MILD SUAVE\nfood_category_id: Dips & Salsa\npublication_date: 2021-06-17\n\n--- FDA Rank 4 (BM25 Score: 10.43) ---\nfdc_id: 1931628\nfdc_id_food: 1931628\nbrand_owner: Fante Inc\nbrand_name: RANCHERA\ngtin_upc: 78732004665\ningredients: TOMATOES, ONIONS, PEPPERS, WATER, VINEGAR, CILANTRO, SEA SALT, GARLIC, OREGANO, CITRIC ACID & CUMIN.\nserving_size: 30.0\nserving_size_unit: g\nhousehold_serving_fulltext: 2 Tbsp\nbranded_food_category: Dips & Salsa\ndata_source: LI\npackage_weight: 15 oz/425 g\nmodified_date: 2017-10-13\navailable_date: 2021-07-29\nmarket_country: United States\nid_x: 4410323\nfdc_id_nutrient: 399506\nnutrient_id: 1003\namount: 7.14\nderivation_id: 70.0\npercent_daily_value: 0.0\nid_y: 1003\nname: Protein\nunit_name: G\nnutrient_nbr: 203.0\nrank: 600.0\ndata_type: branded_food\ndescription: HOME STYLE SALSA, MILD SUAVE\nfood_category_id: Dips & Salsa\npublication_date: 2021-07-29\n\n--- FDA Rank 5 (BM25 Score: 10.43) ---\nfdc_id: 2285164\nfdc_id_food: 2285164\nbrand_owner: Fante Inc\nbrand_name: RANCHERA\ngtin_upc: 78732004665\ningredients: TOMATOES, ONIONS, PEPPERS, WATER, VINEGAR, CILANTRO, SEA SALT, GARLIC, OREGANO, CITRIC ACID & CUMIN.\nserving_size: 30.0\nserving_size_unit: g\nhousehold_serving_fulltext: 2 Tbsp\nbranded_food_category: Dips & Salsa\ndata_source: LI\npackage_weight: 15 oz/425 g\nmodified_date: 2017-10-13\navailable_date: 2022-05-23\nmarket_country: United States\nid_x: 3178691\nfdc_id_nutrient: 426985\nnutrient_id: 1089\namount: 3.86\nderivation_id: 75.0\npercent_daily_value: 6.0\nid_y: 1089\nname: Iron, Fe\nunit_name: MG\nnutrient_nbr: 303.0\nrank: 5400.0\ndata_type: branded_food\ndescription: HOME STYLE SALSA, MILD SUAVE\nfood_category_id: Dips & Salsa\npublication_date: 2022-05-23\n\n\n\nInstructions:\n1. You are provided with product images and product details fetched from online sources in $PRODUCT_DATA\n2. You are also provided with products data from other sources which might be similar to the original product in $ENRICHED_DATA\n3. Your job is to go through the images of the product, $PRODUCT_DATA and $ENRICHED_DATA and extract information according to schema provided in RESPONSE SCHEMA\n4. You are also provided with the format in which the response must be in RESPONSE SCHEMA\n5. Absolutely avoid ```json or ``` in the final response\n6. If you get multiple images which are unrelated to each other, provide output only for one product \n7. Do not output the schema itself. Make sure your output is JSON formatted string which satisfies the JSON schema\n8. Do not leave the qualified fields empty. Do guess work and provide some value for the qualified fields in the response.", "response": "{\n  \"analysed_data\": {\n    \"generalData\": {\n      \"gtinOnPack\": \"7702032111046\",\n      \"gtin14\": \"07702032111046\",\n      \"upc12\": \"770203211104\",\n      \"brandOwner\": \"Matiz\",\n      \"brandName\": \"Matiz\",\n      \"department\": \"Grocery\",\n      \"superCategory\": \"Beverages\",\n      \"category\": \"Coffee\",\n      \"subCategory\": \"Ground Coffee\",\n      \"segment\": \"Light Roast\",\n      \"productTitle\": \"Matiz Colombian Light Coffee (2 Pack)\",\n      \"variant\": \"Suave\",\n      \"netWeight1Value\": 340,\n      \"netWeight1UOM\": \"g\",\n      \"netWeight2Value\": 11.99,\n      \"netWeight2UOM\": \"oz\",\n      \"unitsPerPack\": 2,\n      \"unitsPerPackDescriptor\": \"Bags\",\n      \"storage\": \"Room Temperature\",\n      \"numberOfIngredients\": 1\n    },\n    \"servingSize\": {\n      \"servingSize\": 6,\n      \"servingSizeUnit\": \"g\",\n      \"servesPerPack\": 56,\n      \"servingDescription\": \"1 Tbsp (6g)\",\n      \"servingSize2\": null,\n      \"servingSize2Unit\": null\n    },\n    \"nutritionalInformation\": {\n      \"stated\": {\n        \"totalAmount\": null,\n        \"totalAmountUOM\": null,\n        \"source\": \"Amazon Images\",\n        \"macronutrients\": [],\n        \"micronutrients\": []\n      },\n      \"qualified\": {\n        \"totalAmount\": 100,\n        \"totalAmountUOM\": \"g\",\n        \"source\": \"ScrapingQualificationAI\",\n        \"macronutrients\": [\n          {\n            \"name\": \"Protein\",\n            \"amount\": 0.2,\n            \"totalAmount\": 100,\n            \"UOM\": \"g\",\n            \"dailyValue\": null,\n            \"source\": \"ScrapingQualificationAI\"\n          },\n          {\n            \"name\": \"Carbohydrates\",\n            \"amount\": 0.1,\n            \"totalAmount\": 100,\n            \"UOM\": \"g\",\n            \"dailyValue\": null,\n            \"source\": \"ScrapingQualificationAI\"\n          },\n          {\n            \"name\": \"Fat\",\n            \"amount\": 0.0,\n            \"totalAmount\": 100,\n            \"UOM\": \"g\",\n            \"dailyValue\": null,\n            \"source\": \"ScrapingQualificationAI\"\n          }\n        ],\n        \"micronutrients\": []\n      }\n    },\n    \"ingredients\": {\n      \"stated\": {\n        \"source\": \"Amazon Images\",\n        \"ingredientList\": [\n          {\n            \"text\": \"100% Colombian Arabica Coffee\",\n            \"amount\": 340,\n            \"amountUOM\": \"g\",\n            \"dietary_preference\": {\n              \"Vegan\": true,\n              \"Vegetarian\": true,\n              \"Pescatarian\": true,\n              \"WhiteMeatOnly\": false,\n              \"KetoFriendly\": true,\n              \"LowFodmap\": true\n            },\n            \"allergens\": {\n              \"Sugar\": false,\n              \"Celery\": false,\n              \"Gluten\": false,\n              \"Crustaceans\": false,\n              \"Eggs\": false,\n              \"Fish\": false,\n              \"Lupin\": false,\n              \"Milk\": false,\n              \"Peanuts\": false,\n              \"Sesame\": false,\n              \"TreeNuts\": false\n            },\n            \"religious_labels\": {\n              \"Halal\": true,\n              \"Kosher\": true,\n              \"Hindu\": true,\n              \"Jain\": true\n            },\n            \"food_safety_labels\": {\n              \"GMO\": false,\n              \"NoGMO\": true,\n              \"Hormones\": false,\n              \"Carcinogenic\": false,\n              \"Organic\": true,\n              \"ProductRecalls\": false\n            },\n            \"sustainability_labels\": {\n              \"Recycled\": false,\n              \"AnimalWelfare\": true,\n              \"OrganicPositioning\": true,\n              \"PlantBased\": true,\n              \"SocialResponsibility\": true,\n              \"SustainablePackaging\": true\n            },\n            \"source\": \"Amazon Images\",\n            \"subIngredients\": []\n          }\n        ]\n      },\n      \"qualified\": {\n        \"source\": \"ScrapingQualificationAI\",\n        \"ingredientList\": [\n          {\n            \"text\": \"100% Colombian Arabica Coffee\",\n            \"amount\": 340,\n            \"amountUOM\": \"g\",\n            \"dietary_preference\": {\n              \"Vegan\": true,\n              \"Vegetarian\": true,\n              \"Pescatarian\": true,\n              \"WhiteMeatOnly\": false,\n              \"KetoFriendly\": true,\n              \"LowFodmap\": true\n            },\n            \"allergens\": {\n              \"Sugar\": false,\n              \"Celery\": false,\n              \"Gluten\": false,\n              \"Crustaceans\": false,\n              \"Eggs\": false,\n              \"Fish\": false,\n              \"Lupin\": false,\n              \"Milk\": false,\n              \"Peanuts\": false,\n              \"Sesame\": false,\n              \"TreeNuts\": false\n            },\n            \"religious_labels\": {\n              \"Halal\": true,\n              \"Kosher\": true,\n              \"Hindu\": true,\n              \"Jain\": true\n            },\n            \"food_safety_labels\": {\n              \"GMO\": false,\n              \"NoGMO\": true,\n              \"Hormones\": false,\n              \"Carcinogenic\": false,\n              \"Organic\": true,\n              \"ProductRecalls\": false\n            },\n            \"sustainability_labels\": {\n              \"Recycled\": false,\n              \"AnimalWelfare\": true,\n              \"OrganicPositioning\": true,\n              \"PlantBased\": true,\n              \"SocialResponsibility\": true,\n              \"SustainablePackaging\": true\n            },\n            \"source\": \"ScrapingQualificationAI\",\n            \"subIngredients\": []\n          }\n        ]\n      }\n    },\n    \"claims\": {\n      \"certifications\": [\n        \"100% Colombian Arabica Coffee\",\n        \"Sustainably Sourced\"\n      ],\n      \"nutritionalClaims\": [\n        \"Low Fat\",\n        \"No Sugar\"\n      ],\n      \"ingredientClaims\": [\n        \"No Artificial Ingredients\",\n        \"Organic\"\n      ],\n      \"preparation\": {\n        \"storage\": \"Store in a cool, dry place\",\n        \"heatingInstructions\": {\n          \"toaster\": null,\n          \"oven\": null\n        }\n      },\n      \"sustainability\": {\n        \"packaging\": [\n          \"100% Recyclable Packaging\"\n        ],\n        \"environmentalClaims\": [\n          \"Sustainably Sourced\",\n          \"Social Responsibility\"\n        ]\n      },\n      \"contact\": {\n        \"website\": \"https://www.matiz.com\",\n        \"phone\": \"**************\"\n      }\n    },\n    \"NPI 2.0 Food Packages - Allergens & Intolerances\": {\n      \"fdaRegulatedAllergens\": \"None\",\n      \"caseinQualified\": \"No\",\n      \"caseinStated\": \"No Casein Added\",\n      \"coconutQualified\": \"No\",\n      \"coconutStated\": \"No Coconut Added\",\n      \"cornQualified\": \"No\",\n      \"cornStated\": \"No Corn Added\",\n      \"dairyLevelStated\": \"Dairy-Free\",\n      \"dairyQualified\": \"No\",\n      \"dairyStated\": \"No Dairy Added\",\n      \"eggLevelStated\": \"Egg-Free\",\n      \"eggQualified\": \"No\",\n      \"eggStated\": \"No Eggs Added\",\n      \"falcpaCommonAllergensQualified\": \"No\",\n      \"falcpaCommonAllergensStated\": \"None\",\n      \"fishLevelStated\": \"Fish-Free\",\n      \"fishQualified\": \"No\",\n      \"fishStated\": \"No Fish Added\",\n      \"glutenLevelStated\": \"Gluten-Free\",\n      \"glutenQualified\": \"No\"\n    },\n    \"cleanLabel\": {\n      \"artisanalStated\": \"Handcrafted\",\n      \"coldPressedIngredientsStated\": null,\n      \"craftStated\": \"Craft Brewed\",\n      \"gourmetStated\": \"Gourmet Quality\",\n      \"localStated\": \"Locally Sourced\",\n      \"madeInUsaStated\": null,\n      \"premiumStated\": \"Premium Quality\",\n      \"smallBatchStated\": \"Small Batch Produced\",\n      \"animalByProductQualified\": \"No\",\n      \"animalByProductStated\": \"No Animal By-Products Added\",\n      \"antibioticsQualified\": \"No\",\n      \"antibioticsStated\": \"No Antibiotics Ever\",\n      \"artificialColorsQualified\": \"No\",\n      \"artificialColorsStated\": \"No Artificial Colors Added\",\n      \"artificialFlavorsQualified\": \"No\",\n      \"artificialFlavorsStated\": \"No Artificial Flavors Added\",\n      \"artificialIngredientsQualified\": \"No\",\n      \"artificialPreservativesQualified\": \"No\",\n      \"artificialPreservativesStated\": \"No Artificial Preservatives Added\",\n      \"artificialSweetenersQualified\": \"No\",\n      \"artificialSweetenersStated\": \"No Artificial Sweeteners Added\",\n      \"countOfIngredientsQualified\": \"1\",\n      \"gmoPresenceQualified\": \"No\",\n      \"gmoPresenceStated\": \"Non-GMO Project Verified\",\n      \"highFructoseCornSyrupQualified\": \"No\",\n      \"highFructoseCornSyrupStated\": \"No High Fructose Corn Syrup Added\",\n      \"hormonesQualified\": \"No\",\n      \"hormonesStated\": \"No Hormones Added\",\n      \"naturalColorsQualified\": \"Yes\",\n      \"naturalColorsStated\": \"Colored with Natural Ingredients\",\n      \"naturalFlavorsQualified\": \"Yes\",\n      \"naturalFlavorsStated\": \"Flavored with Natural Ingredients\",\n      \"naturalPreservativesQualified\": \"Yes\",\n      \"naturalPreservativesStated\": \"Preserved with Natural Ingredients\",\n      \"naturalSweetenersQualified\": \"Yes\",\n      \"naturalSweetenersStated\": \"Sweetened with Natural Ingredients\",\n      \"preservativesQualified\": \"No\",\n      \"preservativesStated\": \"No Preservatives Added\",\n      \"rbstQualified\": \"No\",\n      \"rbstStated\": \"No rBST Added\",\n      \"recognizableIngredientsQualified\": \"Yes\",\n      \"sugarAlcoholsQualified\": \"No\",\n      \"sugarAlcoholsStated\": \"No Sugar Alcohols Added\"\n    },\n    \"additionalInfo\": {\n      \"energy\": null,\n      \"weight\": 340,\n      \"categories\": \"Plant-based foods and beverages, Coffees, Ground coffees\",\n      \"packaging\": \"Plastic\",\n      \"ecoscore\": \"B\",\n      \"nova_group\": \"1\",\n      \"nutriscore_grade\": \"A\",\n      \"data_source\": \"Manufactruer_claims,FoodScanGenius_AI\",\n      \"dietary_preference\": {\n        \"Vegan\": true,\n        \"Vegetarian\": true,\n        \"Pescatarian\": true,\n        \"WhiteMeatOnly\": false,\n        \"KetoFriendly\": true,\n        \"LowFodmap\": true\n      },\n      \"allergens\": {\n        \"Sugar\": false,\n        \"Celery\": false,\n        \"Gluten\": false,\n        \"Crustaceans\": false,\n        \"Eggs\": false,\n        \"Fish\": false,\n        \"Lupin\": false,\n        \"Milk\": false,\n        \"Peanuts\": false,\n        \"Sesame\": false,\n        \"TreeNuts\": false\n      },\n      \"religious_labels\": {\n        \"Halal\": true,\n        \"Kosher\": true,\n        \"Hindu\": true,\n        \"Jain\": true\n      },\n      \"food_safety_labels\": {\n        \"GMO\": false,\n        \"NoGMO\": true,\n        \"Hormones\": false,\n        \"Carcinogenic\": false,\n        \"Organic\": true,\n        \"ProductRecalls\": false\n      },\n      \"sustainability_labels\": {\n        \"Recycled\": false,\n        \"AnimalWelfare\": true,\n        \"OrganicPositioning\": true,\n        \"PlantBased\": true,\n        \"SocialResponsibility\": true,\n        \"SustainablePackaging\": true\n      },\n      \"average_customer_rating\": null,\n      \"ASIN\": \"B0BRGHLXHD\",\n      \"traces\": \"None\",\n      \"country_of_origin\": \"Colombia\",\n      \"customerCareNumber\": \"**************\",\n      \"email\": \"<EMAIL>\",\n      \"websiteLink\": \"https://www.matiz.com\"\n    }\n  },\n  \"scraped_data\": {\n    \"g\": 500,\n    \"url\": \"https://www.amazon.com/dp/B0BRGHLXHD\",\n    \"asin\": \"B0BRGHLXHD\",\n    \"size\": \"5.5 Ounce (Pack of 2)\",\n    \"type\": \"COFFEE\",\n    \"brand\": \"RUUFE\",\n    \"color\": null,\n    \"isB2B\": false,\n    \"isSNS\": false,\n    \"model\": null,\n    \"stats\": null,\n    \"title\": \"Matiz Colombian Light Coffee (2 Pack) Matiz Cafe Colombiano Suave roasted and ground coffee Matiz Suave\",\n    \"author\": null,\n    \"coupon\": null,\n    \"format\": null,\n    \"images\": [\n      {\n        \"m\": \"51h7aTHB51L.jpg\",\n        \"mH\": 500,\n        \"mW\": 500\n      },\n      {\n        \"m\": \"41IEemPPiQL.jpg\",\n        \"mH\": 500,\n        \"mW\": 500\n      },\n      {\n        \"m\": \"51eXNUQDwML.jpg\",\n        \"mH\": 500,\n        \"mW\": 500\n      },\n      {\n        \"m\": \"51NaxHcy7iL.jpg\",\n        \"mH\": 500,\n        \"mW\": 500\n      },\n      {\n        \"m\": \"41szrlvu-rL.jpg\",\n        \"mH\": 500,\n        \"mW\": 500\n      },\n      {\n        \"m\": \"61C5itIkxDL.jpg\",\n        \"mH\": 500,\n        \"mW\": 500\n      }\n    ],\n    \"offers\": null,\n    \"binding\": null,\n    \"eanList\": [\n      \"7706512558604\"\n    ],\n    \"edition\": null,\n    \"fbaFees\": null,\n    \"upcList\": null,\n    \"urlSlug\": \"Colombian-Coffee-Colombiano-roasted-ground\",\n    \"domainId\": 1,\n    \"features\": [\n      \"QUALITY: Premium 100% Colombian Coffee.\",\n      \"FLAVOR: Light\",\n      \"ROAST LEVEL: Roast Level: Light\",\n      \"HOW TO PREPARE: It can be diluted in milk or hot or cold water.\",\n      \"100% COLOMBIAN ARABICA COFFEE: Pure Arabica Colombian coffee. No blends. Features a medium roast & grind, which produces a smooth balanced body with notes of sweetness.\",\n      \"PRIME CLIMATE: The Andes Green Mountains provide the perfect recipe of elevation, soil, and climate for our coffee's globally recognized quality.\",\n      \"BEHIND THE BEANS: This coffee is sourced directly from family farms who take pride in their meticulously hand-picked coffee beans. Harvested at their peak, our roasted coffee beans are pristinely washed and sundried to perfection\"\n    ],\n    \"itemForm\": \"Powder\",\n    \"imagesCSV\": \"51h7aTHB51L.jpg,41IEemPPiQL.jpg,51eXNUQDwML.jpg,51NaxHcy7iL.jpg,41szrlvu-rL.jpg,61C5itIkxDL.jpg\",\n    \"itemWidth\": 0,\n    \"languages\": null,\n    \"launchpad\": false,\n    \"unitCount\": {\n      \"unitType\": \"Ounce\",\n      \"unitValue\": 11.0,\n      \"eachUnitCount\": 1.0\n    },\n    \"hasReviews\": false,\n    \"images_url\": [\n      \"https://m.media-amazon.com/images/I/51h7aTHB51L.jpg\",\n      \"https://m.media-amazon.com/images/I/41IEemPPiQL.jpg\",\n      \"https://m.media-amazon.com/images/I/51eXNUQDwML.jpg\",\n      \"https://m.media-amazon.com/images/I/51NaxHcy7iL.jpg\",\n      \"https://m.media-amazon.com/images/I/41szrlvu-rL.jpg\",\n      \"https://m.media-amazon.com/images/I/61C5itIkxDL.jpg\"\n    ],\n    \"itemHeight\": 0,\n    \"itemLength\": 0,\n    \"itemWeight\": 0,\n    \"lastUpdate\": 7534854,\n    \"parentAsin\": null,\n    \"partNumber\": null,\n    \"promotions\": null,\n    \"description\": \"Premium 100% Colombian Coffee. Light coffee that has been subjected to a coffee dehydration process at low temperatures that allow the aroma and flavor notes to stand out. It can be diluted in milk or hot or cold water. Manufacturer : Matiz Item Form: Ground Brand: Matiz Flavor: Intense Caffeine Content: Yes Roast Level: Medium_roast Organic, local, global and fair trade options available\",\n    \"listedSince\": 6128524,\n    \"productType\": 0,\n    \"releaseDate\": -1,\n    \"categoryTree\": [\n      {\n        \"name\": \"Grocery & Gourmet Food\",\n        \"catId\": 16310101\n      },\n      {\n        \"name\": \"Beverages\",\n        \"catId\": 16310231\n      },\n      {\n        \"name\": \"Coffee\",\n        \"catId\": 16318031\n      },\n      {\n        \"name\": \"Instant Coffee\",\n        \"catId\": 2251594011\n      }\n    ],\n    \"manufacturer\": \"Matiz\",\n    \"packageWidth\": 0,\n    \"productGroup\": \"Grocery\",\n    \"rootCategory\": 16310101,\n    \"variationCSV\": null,\n    \"brandStoreUrl\": \"/stores/RUUFE/page/E7C1E31F-8174-45D6-93E6-DEF59FD23571\",\n    \"newPriceIsMAP\": false,\n    \"numberOfItems\": 2,\n    \"numberOfPages\": -1,\n    \"packageHeight\": 0,\n    \"packageLength\": 0,\n    \"packageWeight\": 0,\n    \"trackingSince\": 6321850,\n    \"brandStoreName\": \"RUUFE\",\n    \"ebayListingIds\": null,\n    \"isAdultProduct\": false,\n    \"isRedirectASIN\": false,\n    \"lastEbayUpdate\": 0,\n    \"isHeatSensitive\": false,\n    \"itemTypeKeyword\": \"instant-coffee\",\n    \"lastPriceChange\": 7419718,\n    \"liveOffersOrder\": null,\n    \"packageQuantity\": -1,\n    \"publicationDate\": -1,\n    \"lastRatingUpdate\": 7530588,\n    \"offersSuccessful\": false,\n    \"brandStoreUrlName\": \"RUUFE\",\n    \"availabilityAmazon\": -1,\n    \"salesRankReference\": 16310101,\n    \"websiteDisplayGroup\": \"grocery_display_on_website\",\n    \"isEligibleForTradeIn\": false,\n    \"buyBoxSellerIdHistory\": null,\n    \"websiteDisplayGroupName\": \"Grocery\",\n    \"frequentlyBoughtTogether\": [\n      \"B000LXB9TC\",\n      \"B00AYLWSOQ\"\n    ],\n    \"buyBoxEligibleOfferCounts\": [\n      0,\n      1,\n      0,\n      0,\n      0,\n      0,\n      0,\n      0\n    ],\n    \"isEligibleForSuperSaverShipping\": false\n  }\n}", "images_base64": ["data:image/jpeg;base64,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", "data:image/jpeg;base64,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", "data:image/jpeg;base64,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", "data:image/jpeg;base64,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", "data:image/jpeg;base64,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", "data:image/jpeg;base64,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"], "image_urls": ["https://m.media-amazon.com/images/I/51h7aTHB51L.jpg", "https://m.media-amazon.com/images/I/41IEemPPiQL.jpg", "https://m.media-amazon.com/images/I/51eXNUQDwML.jpg", "https://m.media-amazon.com/images/I/51NaxHcy7iL.jpg", "https://m.media-amazon.com/images/I/41szrlvu-rL.jpg", "https://m.media-amazon.com/images/I/61C5itIkxDL.jpg"]}