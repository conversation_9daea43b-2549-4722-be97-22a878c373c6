{
  "prompt": "\nYou are a helpful assistant who can extract product information from images and provide it in JSON format based on the schema below\n\nRESPONSE SCHEMA\n{\n  \"$schema\": \"http://json-schema.org/draft-07/schema#\",\n  \"title\": \"Comprehensive Product Information Schema\",\n  \"description\": \"A comprehensive schema that combines all product information including general data, nutritional information, serving sizes, ingredients, allergens, and product claims. This schema represents a complete view of product attributes, certifications, and nutritional data in a structured format.\",\n  \"type\": \"object\",\n  \"properties\": {\n    \"generalData\": {\n      \"type\": \"object\",\n      \"description\": \"General product identification and classification information\",\n      \"properties\": {\n        \"gtinOnPack\": {\n          \"type\": \"string\",\n          \"description\": \"Global Trade Item Number (GTIN) as it appears on the product packaging. This is the standard barcode number used for product identification. Example: '013562130627' for a specific product variant.\"\n        },\n        \"gtin14\": {\n          \"type\": \"string\",\n          \"description\": \"14-digit GTIN format with leading zeros. This is the standardized format for GTIN numbers, always 14 digits long. Example: '00013562130627' (same as GTIN On Pack but with leading zeros to make it 14 digits).\"\n        },\n        \"upc12\": {\n          \"type\": \"string\",\n          \"description\": \"12-digit Universal Product Code (UPC) format without check digit. This is the standard retail barcode format used in North America. Example: '001356213062' (first digit is typically 0 for standard UPC).\"\n        },\n        \"brandOwner\": {\n          \"type\": \"string\",\n          \"description\": \"NielsenIQ Brand Owner/Manufacturer name. This is the legal entity that owns the brand. Example: 'ANNIE'S HOMEGROWN INC.' for Annie's products.\"\n        },\n        \"brandName\": {\n          \"type\": \"string\",\n          \"description\": \"NielsenIQ Brand name including parent company. This combines the brand name with its parent company for clarity. Example: 'ANNIE'S (ANNIE'S HOMEGROWN INC.)' shows both the consumer-facing brand and its corporate owner.\"\n        },\n        \"department\": {\n          \"type\": \"string\",\n          \"description\": \"NielsenIQ Department category - the highest level of product classification. Example: 'FROZEN' for frozen food products, 'DAIRY' for dairy products, 'BAKERY' for bakery items.\"\n        },\n        \"superCategory\": {\n          \"type\": \"string\",\n          \"description\": \"NielsenIQ Super Category classification - the second level of product classification. Example: 'PREPARED FOODS' for ready-to-eat items, 'BEVERAGES' for drinks, 'SNACKS' for snack products.\"\n        },\n        \"category\": {\n          \"type\": \"string\",\n          \"description\": \"NielsenIQ Category classification - a more specific product grouping. Example: 'WAFFLE' for waffle products, 'YOGURT' for yogurt products, 'CHIPS' for potato chips.\"\n        },\n        \"subCategory\": {\n          \"type\": \"string\",\n          \"description\": \"NielsenIQ Sub Category classification - a detailed product grouping. Example: 'REGULAR' for standard products, 'LIGHT' for reduced-fat items, 'ORGANIC' for organic products.\"\n        },\n        \"segment\": {\n          \"type\": \"string\",\n          \"description\": \"NielsenIQ Segment classification - the most specific product grouping. Example: 'REGULAR' for standard products, 'FLAVORED' for flavored variants, 'WHOLE GRAIN' for whole grain products.\"\n        },\n        \"productTitle\": {\n          \"type\": \"string\",\n          \"description\": \"Product title from identifying header information. This is the full product name as it appears in the system. Example: 'BIRTHDAY CAKE ORGANIC WAFFLES' or 'STRAWBERRY SHORTCAKE ORGANIC WAFFLES'.\"\n        },\n        \"variant\": {\n          \"type\": \"string\",\n          \"description\": \"Product variant or flavor information. This specifies the specific version or flavor of the product. Example: 'BIRTHDAY CAKE' or 'STRAWBERRY SHORTCAKE' for different waffle flavors.\"\n        },\n        \"netWeight1Value\": {\n          \"type\": \"number\",\n          \"description\": \"Primary net weight value of the product. This is the main weight measurement. Example: 9.8 for 9.8 ounces, 280 for 280 grams.\"\n        },\n        \"netWeight1UOM\": {\n          \"type\": \"string\",\n          \"description\": \"Primary net weight unit of measurement. This specifies the unit used for the primary weight. Example: 'oz' for ounces, 'g' for grams, 'lb' for pounds.\"\n        },\n        \"netWeight2Value\": {\n          \"type\": \"number\",\n          \"description\": \"Secondary net weight value of the product. This is an alternative weight measurement, often in a different unit. Example: 280 for 280 grams when primary is in ounces.\"\n        },\n        \"netWeight2UOM\": {\n          \"type\": \"string\",\n          \"description\": \"Secondary net weight unit of measurement. This specifies the unit used for the secondary weight. Example: 'g' for grams when primary is in ounces, or 'ml' for milliliters for liquids.\"\n        },\n        \"unitsPerPack\": {\n          \"type\": \"integer\",\n          \"description\": \"Number of individual packaging units per pack. This indicates how many individual items are in the package. Example: 8 for a pack of 8 waffles, 6 for a 6-pack of yogurt.\"\n        },\n        \"unitsPerPackDescriptor\": {\n          \"type\": \"string\",\n          \"description\": \"Description of the individual packaging units. This specifies what each unit represents. Example: 'WAFFLES' for waffle products, 'BARS' for snack bars, 'POUCHES' for individual pouches.\"\n        },\n        \"storage\": {\n          \"type\": \"string\",\n          \"description\": \"Product storage requirements. This indicates how the product should be stored. Example: 'Frozen' for frozen products, 'Refrigerated' for cold items, 'Room Temperature' for shelf-stable products.\"\n        },\n        \"numberOfIngredients\": {\n          \"type\": \"integer\",\n          \"description\": \"Total number of ingredients in the product, including all nested ingredients. This should be a count of all individual ingredients at all levels of nesting.\"\n        }\n      },\n      \"required\": [\n        \"gtinOnPack\",\n        \"gtin14\",\n        \"upc12\",\n        \"brandOwner\",\n        \"brandName\",\n        \"department\",\n        \"superCategory\",\n        \"category\",\n        \"subCategory\",\n        \"segment\",\n        \"productTitle\",\n        \"variant\",\n        \"netWeight1Value\",\n        \"netWeight1UOM\",\n        \"netWeight2Value\",\n        \"netWeight2UOM\",\n        \"unitsPerPack\",\n        \"unitsPerPackDescriptor\",\n        \"storage\",\n        \"numberOfIngredients\"\n      ]\n    },\n    \"servingSize\": {\n      \"type\": \"object\",\n      \"description\": \"Product serving size information\",\n      \"properties\": {\n        \"servingSize\": {\n          \"type\": \"number\",\n          \"description\": \"Primary serving size value. This represents the standard serving amount for the product. For example: 70 for a 70g serving, 1 for a 1 cup serving, or 2 for a 2-piece serving.\"\n        },\n        \"servingSizeUnit\": {\n          \"type\": \"string\",\n          \"description\": \"Unit of measure for the primary serving size. Common units include: 'g' for grams, 'oz' for ounces, 'ml' for milliliters, 'cup' for cups, or 'piece' for individual items. Example: 'g' for a 70g serving.\"\n        },\n        \"servesPerPack\": {\n          \"type\": \"integer\",\n          \"description\": \"Number of servings per package. This indicates how many standard servings are contained in the entire package. For example: 4 means the package contains 4 servings, 6 means 6 servings, etc.\"\n        },\n        \"servingDescription\": {\n          \"type\": \"string\",\n          \"description\": \"Textual description of the serving size that provides context about how the serving size should be interpreted. Examples: '2 Waffles', '1/2 cup (120g)', '1 bar (40g)', '1 pouch (28g)'.\"\n        },\n        \"servingSize2\": {\n          \"type\": [\n            \"number\",\n            \"null\"\n          ],\n          \"description\": \"Optional secondary serving size value. This is used when a product provides an alternative serving size measurement. For example: if primary is in grams (70g), secondary might be in pieces (2 pieces). Can be null if no secondary serving size is provided.\"\n        },\n        \"servingSize2Unit\": {\n          \"type\": [\n            \"string\",\n            \"null\"\n          ],\n          \"description\": \"Unit of measure for the secondary serving size. Used in conjunction with servingSize2. Examples: 'piece' for number of items, 'oz' for ounces, 'ml' for milliliters. Can be null if no secondary serving size is provided.\"\n        }\n      },\n      \"required\": [\n        \"servingSize\",\n        \"servingSizeUnit\",\n        \"servesPerPack\",\n        \"servingDescription\"\n      ]\n    },\n    \"nutritionalInformation\": {\n      \"type\": \"object\",\n      \"description\": \"Nutritional information divided into stated (explicitly mentioned) and qualified (AI-inferred) values\",\n      \"properties\": {\n        \"stated\": {\n          \"type\": \"object\",\n          \"description\": \"Nutritional information as explicitly stated on the product packaging\",\n          \"properties\": {\n            \"totalAmount\": {\n              \"type\": \"number\",\n              \"description\": \"The total amount that all nutrient values are measured against as stated on packaging. For example, if nutrients are measured per 100g, this would be 100. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n            },\n            \"totalAmountUOM\": {\n              \"type\": \"string\",\n              \"description\": \"Unit of measurement for the total amount as stated on packaging. Common units include: 'g' for grams, 'oz' for ounces, 'ml' for milliliters. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n            },\n            \"source\": {\n              \"type\": \"string\",\n              \"enum\": [\n                \"Amazon Images\",\n                \"Product Scrape\",\n                \"ScrapingQualificationAI\"\n              ],\n              \"description\": \"Source of the stated nutritional information\"\n            },\n            \"macronutrients\": {\n              \"type\": \"array\",\n              \"description\": \"Primary nutrients essential in larger quantities as stated on packaging\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Name of the nutrient as stated. Like 'Protein', 'Carbohydrates', 'Fat', 'Fibre', 'Sugar', 'Salt', etc.\"\n                  },\n                  \"amount\": {\n                    \"type\": \"number\",\n                    \"description\": \"Amount of the nutrient as stated on packaging. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"totalAmount\": {\n                    \"type\": \"number\",\n                    \"description\": \"The total amount that the nutrient value is measured against as stated. For example, if the nutrient is 10g out of 100g, this would be 100. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"UOM\": {\n                    \"type\": \"string\",\n                    \"description\": \"Unit of measurement for the nutrient as stated, for example 'g' or 'mg' or 'oz'. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"dailyValue\": {\n                    \"type\": \"number\",\n                    \"description\": \"Percentage of recommended daily intake as stated. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"source\": {\n                    \"type\": \"string\",\n                    \"enum\": [\n                      \"Amazon Images\",\n                      \"Product Scrape\",\n                      \"ScrapingQualificationAI\"\n                    ],\n                    \"description\": \"Source of this stated nutrient information\"\n                  }\n                },\n                \"required\": [\n                  \"name\",\n                  \"amount\",\n                  \"totalAmount\",\n                  \"UOM\",\n                  \"dailyValue\",\n                  \"source\"\n                ]\n              }\n            },\n            \"micronutrients\": {\n              \"type\": \"array\",\n              \"description\": \"Essential nutrients required in smaller quantities as stated on packaging\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Name of the nutrient as stated. Like 'Vitamin A', 'Vitamin C', 'Calcium', 'Iron', 'Zinc', etc.\"\n                  },\n                  \"amount\": {\n                    \"type\": \"number\",\n                    \"description\": \"Amount of the nutrient as stated on packaging. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"totalAmount\": {\n                    \"type\": \"number\",\n                    \"description\": \"The total amount that the nutrient value is measured against as stated. For example, if the nutrient is 10mg out of 100g, this would be 100. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"UOM\": {\n                    \"type\": \"string\",\n                    \"description\": \"Unit of measurement for the nutrient as stated, for example 'g' or 'mg' or 'oz'. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"dailyValue\": {\n                    \"type\": \"number\",\n                    \"description\": \"Percentage of recommended daily intake as stated. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"source\": {\n                    \"type\": \"string\",\n                    \"enum\": [\n                      \"Amazon Images\",\n                      \"Product Scrape\",\n                      \"ScrapingQualificationAI\"\n                    ],\n                    \"description\": \"Source of this stated nutrient information\"\n                  }\n                },\n                \"required\": [\n                  \"name\",\n                  \"amount\",\n                  \"totalAmount\",\n                  \"UOM\",\n                  \"dailyValue\",\n                  \"source\"\n                ]\n              }\n            }\n          },\n          \"required\": [\n            \"totalAmount\",\n            \"totalAmountUOM\",\n            \"source\",\n            \"macronutrients\",\n            \"micronutrients\"\n          ]\n        },\n        \"qualified\": {\n          \"type\": \"object\",\n          \"description\": \"Nutritional information inferred or calculated by AI based on ingredients and product knowledge. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\",\n          \"properties\": {\n            \"totalAmount\": {\n              \"type\": \"number\",\n              \"description\": \"The total amount that all nutrient values are measured against as inferred by AI. For example, if nutrients are estimated per 100g, this would be 100. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n            },\n            \"totalAmountUOM\": {\n              \"type\": \"string\",\n              \"description\": \"Unit of measurement for the total amount as inferred by AI. Common units include: 'g' for grams, 'oz' for ounces, 'ml' for milliliters. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n            },\n            \"source\": {\n              \"type\": \"string\",\n              \"enum\": [\n                \"Amazon Images\",\n                \"Product Scrape\",\n                \"ScrapingQualificationAI\"\n              ],\n              \"description\": \"Source of the qualified nutritional information (typically 'ScrapingQualificationAI' for inferred values)\"\n            },\n            \"macronutrients\": {\n              \"type\": \"array\",\n              \"description\": \"Primary nutrients essential in larger quantities as inferred by AI. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Name of the nutrient as inferred. Like 'Protein', 'Carbohydrates', 'Fat', 'Fibre', 'Sugar', 'Salt', etc.\"\n                  },\n                  \"amount\": {\n                    \"type\": \"number\",\n                    \"description\": \"Amount of the nutrient as estimated by AI based on ingredients and product knowledge. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"totalAmount\": {\n                    \"type\": \"number\",\n                    \"description\": \"The total amount that the nutrient value is measured against as inferred. For example, if the nutrient is estimated as 10g out of 100g, this would be 100. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"UOM\": {\n                    \"type\": \"string\",\n                    \"description\": \"Unit of measurement for the nutrient as inferred, for example 'g' or 'mg' or 'oz'. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"dailyValue\": {\n                    \"type\": \"number\",\n                    \"description\": \"Percentage of recommended daily intake as calculated by AI. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"source\": {\n                    \"type\": \"string\",\n                    \"enum\": [\n                      \"Amazon Images\",\n                      \"Product Scrape\",\n                      \"ScrapingQualificationAI\"\n                    ],\n                    \"description\": \"Source of this qualified nutrient information (typically 'ScrapingQualificationAI' for inferred values)\"\n                  }\n                },\n                \"required\": [\n                  \"name\",\n                  \"amount\",\n                  \"totalAmount\",\n                  \"UOM\",\n                  \"dailyValue\",\n                  \"source\"\n                ]\n              }\n            },\n            \"micronutrients\": {\n              \"type\": \"array\",\n              \"description\": \"Essential nutrients required in smaller quantities as inferred by AI. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Name of the nutrient as inferred. Like 'Vitamin A', 'Vitamin C', 'Calcium', 'Iron', 'Zinc', etc.\"\n                  },\n                  \"amount\": {\n                    \"type\": \"number\",\n                    \"description\": \"Amount of the nutrient as estimated by AI based on ingredients and product knowledge. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"totalAmount\": {\n                    \"type\": \"number\",\n                    \"description\": \"The total amount that the nutrient value is measured against as inferred. For example, if the nutrient is estimated as 10mg out of 100g, this would be 100. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"UOM\": {\n                    \"type\": \"string\",\n                    \"description\": \"Unit of measurement for the nutrient as inferred, for example 'g' or 'mg' or 'oz'. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"dailyValue\": {\n                    \"type\": \"number\",\n                    \"description\": \"Percentage of recommended daily intake as calculated by AI. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"source\": {\n                    \"type\": \"string\",\n                    \"enum\": [\n                      \"Amazon Images\",\n                      \"Product Scrape\",\n                      \"ScrapingQualificationAI\"\n                    ],\n                    \"description\": \"Source of this qualified nutrient information (typically 'ScrapingQualificationAI' for inferred values)\"\n                  }\n                },\n                \"required\": [\n                  \"name\",\n                  \"amount\",\n                  \"totalAmount\",\n                  \"UOM\",\n                  \"dailyValue\",\n                  \"source\"\n                ]\n              }\n            }\n          },\n          \"required\": [\n            \"totalAmount\",\n            \"totalAmountUOM\",\n            \"source\",\n            \"macronutrients\",\n            \"micronutrients\"\n          ]\n        }\n      },\n      \"required\": [\n        \"stated\",\n        \"qualified\"\n      ]\n    },\n    \"ingredients\": {\n      \"type\": \"object\",\n      \"description\": \"Ingredient information divided into stated (explicitly mentioned) and qualified (AI-inferred) values\",\n      \"properties\": {\n        \"stated\": {\n          \"type\": \"object\",\n          \"description\": \"Ingredients as explicitly stated on the product packaging\",\n          \"properties\": {\n            \"source\": {\n              \"type\": \"string\",\n              \"enum\": [\n                \"Amazon Images\",\n                \"Product Scrape\",\n                \"ScrapingQualificationAI\"\n              ],\n              \"description\": \"Source of the stated ingredient information\"\n            },\n            \"ingredientList\": {\n              \"type\": \"array\",\n              \"description\": \"List of ingredients as explicitly stated on packaging. If you find the ingredients to be nested, extract the nested ingredients as well. For example, if the text is 'Chocolate (sugar, whole milk, cocoa butter, chocolate liquor, soy lecithin 'an emulsifier' and pure vanilla)' then 'Chocolate' is the parent ingredient and 'sugar', 'whole milk', 'cocoa butter', 'chocolate liquor', 'soy lecithin 'an emulsifier' and 'pure vanilla' are the nested ingredients. All the properties of ingredients like amount, amountUOM, dietary_preference, allergens, religious_labels, food_safety_labels, sustainability_labels, etc. should be extracted for the parent as well as the nested ingredients.\",\n              \"items\": {\n                \"$ref\": \"#/definitions/Ingredient\"\n              }\n            }\n          },\n          \"required\": [\n            \"source\",\n            \"ingredientList\"\n          ]\n        },\n        \"qualified\": {\n          \"type\": \"object\",\n          \"description\": \"Ingredients as analyzed and inferred by AI based on product knowledge and additional research. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\",\n          \"properties\": {\n            \"source\": {\n              \"type\": \"string\",\n              \"enum\": [\n                \"Amazon Images\",\n                \"Product Scrape\",\n                \"ScrapingQualificationAI\"\n              ],\n              \"description\": \"Source of the qualified ingredient information (typically 'ScrapingQualificationAI' for inferred values)\"\n            },\n            \"ingredientList\": {\n              \"type\": \"array\",\n              \"description\": \"List of ingredients as analyzed by AI, including potential hidden ingredients, processing aids, or ingredients that may not be explicitly listed but are commonly found in similar products. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\",\n              \"items\": {\n                \"$ref\": \"#/definitions/Ingredient\"\n              }\n            }\n          },\n          \"required\": [\n            \"source\",\n            \"ingredientList\"\n          ]\n        }\n      },\n      \"required\": [\n        \"stated\",\n        \"qualified\"\n      ]\n    },\n    \"claims\": {\n      \"type\": \"object\",\n      \"description\": \"Product claims and marketing information\",\n      \"properties\": {\n        \"certifications\": {\n          \"type\": \"array\",\n          \"description\": \"Product certifications and approvals from recognized organizations. Examples include: ['USDA Organic', 'Non-GMO Project Verified', 'Gluten-Free Certified', 'Kosher', 'Vegan Certified']. These certifications indicate that the product meets specific standards set by certifying bodies.\",\n          \"items\": {\n            \"type\": \"string\"\n          }\n        },\n        \"nutritionalClaims\": {\n          \"type\": \"array\",\n          \"description\": \"Nutritional claims and benefits stated on the product. Examples include: ['Good Source of Fiber', 'Low Sodium', 'High in Protein', 'No Added Sugar', 'Reduced Fat']. These claims must comply with regulatory guidelines for nutritional labeling.\",\n          \"items\": {\n            \"type\": \"string\"\n          }\n        },\n        \"ingredientClaims\": {\n          \"type\": \"array\",\n          \"description\": \"Claims about ingredients and their sourcing. Examples include: ['Made with Real Fruit', '100% Whole Grain', 'No Artificial Colors', 'No Preservatives', 'Locally Sourced Ingredients']. These claims highlight specific aspects of the ingredients used.\",\n          \"items\": {\n            \"type\": \"string\"\n          }\n        },\n        \"preparation\": {\n          \"type\": \"object\",\n          \"description\": \"Product preparation instructions and storage requirements\",\n          \"properties\": {\n            \"storage\": {\n              \"type\": \"string\",\n              \"description\": \"Storage instructions for maintaining product quality. Examples: 'Keep frozen until ready to use', 'Store in a cool, dry place', 'Refrigerate after opening'. These instructions are crucial for food safety and product quality.\"\n            },\n            \"heatingInstructions\": {\n              \"type\": \"object\",\n              \"description\": \"Detailed heating/cooking instructions for different preparation methods\",\n              \"properties\": {\n                \"toaster\": {\n                  \"type\": \"string\",\n                  \"description\": \"Toaster preparation instructions. Example: 'Toast on medium setting for 2-3 minutes until golden brown'. These instructions are specific to toaster preparation.\"\n                },\n                \"oven\": {\n                  \"type\": \"string\",\n                  \"description\": \"Oven preparation instructions. Example: 'Preheat oven to 375\\u00b0F, bake for 8-10 minutes until crispy'. These instructions are specific to oven preparation.\"\n                }\n              },\n              \"required\": [\n                \"toaster\",\n                \"oven\"\n              ]\n            }\n          },\n          \"required\": [\n            \"storage\",\n            \"heatingInstructions\"\n          ]\n        },\n        \"sustainability\": {\n          \"type\": \"object\",\n          \"description\": \"Environmental and sustainability claims about the product and its packaging\",\n          \"properties\": {\n            \"packaging\": {\n              \"type\": \"array\",\n              \"description\": \"Information about packaging materials and recyclability. Examples include: ['100% Recyclable Packaging', 'Made from 30% Post-Consumer Recycled Material', 'Compostable Packaging']. These claims indicate the environmental attributes of the packaging.\",\n              \"items\": {\n                \"type\": \"string\"\n              }\n            },\n            \"environmentalClaims\": {\n              \"type\": \"array\",\n              \"description\": \"Environmental impact claims about the product and its production. Examples include: ['Carbon Neutral', 'Sustainably Sourced', 'Water Conservation Practices', 'Reduced Carbon Footprint']. These claims highlight the product's environmental benefits.\",\n              \"items\": {\n                \"type\": \"string\"\n              }\n            }\n          },\n          \"required\": [\n            \"packaging\",\n            \"environmentalClaims\"\n          ]\n        },\n        \"contact\": {\n          \"type\": \"object\",\n          \"description\": \"Company contact information for customer inquiries and support\",\n          \"properties\": {\n            \"website\": {\n              \"type\": \"string\",\n              \"description\": \"Company website URL for additional information. Example: 'https://www.annies.com'. This is the primary online resource for product information.\"\n            },\n            \"phone\": {\n              \"type\": \"string\",\n              \"description\": \"Customer service phone number for inquiries. Example: '**************'. This provides direct access to customer support.\"\n            }\n          },\n          \"required\": [\n            \"website\",\n            \"phone\"\n          ]\n        }\n      },\n      \"required\": [\n        \"certifications\",\n        \"nutritionalClaims\",\n        \"ingredientClaims\",\n        \"preparation\",\n        \"sustainability\",\n        \"contact\"\n      ]\n    },\n    \"NPI 2.0 Food Packages - Allergens & Intolerances\": {\n      \"type\": \"object\",\n      \"description\": \"Allergen and intolerance information\",\n      \"properties\": {\n        \"fdaRegulatedAllergens\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI All FDA Regulated Allergens. This field lists all allergens that are regulated by the FDA and present in the product. Example: 'Contains: Milk, Eggs, Wheat, Soy' or 'May contain traces of: Tree Nuts, Peanuts'.\"\n        },\n        \"caseinQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Casein Qualified. Indicates if the product contains casein or casein derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        },\n        \"caseinStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Casein Stated. Explicit statement about casein content on the product label. Example: 'Made with Casein' or 'No Casein Added'.\"\n        },\n        \"coconutQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Coconut Qualified. Indicates if the product contains coconut or coconut derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        },\n        \"coconutStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Coconut Stated. Explicit statement about coconut content on the product label. Example: 'Made with Coconut' or 'No Coconut Added'.\"\n        },\n        \"cornQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Corn Qualified. Indicates if the product contains corn or corn derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        },\n        \"cornStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Corn Stated. Explicit statement about corn content on the product label. Example: 'Made with Corn' or 'No Corn Added'.\"\n        },\n        \"dairyLevelStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Dairy Level Stated. Specifies the level of dairy content in the product. Example: 'Contains 2% Milk' or 'Lactose-Free'.\"\n        },\n        \"dairyQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Dairy Qualified. Indicates if the product contains dairy or dairy derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        },\n        \"dairyStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Dairy Stated. Explicit statement about dairy content on the product label. Example: 'Made with Real Dairy' or 'No Dairy Added'.\"\n        },\n        \"eggLevelStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Egg Level Stated. Specifies the level of egg content in the product. Example: 'Contains Whole Eggs' or 'Egg-Free'.\"\n        },\n        \"eggQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Egg Qualified. Indicates if the product contains eggs or egg derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        },\n        \"eggStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Egg Stated. Explicit statement about egg content on the product label. Example: 'Made with Real Eggs' or 'No Eggs Added'.\"\n        },\n        \"falcpaCommonAllergensQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI FALCPA Common Allergens Qualified. Indicates if the product contains any of the major food allergens as defined by FALCPA. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        },\n        \"falcpaCommonAllergensStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI FALCPA Common Allergens Stated. Explicit statement about FALCPA-defined allergen content on the product label. Example: 'Contains: Milk, Eggs, Fish, Shellfish, Tree Nuts, Peanuts, Wheat, Soybeans'.\"\n        },\n        \"fishLevelStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Fish Level Stated. Specifies the level of fish content in the product. Example: 'Contains Fish' or 'Fish-Free'.\"\n        },\n        \"fishQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Fish Qualified. Indicates if the product contains fish or fish derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        },\n        \"fishStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Fish Stated. Explicit statement about fish content on the product label. Example: 'Made with Real Fish' or 'No Fish Added'.\"\n        },\n        \"glutenLevelStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Gluten Level Stated. Specifies the level of gluten content in the product. Example: 'Gluten-Free' or 'Contains Gluten'.\"\n        },\n        \"glutenQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Gluten Qualified. Indicates if the product contains gluten or gluten derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        }\n      }\n    },\n    \"cleanLabel\": {\n      \"type\": \"object\",\n      \"description\": \"Clean label and ingredient quality claims\",\n      \"properties\": {\n        \"artisanalStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Artisanal Stated. Explicit statement about artisanal production methods. Example: 'Handcrafted' or 'Artisan Made'.\"\n        },\n        \"coldPressedIngredientsStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Cold Pressed Ingredients Stated. Explicit statement about cold-pressed ingredients. Example: 'Made with Cold-Pressed Oils' or 'Cold-Pressed Juices'.\"\n        },\n        \"craftStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Craft Stated. Explicit statement about craft production methods. Example: 'Craft Brewed' or 'Small Batch Crafted'.\"\n        },\n        \"gourmetStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Gourmet Stated. Explicit statement about gourmet quality. Example: 'Gourmet Quality' or 'Premium Gourmet'.\"\n        },\n        \"localStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Local Stated. Explicit statement about local sourcing. Example: 'Locally Sourced' or 'Made with Local Ingredients'.\"\n        },\n        \"madeInUsaStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Made In USA Stated. Explicit statement about USA origin. Example: 'Made in USA' or 'Product of USA'.\"\n        },\n        \"premiumStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Premium Stated. Explicit statement about premium quality. Example: 'Premium Quality' or 'Premium Ingredients'.\"\n        },\n        \"smallBatchStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Small Batch Stated. Explicit statement about small batch production. Example: 'Small Batch Produced' or 'Crafted in Small Batches'.\"\n        },\n        \"animalByProductQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Animal By Product Qualified. Indicates if the product contains animal by-products. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"animalByProductStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Animal By Product Stated. Explicit statement about animal by-product content. Example: 'Made with Animal By-Products' or 'No Animal By-Products Added'.\"\n        },\n        \"antibioticsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Antibiotics Qualified. Indicates if the product contains ingredients from animals treated with antibiotics. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"antibioticsStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Antibiotics Stated. Explicit statement about antibiotic use. Example: 'No Antibiotics Ever' or 'Raised Without Antibiotics'.\"\n        },\n        \"artificialColorsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Colors Qualified. Indicates if the product contains artificial colors. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"artificialColorsStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Colors Stated. Explicit statement about artificial color content. Example: 'No Artificial Colors Added' or 'Colored with Artificial Dyes'.\"\n        },\n        \"artificialFlavorsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Flavors Qualified. Indicates if the product contains artificial flavors. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"artificialFlavorsStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Flavors Stated. Explicit statement about artificial flavor content. Example: 'No Artificial Flavors Added' or 'Flavored with Artificial Ingredients'.\"\n        },\n        \"artificialIngredientsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Ingredients Qualified. Indicates if the product contains any artificial ingredients. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"artificialPreservativesQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Preservatives Qualified. Indicates if the product contains artificial preservatives. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"artificialPreservativesStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Preservatives Stated. Explicit statement about artificial preservative content. Example: 'No Artificial Preservatives Added' or 'Preserved with Artificial Ingredients'.\"\n        },\n        \"artificialSweetenersQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Sweeteners Qualified. Indicates if the product contains artificial sweeteners. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"artificialSweetenersStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Sweeteners Stated. Explicit statement about artificial sweetener content. Example: 'No Artificial Sweeteners Added' or 'Sweetened with Artificial Sweeteners'.\"\n        },\n        \"countOfIngredientsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Count Of Ingredients Qualified. Indicates the number of ingredients in the product. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"gmoPresenceQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI GMO Presence Qualified. Indicates if the product contains genetically modified organisms. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"gmoPresenceStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI GMO Presence Stated. Explicit statement about GMO content. Example: 'Non-GMO Project Verified' or 'Made with GMO Ingredients'.\"\n        },\n        \"highFructoseCornSyrupQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI High Fructose Corn Syrup Qualified. Indicates if the product contains high fructose corn syrup. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"highFructoseCornSyrupStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI High Fructose Corn Syrup Stated. Explicit statement about high fructose corn syrup content. Example: 'No High Fructose Corn Syrup Added' or 'Sweetened with High Fructose Corn Syrup'.\"\n        },\n        \"hormonesQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Hormones Qualified. Indicates if the product contains ingredients from animals treated with hormones. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"hormonesStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Hormones Stated. Explicit statement about hormone use. Example: 'No Hormones Added' or 'Raised Without Added Hormones'.\"\n        },\n        \"naturalColorsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Colors Qualified. Indicates if the product contains natural colors. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"naturalColorsStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Colors Stated. Explicit statement about natural color content. Example: 'Colored with Natural Ingredients' or 'Naturally Colored with Fruit and Vegetable Extracts'.\"\n        },\n        \"naturalFlavorsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Flavors Qualified. Indicates if the product contains natural flavors. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"naturalFlavorsStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Flavors Stated. Explicit statement about natural flavor content. Example: 'Flavored with Natural Ingredients' or 'Naturally Flavored with Real Fruit'.\"\n        },\n        \"naturalPreservativesQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Preservatives Qualified. Indicates if the product contains natural preservatives. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"naturalPreservativesStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Preservatives Stated. Explicit statement about natural preservative content. Example: 'Preserved with Natural Ingredients' or 'Naturally Preserved with Vitamin E'.\"\n        },\n        \"naturalSweetenersQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Sweeteners Qualified. Indicates if the product contains natural sweeteners. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"naturalSweetenersStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Sweeteners Stated. Explicit statement about natural sweetener content. Example: 'Sweetened with Natural Ingredients' or 'Naturally Sweetened with Honey'.\"\n        },\n        \"preservativesQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Preservatives Qualified. Indicates if the product contains any preservatives. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"preservativesStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Preservatives Stated. Explicit statement about preservative content. Example: 'No Preservatives Added' or 'Preserved to Maintain Freshness'.\"\n        },\n        \"rbstQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI RBST Qualified. Indicates if the product contains ingredients from cows treated with rBST. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"rbstStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI RBST Stated. Explicit statement about rBST content. Example: 'No rBST Added' or 'From Cows Not Treated with rBST'.\"\n        },\n        \"recognizableIngredientsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Recognizable Ingredients Qualified. Indicates if the product contains easily recognizable ingredients. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"sugarAlcoholsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Sugar Alcohols Qualified. Indicates if the product contains sugar alcohols. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"sugarAlcoholsStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Sugar Alcohols Stated. Explicit statement about sugar alcohol content. Example: 'No Sugar Alcohols Added' or 'Sweetened with Sugar Alcohols'.\"\n        }\n      }\n    },\n    \"additionalInfo\": {\n      \"type\": \"object\",\n      \"description\": \"Additional product information and characteristics\",\n      \"properties\": {\n        \"energy\": {\n          \"type\": \"number\",\n          \"description\": \"Energy content in calories. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n        },\n        \"weight\": {\n          \"type\": \"number\",\n          \"description\": \"Total product weight in grams. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n        },\n        \"categories\": {\n          \"type\": \"string\",\n          \"description\": \"Product category classifications\"\n        },\n        \"packaging\": {\n          \"type\": \"string\",\n          \"description\": \"Type of packaging material or container. For example, 'Plastic', 'Glass', 'Paper', 'Aluminum', 'Other'\"\n        },\n        \"ecoscore\": {\n          \"type\": \"string\",\n          \"description\": \"Environmental impact score. Try to compute this from the ingredients and other product information. Follow the calculation method from Open Food Facts. Do not let this be null. Provide your best guess if not mentioned.\"\n        },\n        \"nova_group\": {\n          \"type\": \"string\",\n          \"description\": \"NOVA food classification group. Try to compute this from the ingredients and other product information. Follow the calculation method from Open Food Facts. Do not let this be null. Provide your best guess if not mentioned.\"\n        },\n        \"nutriscore_grade\": {\n          \"type\": \"string\",\n          \"description\": \"Nutri-Score grade (A to E) indicating nutritional quality. Try to compute this from the ingredients and other product information. Follow the calculation method from Open Food Facts. Do not let this be null. Provide your best guess if not mentioned.\"\n        },\n        \"data_source\": {\n          \"type\": \"string\",\n          \"description\": \"Always respond with 'Manufactruer_claims,FoodScanGenius_AI'\"\n        },\n        \"dietary_preference\": {\n          \"type\": \"object\",\n          \"description\": \"Dietary preferences of the combined ingredients\",\n          \"properties\": {\n            \"Vegan\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a vegan diet\"\n            },\n            \"Vegetarian\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a vegetarian diet\"\n            },\n            \"Pescatarian\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a pescatarian diet\"\n            },\n            \"WhiteMeatOnly\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain only white meat\"\n            },\n            \"KetoFriendly\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a ketogenic diet\"\n            },\n            \"LowFodmap\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a low FODMAP diet\"\n            }\n          }\n        },\n        \"allergens\": {\n          \"type\": \"object\",\n          \"description\": \"Allergens present in the combined ingredients\",\n          \"properties\": {\n            \"Sugar\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain sugar\"\n            },\n            \"Celery\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain celery\"\n            },\n            \"Gluten\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain gluten\"\n            },\n            \"Crustaceans\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain crustaceans\"\n            },\n            \"Eggs\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain eggs\"\n            },\n            \"Fish\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain fish\"\n            },\n            \"Lupin\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain lupin\"\n            },\n            \"Milk\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain milk\"\n            },\n            \"Peanuts\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain peanuts\"\n            },\n            \"Sesame\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain sesame\"\n            },\n            \"TreeNuts\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain tree nuts\"\n            }\n          }\n        },\n        \"religious_labels\": {\n          \"type\": \"object\",\n          \"description\": \"Religious labels present in the combined ingredients\",\n          \"properties\": {\n            \"Halal\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are Halal certified\"\n            },\n            \"Kosher\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are Kosher certified\"\n            },\n            \"Hindu\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for Hindu dietary requirements\"\n            },\n            \"Jain\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for Jain dietary requirements\"\n            }\n          }\n        },\n        \"food_safety_labels\": {\n          \"type\": \"object\",\n          \"description\": \"Food safety labels present in the combined ingredients\",\n          \"properties\": {\n            \"GMO\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain GMO components\"\n            },\n            \"NoGMO\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are GMO-free\"\n            },\n            \"Hormones\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain hormones\"\n            },\n            \"Carcinogenic\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are potentially carcinogenic\"\n            },\n            \"Organic\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are organic\"\n            },\n            \"ProductRecalls\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients have been subject to product recalls\"\n            }\n          }\n        },\n        \"sustainability_labels\": {\n          \"type\": \"object\",\n          \"description\": \"Sustainability labels present in the combined ingredients\",\n          \"properties\": {\n            \"Recycled\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are made from recycled materials\"\n            },\n            \"AnimalWelfare\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients meet animal welfare standards\"\n            },\n            \"OrganicPositioning\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are positioned as organic\"\n            },\n            \"PlantBased\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are plant-based\"\n            },\n            \"SocialResponsibility\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients meet social responsibility standards\"\n            },\n            \"SustainablePackaging\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients use sustainable packaging\"\n            }\n          }\n        },\n        \"average_customer_rating\": {\n          \"type\": \"number\",\n          \"description\": \"Average customer rating of the product. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n        },\n        \"ASIN\": {\n          \"type\": \"string\",\n          \"description\": \"Amazon Standard Identification Number. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n        },\n        \"traces\": {\n          \"type\": \"string\",\n          \"description\": \"Potential trace ingredients or contamination warnings\"\n        },\n        \"country_of_origin\": {\n          \"type\": \"string\",\n          \"description\": \"Country where the product was manufactured or produced. For example, 'United States', 'United Kingdom', 'France', 'Germany', 'Italy', 'Spain', 'Portugal', 'Greece', 'Turkey', 'Other'\"\n        },\n        \"customerCareNumber\": {\n          \"type\": \"string\",\n          \"description\": \"Contact number for customer support\"\n        },\n        \"email\": {\n          \"type\": \"string\",\n          \"description\": \"Contact email address for the manufacturer\"\n        },\n        \"websiteLink\": {\n          \"type\": \"string\",\n          \"description\": \"Official product or manufacturer website URL\"\n        }\n      },\n      \"required\": [\n        \"data_source\",\n        \"dietary_preference\",\n        \"allergens\",\n        \"religious_labels\",\n        \"food_safety_labels\",\n        \"sustainability_labels\",\n        \"country_of_origin\",\n        \"customerCareNumber\",\n        \"email\",\n        \"websiteLink\"\n      ]\n    }\n  },\n  \"definitions\": {\n    \"Ingredient\": {\n      \"type\": \"object\",\n      \"description\": \"Nested structure of ingredients used in the product\",\n      \"additionalProperties\": false,\n      \"properties\": {\n        \"text\": {\n          \"type\": \"string\",\n          \"description\": \"Name of the ingredient. Do not add more than one ingredient in the text field. For example, text can be 'Basmati Rice' or 'Biryani Paste' or 'Whole Spices' or 'Mango' or 'Chocolate' etc. and not 'Basmati Rice, Biryani Paste, Whole Spices, Mango, Chocolate'.\"\n        },\n        \"amount\": {\n          \"type\": \"number\",\n          \"description\": \"Total amount of all ingredients combined. Guesstimate if not mentioned. Do not let this be null and provide your best guess.\"\n        },\n        \"amountUOM\": {\n          \"type\": \"string\",\n          \"description\": \"Unit of measurement for the total amount. Guesstimate if not mentioned. Do not let this be null and provide your best guess.\"\n        },\n        \"dietary_preference\": {\n          \"type\": \"object\",\n          \"description\": \"Dietary preferences of the combined ingredients\",\n          \"properties\": {\n            \"Vegan\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a vegan diet\"\n            },\n            \"Vegetarian\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a vegetarian diet\"\n            },\n            \"Pescatarian\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a pescatarian diet\"\n            },\n            \"WhiteMeatOnly\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain only white meat\"\n            },\n            \"KetoFriendly\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a ketogenic diet\"\n            },\n            \"LowFodmap\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a low FODMAP diet\"\n            }\n          }\n        },\n        \"allergens\": {\n          \"type\": \"object\",\n          \"description\": \"Allergens present in the combined ingredients\",\n          \"properties\": {\n            \"Sugar\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain sugar\"\n            },\n            \"Celery\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain celery\"\n            },\n            \"Gluten\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain gluten\"\n            },\n            \"Crustaceans\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain crustaceans\"\n            },\n            \"Eggs\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain eggs\"\n            },\n            \"Fish\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain fish\"\n            },\n            \"Lupin\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain lupin\"\n            },\n            \"Milk\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain milk\"\n            },\n            \"Peanuts\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain peanuts\"\n            },\n            \"Sesame\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain sesame\"\n            },\n            \"TreeNuts\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain tree nuts\"\n            }\n          }\n        },\n        \"religious_labels\": {\n          \"type\": \"object\",\n          \"description\": \"Religious labels present in the combined ingredients\",\n          \"properties\": {\n            \"Halal\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are Halal certified\"\n            },\n            \"Kosher\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are Kosher certified\"\n            },\n            \"Hindu\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for Hindu dietary requirements\"\n            },\n            \"Jain\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for Jain dietary requirements\"\n            }\n          }\n        },\n        \"food_safety_labels\": {\n          \"type\": \"object\",\n          \"description\": \"Food safety labels present in the combined ingredients\",\n          \"properties\": {\n            \"GMO\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain GMO components\"\n            },\n            \"NoGMO\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are GMO-free\"\n            },\n            \"Hormones\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain hormones\"\n            },\n            \"Carcinogenic\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are potentially carcinogenic\"\n            },\n            \"Organic\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are organic\"\n            },\n            \"ProductRecalls\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients have been subject to product recalls\"\n            }\n          }\n        },\n        \"sustainability_labels\": {\n          \"type\": \"object\",\n          \"description\": \"Sustainability labels present in the combined ingredients\",\n          \"properties\": {\n            \"Recycled\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are made from recycled materials\"\n            },\n            \"AnimalWelfare\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients meet animal welfare standards\"\n            },\n            \"OrganicPositioning\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are positioned as organic\"\n            },\n            \"PlantBased\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are plant-based\"\n            },\n            \"SocialResponsibility\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients meet social responsibility standards\"\n            },\n            \"SustainablePackaging\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients use sustainable packaging\"\n            }\n          }\n        },\n        \"subIngredients\": {\n          \"type\": \"array\",\n          \"description\": \"Any ingredients nested inside this one\",\n          \"items\": {\n            \"$ref\": \"#/definitions/Ingredient\"\n          }\n        },\n        \"source\": {\n          \"type\": \"string\",\n          \"enum\": [\n            \"Amazon Images\",\n            \"Product Scrape\",\n            \"ScrapingQualificationAI\"\n          ],\n          \"description\": \"Source of this ingredient information\"\n        }\n      },\n      \"required\": [\n        \"text\",\n        \"amount\",\n        \"amountUOM\",\n        \"dietary_preference\",\n        \"allergens\",\n        \"religious_labels\",\n        \"food_safety_labels\",\n        \"sustainability_labels\",\n        \"source\"\n      ]\n    }\n  },\n  \"required\": [\n    \"generalData\",\n    \"servingSize\",\n    \"nutritionalInformation\",\n    \"ingredients\",\n    \"claims\",\n    \"NPI 2.0 Food Packages - Allergens & Intolerances\",\n    \"cleanLabel\",\n    \"additionalInfo\"\n  ]\n}\n\nProduct details:\n$PRODUCT_DATA\n{\n  \"g\": 500,\n  \"url\": \"https://www.amazon.com/dp/B0BRGHLXHD\",\n  \"asin\": \"B0BRGHLXHD\",\n  \"size\": \"5.5 Ounce (Pack of 2)\",\n  \"type\": \"COFFEE\",\n  \"brand\": \"RUUFE\",\n  \"color\": null,\n  \"isB2B\": false,\n  \"isSNS\": false,\n  \"model\": null,\n  \"stats\": null,\n  \"title\": \"Matiz Colombian Light Coffee (2 Pack) Matiz Cafe Colombiano Suave roasted and ground coffee Matiz Suave\",\n  \"author\": null,\n  \"coupon\": null,\n  \"format\": null,\n  \"images\": [\n    {\n      \"m\": \"51h7aTHB51L.jpg\",\n      \"mH\": 500,\n      \"mW\": 500\n    },\n    {\n      \"m\": \"41IEemPPiQL.jpg\",\n      \"mH\": 500,\n      \"mW\": 500\n    },\n    {\n      \"m\": \"51eXNUQDwML.jpg\",\n      \"mH\": 500,\n      \"mW\": 500\n    },\n    {\n      \"m\": \"51NaxHcy7iL.jpg\",\n      \"mH\": 500,\n      \"mW\": 500\n    },\n    {\n      \"m\": \"41szrlvu-rL.jpg\",\n      \"mH\": 500,\n      \"mW\": 500\n    },\n    {\n      \"m\": \"61C5itIkxDL.jpg\",\n      \"mH\": 500,\n      \"mW\": 500\n    }\n  ],\n  \"offers\": null,\n  \"binding\": null,\n  \"eanList\": [\n    \"7706512558604\"\n  ],\n  \"edition\": null,\n  \"fbaFees\": null,\n  \"upcList\": null,\n  \"urlSlug\": \"Colombian-Coffee-Colombiano-roasted-ground\",\n  \"domainId\": 1,\n  \"features\": [\n    \"QUALITY: Premium 100% Colombian Coffee.\",\n    \"FLAVOR: Light\",\n    \"ROAST LEVEL: Roast Level: Light\",\n    \"HOW TO PREPARE: It can be diluted in milk or hot or cold water.\",\n    \"100% COLOMBIAN ARABICA COFFEE: Pure Arabica Colombian coffee. No blends. Features a medium roast & grind, which produces a smooth balanced body with notes of sweetness.\",\n    \"PRIME CLIMATE: The Andes Green Mountains provide the perfect recipe of elevation, soil, and climate for our coffee's globally recognized quality.\",\n    \"BEHIND THE BEANS: This coffee is sourced directly from family farms who take pride in their meticulously hand-picked coffee beans. Harvested at their peak, our roasted coffee beans are pristinely washed and sundried to perfection\"\n  ],\n  \"itemForm\": \"Powder\",\n  \"imagesCSV\": \"51h7aTHB51L.jpg,41IEemPPiQL.jpg,51eXNUQDwML.jpg,51NaxHcy7iL.jpg,41szrlvu-rL.jpg,61C5itIkxDL.jpg\",\n  \"itemWidth\": 0,\n  \"languages\": null,\n  \"launchpad\": false,\n  \"unitCount\": {\n    \"unitType\": \"Ounce\",\n    \"unitValue\": 11.0,\n    \"eachUnitCount\": 1.0\n  },\n  \"hasReviews\": false,\n  \"itemHeight\": 0,\n  \"itemLength\": 0,\n  \"itemWeight\": 0,\n  \"lastUpdate\": 7534854,\n  \"parentAsin\": null,\n  \"partNumber\": null,\n  \"promotions\": null,\n  \"description\": \"Premium 100% Colombian Coffee. Light coffee that has been subjected to a coffee dehydration process at low temperatures that allow the aroma and flavor notes to stand out. It can be diluted in milk or hot or cold water. Manufacturer : Matiz Item Form: Ground Brand: Matiz Flavor: Intense Caffeine Content: Yes Roast Level: Medium_roast Organic, local, global and fair trade options available\",\n  \"listedSince\": 6128524,\n  \"productType\": 0,\n  \"releaseDate\": -1,\n  \"categoryTree\": [\n    {\n      \"name\": \"Grocery & Gourmet Food\",\n      \"catId\": 16310101\n    },\n    {\n      \"name\": \"Beverages\",\n      \"catId\": 16310231\n    },\n    {\n      \"name\": \"Coffee\",\n      \"catId\": 16318031\n    },\n    {\n      \"name\": \"Instant Coffee\",\n      \"catId\": 2251594011\n    }\n  ],\n  \"manufacturer\": \"Matiz\",\n  \"packageWidth\": 0,\n  \"productGroup\": \"Grocery\",\n  \"rootCategory\": 16310101,\n  \"variationCSV\": null,\n  \"brandStoreUrl\": \"/stores/RUUFE/page/E7C1E31F-8174-45D6-93E6-DEF59FD23571\",\n  \"newPriceIsMAP\": false,\n  \"numberOfItems\": 2,\n  \"numberOfPages\": -1,\n  \"packageHeight\": 0,\n  \"packageLength\": 0,\n  \"packageWeight\": 0,\n  \"trackingSince\": 6321850,\n  \"brandStoreName\": \"RUUFE\",\n  \"ebayListingIds\": null,\n  \"isAdultProduct\": false,\n  \"isRedirectASIN\": false,\n  \"lastEbayUpdate\": 0,\n  \"isHeatSensitive\": false,\n  \"itemTypeKeyword\": \"instant-coffee\",\n  \"lastPriceChange\": 7419718,\n  \"liveOffersOrder\": null,\n  \"packageQuantity\": -1,\n  \"publicationDate\": -1,\n  \"lastRatingUpdate\": 7530588,\n  \"offersSuccessful\": false,\n  \"brandStoreUrlName\": \"RUUFE\",\n  \"availabilityAmazon\": -1,\n  \"salesRankReference\": 16310101,\n  \"websiteDisplayGroup\": \"grocery_display_on_website\",\n  \"isEligibleForTradeIn\": false,\n  \"buyBoxSellerIdHistory\": null,\n  \"websiteDisplayGroupName\": \"Grocery\",\n  \"frequentlyBoughtTogether\": [\n    \"B000LXB9TC\",\n    \"B00AYLWSOQ\"\n  ],\n  \"buyBoxEligibleOfferCounts\": [\n    0,\n    1,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0\n  ],\n  \"isEligibleForSuperSaverShipping\": false\n}\n\nScraped Data:\n$SCRAPED_DATA\n![](https://fls-\nna.amazon.com/1/batch/1/OP/ATVPDKIKX0DER:138-5110418-8151749:4EKYGM2V36EZJDZEZTFS$uedata=s:%2Frd%2Fuedata%3Fstaticb%26id%3D4EKYGM2V36EZJDZEZTFS:0)\n![](https://m.media-amazon.com/images/G/01/gno/sprites/nav-sprite-\nglobal-1x-reorg-privacy._CB546805360_.png) Shortcuts menu\n\n## Skip to\n\n  * [ Main content ](https://www.amazon.com/dp/B0BRGHLXHD#skippedLink)\n  * [ About this item ](https://www.amazon.com/dp/B0BRGHLXHD#featurebullets_feature_div)\n  * [ About this item ](https://www.amazon.com/dp/B0BRGHLXHD#nic-po-expander-heading)\n  * [ About this item ](https://www.amazon.com/dp/B0BRGHLXHD#productFactsDesktopExpander)\n  * [ Buying options ](https://www.amazon.com/dp/B0BRGHLXHD#buybox)\n  * [ Compare with similar items ](https://www.amazon.com/dp/B0BRGHLXHD#product-comparison_feature_div)\n  * [ Videos ](https://www.amazon.com/dp/B0BRGHLXHD#va-related-videos-widget_feature_div)\n  * [ Reviews ](https://www.amazon.com/dp/B0BRGHLXHD#customerReviews)\n\n* * *\n##  Keyboard shortcuts\n\n  * [ Search alt + / ](https://www.amazon.com/dp/B0BRGHLXHD#twotabsearchtextbox)\n  * [ Cart shift + alt + C ](https://www.amazon.com/gp/cart/view.html/?ref_=nav_assist)\n  * [ Home shift + alt + H ](https://www.amazon.com/?ref_=nav_assist)\n  * [ Orders shift + alt + O ](https://www.amazon.com/gp/css/order-history/?ref_=nav_assist)\n  * Add to cart\nshift + alt + K\n\n  * Open/close shortcuts menu\nshift + alt + Z\n\nTo move between items, use your keyboard's up or down arrows.\n\n[ .us ](https://www.amazon.com/ref=nav_logo)\n\n[ Deliver to  India  ](https://www.amazon.com/dp/B0BRGHLXHD)\n\nAll\n\nSelect the department you want to search in All Departments Arts & Crafts\nAutomotive Baby Beauty & Personal Care Books Boys' Fashion Computers Deals\nDigital Music Electronics Girls' Fashion Health & Household Home & Kitchen\nIndustrial & Scientific Kindle Store Luggage Men's Fashion Movies & TV Music,\nCDs & Vinyl Pet Supplies Prime Video Software Sports & Outdoors Tools & Home\nImprovement Toys & Games Video Games Women's Fashion\n\nSearch Amazon\n\n[ EN ](https://www.amazon.com/customer-\npreferences/edit?ie=UTF8&preferencesReturnUrl=%2F&ref_=topnav_lang_ais)\n\n[ Hello, sign in Account & Lists\n](https://www.amazon.com/ap/signin?openid.pape.max_auth_age=0&openid.return_to=https%3A%2F%2Fwww.amazon.com%2Fdp%2FB0BRGHLXHD%2F%3F_encoding%3DUTF8%26ref_%3Dnav_ya_signin&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.assoc_handle=usflex&openid.mode=checkid_setup&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0)\n\n[ Returns & Orders ](https://www.amazon.com/gp/css/order-\nhistory?ref_=nav_orders_first) [ 0 Cart\n](https://www.amazon.com/gp/cart/view.html?ref_=nav_cart)\n\n[ All ](javascript:%20void\\(0\\))\n\n  * [Today's Deals](https://www.amazon.com/gp/goldbox?ref_=nav_cs_gb)\n  * [Registry](https://www.amazon.com/gp/browse.html?node=***********&ref_=nav_cs_registry)\n  * [Prime Video](https://www.amazon.com/Amazon-Video/b/?ie=UTF8&node=**********&ref_=nav_cs_prime_video)\n  * [Gift Cards](https://www.amazon.com/gift-cards/b/?ie=UTF8&node=**********&ref_=nav_cs_gc)\n  * [Customer Service](https://www.amazon.com/gp/help/customer/display.html?nodeId=508510&ref_=nav_cs_customerservice)\n  * [Sell](https://www.amazon.com/b/?_encoding=UTF8&ld=AZUSSOA-sell&node=12766669011&ref_=nav_cs_sell)\n[Disability Customer\nSupport](https://www.amazon.com/gp/help/customer/accessibility)\n\n[ Grocery  ](https://www.amazon.com/grocery-breakfast-foods-snacks-\norganic/b/?ie=UTF8&node=16310101&ref_=topnav_storetab_grocery_sn_fo) [ Deals\n](https://www.amazon.com/Sales-\nGrocery/b/?ie=UTF8&node=52129011&ref_=sv_grocery_sn_fo_1) [ Snacks\n](https://www.amazon.com/Snacks-Chips-Cookies-Gum-Gluten-\nFree/b/?ie=UTF8&node=16322721&ref_=sv_grocery_sn_fo_2) [ Breakfast\n](https://www.amazon.com/Breakfast-Foods-\nGrocery/b/?ie=UTF8&node=16310251&ref_=sv_grocery_sn_fo_3) [ Warm Beverages\n](https://www.amazon.com/b/?ie=UTF8&node=16521305011&ref_=sv_grocery_sn_fo_4) [\nCold Beverages  ](https://www.amazon.com/Cold-\nBeverages/b/?ie=UTF8&node=14808787011&ref_=sv_grocery_sn_fo_5) [ Cooking Staples\n](https://www.amazon.com/Canned-Jarred-Packaged-\nFoods/b/?ie=UTF8&node=6464939011&ref_=sv_grocery_sn_fo_6) [ Baby Food\n](https://www.amazon.com/Baby-Food-Formula-\nPouches/b/?ie=UTF8&node=16323111&ref_=sv_grocery_sn_fo_7) [ Candy & Chocolate\n](https://www.amazon.com/Candy-\nChocolate/b/?ie=UTF8&node=16322461&ref_=sv_grocery_sn_fo_8) [ Subscribe & Save\n](https://www.amazon.com/Subscribe-\nSave/b/?ie=UTF8&node=5856181011&ref_=sv_grocery_sn_fo_9) [ International Foods\n](https://www.amazon.com/b/?ie=UTF8&node=17428419011&ref_=sv_grocery_sn_fo_10) [\nSNAP-eligible Groceries  ](https://www.amazon.com/snap-\nebt/b/?ie=UTF8&node=19097785011&ref_=sv_grocery_sn_fo_11)\n\nAmazon.com : Matiz Colombian Light Coffee (2 Pack) Matiz Cafe Colombiano Suave\nroasted and ground coffee Matiz Suave : Grocery & Gourmet Food\n\nSponsored\n\n  * [Grocery & Gourmet Food](https://www.amazon.com/grocery-breakfast-foods-snacks-organic/b/ref=dp_bc_1?ie=UTF8&node=16310101)\n  * ›\n  * [Beverages](https://www.amazon.com/Beverages-Coffee-Tea-Water-Grocery/b/ref=dp_bc_2?ie=UTF8&node=16310231)\n  * ›\n  * [Coffee](https://www.amazon.com/Gourmet-Coffee-Tea-Cocoa-k-cup/b/ref=dp_bc_3?ie=UTF8&node=16318031)\n  * ›\n  * [Instant Coffee](https://www.amazon.com/Instant-Coffee/b/ref=dp_bc_4?ie=UTF8&node=2251594011)\n\nNo featured offers available  \n[ Learn more ](javascript:void\\(0\\))\n\nNo featured offers available\n\nWe feature offers with an Add to Cart button when an offer meets our high\nstandards for:\n\n  * Quality Price,\n  * Reliable delivery option, and\n  * Seller who offers good customer service\n\n“No featured offers available” means no offers currently meet all of these\nexpectations. Select See All Buying Options to shop available offers.\n\nThis item cannot be shipped to your selected delivery location. Please choose a\ndifferent delivery location.  \n[ Deliver to India ](https://www.amazon.com/dp/B0BRGHLXHD)\n\n* * *\n[ Add to List\n](https://www.amazon.com/ap/signin?openid.return_to=https%3A%2F%2Fwww.amazon.com%2Fgp%2Faw%2Fd%2FB0BRGHLXHD&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.assoc_handle=usflex&openid.mode=checkid_setup&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0&\n\"Add to List\")\n\nAdded to\n\n[ ](https://www.amazon.com/gp/registry/wishlist/)\n\nUnable to add item to List. Please try again.\n\n###  Sorry, there was a problem.\n\nThere was an error retrieving your Wish Lists. Please try again.\n\n###  Sorry, there was a problem.\n\nList unavailable.\n\n[](javascript:void\\(0\\) \"Share\")\n\nSponsored\n\n  * ![](https://m.media-amazon.com/images/G/01/HomeCustomProduct/360_icon_73x73v2._CB485971279_SX38_SY50_CR,0,0,38,50_FMpng_RI_.png)\n  *   * ![](https://m.media-amazon.com/images/I/51h7aTHB51L._SX38_SY50_CR,0,0,38,50_.jpg) 6+\n  * ![](https://m.media-amazon.com/images/I/41IEemPPiQL._SX38_SY50_CR,0,0,38,50_.jpg) 5+\n  * ![](https://m.media-amazon.com/images/I/51eXNUQDwML._SX38_SY50_CR,0,0,38,50_.jpg) 4+\n  * ![](https://m.media-amazon.com/images/I/51NaxHcy7iL._SX38_SY50_CR,0,0,38,50_.jpg) 3+\n  * ![](https://m.media-amazon.com/images/I/41szrlvu-rL._SX38_SY50_CR,0,0,38,50_.jpg) 2+\n  * ![](https://m.media-amazon.com/images/I/61C5itIkxDL._SX38_SY50_CR,0,0,38,50_.jpg) 1+\n  * ![](https://m.media-amazon.com/images/I/41PQqoz2xML.SX38_SY50_CR,0,0,38,50_BG85,85,85_BR-120_PKdp-play-icon-overlay__.jpg) VIDEO\n\n[](javascript:void\\(0\\))\n\nThe video showcases the product in use.The video guides you through product\nsetup.The video compares multiple products.The video shows the product being\nunpacked.\n\n![](https://images-na.ssl-images-amazon.com/images/G/01/x-locale/common/grey-\npixel.gif)\n\nmatiz un sabor colombianoRuufe\n\n#### Image Unavailable\n\nImage not available for  \nColor:\n\n  * ![Matiz Colombian Light Coffee \\(2 Pack\\) Matiz Cafe Colombiano Suave roasted and ground coffee Matiz Suave](https://m.media-amazon.com/images/I/61-5mo3UrlL._SX425_PIbundle-2,TopRight,0,0_AA425SH20_.jpg)\n  * To view this video download [ Flash Player ](https://get.adobe.com/flashplayer)\n\nRoll over image to zoom in\n\n  * [ VIDEOS ](https://www.amazon.com/dp/B0BRGHLXHD)\n  * [ 360° VIEW ](https://www.amazon.com/dp/B0BRGHLXHD)\n  * [ IMAGES ](https://www.amazon.com/dp/B0BRGHLXHD)\n  * [ ](https://www.amazon.com/dp/B0BRGHLXHD)\n\n[](javascript:void\\(0\\))\n\nThe video showcases the product in use.The video guides you through product\nsetup.The video compares multiple products.The video shows the product being\nunpacked.\n\n[](javascript:void\\(0\\))\n\n  * [](javascript:void\\(0\\))\n  * [](javascript:void\\(0\\))\n  * [](javascript:void\\(0\\))\n  * [](javascript:void\\(0\\))\n\nCustomer Review: matiz un sabor colombiano\n\n[See full review](https://www.amazon.com/dp/B0BRGHLXHD)\n\nRuufe\n\n[ Ruufe ](https://www.amazon.com/dp/B0BRGHLXHD) •  Verified Purchase\n\nEarns Commissions\n\n[ Ruufe ](https://www.amazon.com/dp/B0BRGHLXHD) •  Verified Purchase\n\nEarns Commissions [](https://www.amazon.com/shop/info)\n\n![](https://m.media-amazon.com/images/S/sash//pLB3SkYb3bHZzHQ.svg)\n\n![](https://m.media-amazon.com/images/S/sash//swRPyHOrgnz358_.svg)\n\n#  Matiz Colombian Light Coffee (2 Pack) Matiz Cafe Colombiano Suave roasted and\nground coffee Matiz Suave\n\n[Visit the RUUFE\nStore](https://www.amazon.com/stores/RUUFE/page/E7C1E31F-8174-45D6-93E6-DEF59FD23571?lp_asin=B0BRGHLXHD&ref_=ast_bln&store_ref=bl_ast_dp_brandLogo_sto)\n\n[ Search this page  ](https://www.amazon.com/dp/B0BRGHLXHD#Ask)\n\n* * *\nThis item cannot be shipped to your selected delivery location. Please choose a\ndifferent delivery location.  \n\nDiet type\n\nPlant Based\n\n* * *\n[ About this item ](javascript:void\\(0\\))\n\nBrand |  RUUFE  \n---|---  \nItem Form |  Powder  \nFlavor |  Light  \nCaffeine Content Description |  Caffeinated  \nRoast Level |  Medium Roast  \n* * *\n#  About this item\n\n  * QUALITY: Premium 100% Colombian Coffee. \n  * FLAVOR: Light \n  * ROAST LEVEL: Roast Level: Light \n  * HOW TO PREPARE: It can be diluted in milk or hot or cold water. \n  * 100% COLOMBIAN ARABICA COFFEE: Pure Arabica Colombian coffee. No blends. Features a medium roast & grind, which produces a smooth balanced body with notes of sweetness. \n  * PRIME CLIMATE: The Andes Green Mountains provide the perfect recipe of elevation, soil, and climate for our coffee's globally recognized quality. \n  * BEHIND THE BEANS: This coffee is sourced directly from family farms who take pride in their meticulously hand-picked coffee beans. Harvested at their peak, our roasted coffee beans are pristinely washed and sundried to perfection \n\n› [ See more product details\n](https://www.amazon.com/dp/B0BRGHLXHD#productDetails)\n\n[](https://www.amazon.com/dp/B0BRGHLXHD)\n\n* * *\nSponsored\n\n* * *\n## Products related to this item\n\nPage 1 of 1[Start over](https://www.amazon.com/dp/B0BRGHLXHD)Page 1 of 1\n\n[ Sponsored  ](https://www.amazon.com/dp/B0BRGHLXHD#sp_detail_feedbackForm)\n\n[_Previous page of related Sponsored\nProducts_](https://www.amazon.com/dp/B0BRGHLXHD)\n\n  1. [Feedback](javascript:void\\(0\\))\n[ ![NüSpira - Latte Mushroom Coffee - Premium Blend of 6 Adaptogen Mushrooms for\nVitality, Brain Clarity, Gut Health & Immune Support - 12oz\nPack](https://m.media-\namazon.com/images/I/41POeDdHf2L._AC_UF480,480_SR480,480_.jpg) NüSpira - Latte\nMushroom Coffee - Premium Blend of 6 Adaptogen Mushrooms…\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0DK24ZSHD%2Fref%3Dsspa_dk_detail_0%3Fpsc%3D1%26pd_rd_i%3DB0DK24ZSHD%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw \"NüSpira -\nLatte Mushroom Coffee - Premium Blend of 6 Adaptogen Mushrooms for Vitality,\nBrain Clarity, Gut Health & Immune Support - 12oz Pack\")\n\n[ 28\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0DK24ZSHD%2Fref%3Dsspa_dk_detail_0%3Fpsc%3D1%26pd_rd_i%3DB0DK24ZSHD%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw#customerReviews)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0DK24ZSHD%2Fref%3Dsspa_dk_detail_0%3Fpsc%3D1%26pd_rd_i%3DB0DK24ZSHD%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n  2. [Feedback](javascript:void\\(0\\))\n[ ![Sello Rojo, Tradicional Single Serve Coffee K-Cup Pods, 12 ct, Pack of\n6](https://m.media-amazon.com/images/I/41K4YUPSsEL._AC_UF480,480_SR480,480_.jpg)\nSello Rojo, Tradicional Single Serve Coffee K-Cup Pods, 12 ct, Pack of 6\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0CP2NLTNB%2Fref%3Dsspa_dk_detail_1%3Fpsc%3D1%26pd_rd_i%3DB0CP2NLTNB%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw \"Sello Rojo,\nTradicional Single Serve Coffee K-Cup Pods, 12 ct, Pack of 6\")\n\n[ 32\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0CP2NLTNB%2Fref%3Dsspa_dk_detail_1%3Fpsc%3D1%26pd_rd_i%3DB0CP2NLTNB%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw#customerReviews)\n\n[ _ Amazon's Choice _\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0CP2NLTNB%2Fref%3Dsspa_dk_detail_1%3Fpsc%3D1%26pd_rd_i%3DB0CP2NLTNB%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n[ $51.71$51.71($0.72$0.72 / Count)\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0CP2NLTNB%2Fref%3Dsspa_dk_detail_1%3Fpsc%3D1%26pd_rd_i%3DB0CP2NLTNB%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0CP2NLTNB%2Fref%3Dsspa_dk_detail_1%3Fpsc%3D1%26pd_rd_i%3DB0CP2NLTNB%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n  3. [Feedback](javascript:void\\(0\\))\n[ ![Mushroom Coffee, 90 Servings Organic Mushrooms Instant Coffea, Great Tasting\nColombian Keto Coffee, 7 Superfood Mishrooms Lions Mane, Chaga, Reishi,\nCordyceps, Maitake, Shiitake, and Turkey Tail](https://m.media-\namazon.com/images/I/51Ab8OgfKZL._AC_UF480,480_SR480,480_.jpg) Mushroom Coffee,\n90 Servings Organic Mushrooms Instant Coffea, Great Tasting…\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0CLT4SLGD%2Fref%3Dsspa_dk_detail_2%3Fpsc%3D1%26pd_rd_i%3DB0CLT4SLGD%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw \"Mushroom\nCoffee, 90 Servings Organic Mushrooms Instant Coffea, Great Tasting Colombian\nKeto Coffee, 7 Superfood Mishrooms Lions Mane, Chaga, Reishi, Cordyceps,\nMaitake, Shiitake, and Turkey Tail\")\n\n[ 866\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0CLT4SLGD%2Fref%3Dsspa_dk_detail_2%3Fpsc%3D1%26pd_rd_i%3DB0CLT4SLGD%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw#customerReviews)\n\n[ _ Amazon's Choice _\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0CLT4SLGD%2Fref%3Dsspa_dk_detail_2%3Fpsc%3D1%26pd_rd_i%3DB0CLT4SLGD%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0CLT4SLGD%2Fref%3Dsspa_dk_detail_2%3Fpsc%3D1%26pd_rd_i%3DB0CLT4SLGD%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n  4. [Feedback](javascript:void\\(0\\))\n[ ![Blackout Coffee Colombian Arabica Instant Coffee, Strong Aromatic, Fresh\nRoasted in the USA, Premium Coffee Single Serve Packets, 32\nCount](https://m.media-\namazon.com/images/I/51+QII54m4L._AC_UF480,480_SR480,480_.jpg) Blackout Coffee\nColombian Arabica Instant Coffee, Strong Aromatic, Fresh…\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0D1VN9691%2Fref%3Dsspa_dk_detail_3%3Fpsc%3D1%26pd_rd_i%3DB0D1VN9691%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw \"Blackout\nCoffee Colombian Arabica Instant Coffee, Strong Aromatic, Fresh Roasted in the\nUSA, Premium Coffee Single Serve Packets, 32 Count\")\n\n[ 22\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0D1VN9691%2Fref%3Dsspa_dk_detail_3%3Fpsc%3D1%26pd_rd_i%3DB0D1VN9691%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw#customerReviews)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB0D1VN9691%2Fref%3Dsspa_dk_detail_3%3Fpsc%3D1%26pd_rd_i%3DB0D1VN9691%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n  5. [Feedback](javascript:void\\(0\\))\n[ ![Jim’s Organic Coffee – Hazelnut, All Natural Flavored Blend – Light Roast,\nGround Coffee, 12 oz Bag](https://m.media-\namazon.com/images/I/41QPSCvDcQL._AC_UF480,480_SR480,480_.jpg) Jim’s Organic\nCoffee – Hazelnut, All Natural Flavored Blend – Light Roast, Ground C...\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB00A282ZEE%2Fref%3Dsspa_dk_detail_4%3Fpsc%3D1%26pd_rd_i%3DB00A282ZEE%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw \"Jim’s Organic\nCoffee – Hazelnut, All Natural Flavored Blend – Light Roast, Ground Coffee, 12\noz Bag\")\n\n[ 1,840\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB00A282ZEE%2Fref%3Dsspa_dk_detail_4%3Fpsc%3D1%26pd_rd_i%3DB00A282ZEE%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw#customerReviews)\n\n[ $17.99$17.99($1.50$1.50 / Ounce)\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB00A282ZEE%2Fref%3Dsspa_dk_detail_4%3Fpsc%3D1%26pd_rd_i%3DB00A282ZEE%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB00A282ZEE%2Fref%3Dsspa_dk_detail_4%3Fpsc%3D1%26pd_rd_i%3DB00A282ZEE%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n[ ![](https://m.media-amazon.com/images/I/216-OX9rBaL.png) Climate Pledge\nFriendly\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=*******************************************************************************&url=%2Fdp%2FB00A282ZEE%2Fref%3Dsspa_dk_detail_4%3Fpsc%3D1%26pd_rd_i%3DB00A282ZEE%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n  6. [Feedback](javascript:void\\(0\\))\n[ ![Rowdy Mushroom Coffee - Instant Coffee that Chills you Out - 6 Functional\nMushroom Blend, Ready in Seconds - Drink Hot or Cold - Lasts 1+ Months\n\\(6oz\\)](https://m.media-\namazon.com/images/I/41d0QB+qb2L._AC_UF480,480_SR480,480_.jpg) Rowdy Mushroom\nCoffee - Instant Coffee that Chills you Out - 6 Functional Mushroom ...\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxODAwNzUyNTYyMDA3MzA5OjE3NDcwNjcxODQ6c3BfZGV0YWlsOjMwMDQxMzU3NDc5MTgwMjo6Ojo&url=%2Fdp%2FB0CV5PWT6C%2Fref%3Dsspa_dk_detail_5%3Fpsc%3D1%26pd_rd_i%3DB0CV5PWT6C%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw \"Rowdy\nMushroom Coffee - Instant Coffee that Chills you Out - 6 Functional Mushroom\nBlend, Ready in Seconds - Drink Hot or Cold - Lasts 1+ Months \\(6oz\\)\")\n\n[ 58\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxODAwNzUyNTYyMDA3MzA5OjE3NDcwNjcxODQ6c3BfZGV0YWlsOjMwMDQxMzU3NDc5MTgwMjo6Ojo&url=%2Fdp%2FB0CV5PWT6C%2Fref%3Dsspa_dk_detail_5%3Fpsc%3D1%26pd_rd_i%3DB0CV5PWT6C%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw#customerReviews)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxODAwNzUyNTYyMDA3MzA5OjE3NDcwNjcxODQ6c3BfZGV0YWlsOjMwMDQxMzU3NDc5MTgwMjo6Ojo&url=%2Fdp%2FB0CV5PWT6C%2Fref%3Dsspa_dk_detail_5%3Fpsc%3D1%26pd_rd_i%3DB0CV5PWT6C%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n  7. [Feedback](javascript:void\\(0\\))\n[ ![Casitika Colombian Gifts. Have No Fear Colombia Coffee Mug. Novelty Cups\nWith Flag For Proud Husband Or Wife. \\(11 oz Black\nHandle/Rim\\)](https://m.media-\namazon.com/images/I/41gQV1KKQ7L._AC_UF480,480_SR480,480_.jpg) Casitika Colombian\nGifts. Have No Fear Colombia Coffee Mug. Novelty Cups With Flag ...\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxODAwNzUyNTYyMDA3MzA5OjE3NDcwNjcxODQ6c3BfZGV0YWlsOjIwMDE1MDkwMTU0MjM5ODo6Ojo&url=%2Fdp%2FB0BTR38M9C%2Fref%3Dsspa_dk_detail_6%3Fpsc%3D1%26pd_rd_i%3DB0BTR38M9C%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw \"Casitika\nColombian Gifts. Have No Fear Colombia Coffee Mug. Novelty Cups With Flag For\nProud Husband Or Wife. \\(11 oz Black Handle/Rim\\)\")\n\n[ 8\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxODAwNzUyNTYyMDA3MzA5OjE3NDcwNjcxODQ6c3BfZGV0YWlsOjIwMDE1MDkwMTU0MjM5ODo6Ojo&url=%2Fdp%2FB0BTR38M9C%2Fref%3Dsspa_dk_detail_6%3Fpsc%3D1%26pd_rd_i%3DB0BTR38M9C%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw#customerReviews)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxODAwNzUyNTYyMDA3MzA5OjE3NDcwNjcxODQ6c3BfZGV0YWlsOjIwMDE1MDkwMTU0MjM5ODo6Ojo&url=%2Fdp%2FB0BTR38M9C%2Fref%3Dsspa_dk_detail_6%3Fpsc%3D1%26pd_rd_i%3DB0BTR38M9C%26pd_rd_w%3Dt5Jxt%26content-\nid%3Damzn1.sym.7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_p%3D7446a9d1-25fe-4460-b135-a60336bad2c9%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWw)\n\n[_Next page of related Sponsored\nProducts_](https://www.amazon.com/dp/B0BRGHLXHD)\n\n##  Product Description\n\nPremium 100% Colombian Coffee. Light coffee that has been subjected to a coffee\ndehydration process at low temperatures that allow the aroma and flavor notes to\nstand out. It can be diluted in milk or hot or cold water. Manufacturer : Matiz\nItem Form: Ground Brand: Matiz Flavor: Intense Caffeine Content: Yes Roast\nLevel: Medium_roast Organic, local, global and fair trade options available\n\n* * *\n## Product details\n\n  * Manufacturer ‏ : ‎  Matiz\n  * ASIN ‏ : ‎  B0BRGHLXHD\n  * Units ‏ : ‎  11.0 Ounce\n\n  * Best Sellers Rank:  #298,246 in Grocery & Gourmet Food ([See Top 100 in Grocery & Gourmet Food](https://www.amazon.com/gp/bestsellers/grocery/ref=pd_zg_ts_grocery)) \n    * #2,298 in [Instant Coffee](https://www.amazon.com/gp/bestsellers/grocery/2251594011/ref=pd_zg_hrsr_grocery)\n\nBrief content visible, double tap to read full content.\n\nFull content visible, double tap to read brief content.\n\n## Videos\n\nPage 1 of 1[Start Over](https://www.amazon.com/dp/B0BRGHLXHD)Page 1 of 1\n\n[_Previous page_](https://www.amazon.com/dp/B0BRGHLXHD)\n\n  1. #### Videos for this product\n[ ![Video Widget Card](https://images-na.ssl-images-\namazon.com/images/G/01/x-locale/common/grey-pixel.gif)![Video Widget\nCard](https://m.media-\namazon.com/images/I/41PQqoz2xML._CR1,0,638,360_SR342,193_.jpg) 0:20  Click to\nplay video\n](https://www.amazon.com/vdp/0a6e40ba54194528a33046f71e0700e5?ref=dp_vse_rvc_0)\n\n![Video Widget Video Title Section](https://images-na.ssl-images-\namazon.com/images/G/01/x-locale/common/grey-pixel.gif)\n\nmatiz un sabor colombiano\n\nRuufe\n\n[_Next page_](https://www.amazon.com/dp/B0BRGHLXHD)\n\n[Upload your\nvideo](https://www.amazon.com/creatorhub/video/upload?productASIN=B0BRGHLXHD&referringURL=ZHAvQjBCUkdITFhIRA%3D%3D&ref=RVSW)\n\n* * *\n## Important information\n\nLegal Disclaimer\n\nStatements regarding dietary supplements have not been evaluated by the FDA and\nare not intended to diagnose, treat, cure, or prevent any disease or health\ncondition.\n\n* * *\n## Products related to this item\n\nPage 1 of 1[Start over](https://www.amazon.com/dp/B0BRGHLXHD)Page 1 of 1\n\n[ Sponsored  ](https://www.amazon.com/dp/B0BRGHLXHD#sp_detail2_feedbackForm)\n\n[_Previous page of related Sponsored\nProducts_](https://www.amazon.com/dp/B0BRGHLXHD)\n\n  1. [Feedback](javascript:void\\(0\\))\n[ ![Sello Rojo, Tradicional Single Serve Coffee K-Cup Pods, 12\nct](https://m.media-\namazon.com/images/I/41K4YUPSsEL._AC_UF480,480_SR480,480_.jpg) Sello Rojo,\nTradicional Single Serve Coffee K-Cup Pods, 12 ct\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDAxMzQ4MTc4NjY3MDI6Ojo6&url=%2Fdp%2FB0CP2PSG7Z%2Fref%3Dsspa_dk_detail_0%3Fpsc%3D1%26pd_rd_i%3DB0CP2PSG7Z%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy \"Sello Rojo,\nTradicional Single Serve Coffee K-Cup Pods, 12 ct\")\n\n[ 32\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDAxMzQ4MTc4NjY3MDI6Ojo6&url=%2Fdp%2FB0CP2PSG7Z%2Fref%3Dsspa_dk_detail_0%3Fpsc%3D1%26pd_rd_i%3DB0CP2PSG7Z%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy#customerReviews)\n\n[ _ Amazon's Choice _\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDAxMzQ4MTc4NjY3MDI6Ojo6&url=%2Fdp%2FB0CP2PSG7Z%2Fref%3Dsspa_dk_detail_0%3Fpsc%3D1%26pd_rd_i%3DB0CP2PSG7Z%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n[ $8.99$8.99($0.75$0.75 / Count)\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDAxMzQ4MTc4NjY3MDI6Ojo6&url=%2Fdp%2FB0CP2PSG7Z%2Fref%3Dsspa_dk_detail_0%3Fpsc%3D1%26pd_rd_i%3DB0CP2PSG7Z%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDAxMzQ4MTc4NjY3MDI6Ojo6&url=%2Fdp%2FB0CP2PSG7Z%2Fref%3Dsspa_dk_detail_0%3Fpsc%3D1%26pd_rd_i%3DB0CP2PSG7Z%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n  2. [Feedback](javascript:void\\(0\\))\n[ ![Casitika Colombian Gifts. Have No Fear Colombia Coffee Mug. Novelty Cups\nWith Flag For Proud Husband Or Wife. \\(11 oz Black\nHandle/Rim\\)](https://m.media-\namazon.com/images/I/41gQV1KKQ7L._AC_UF480,480_SR480,480_.jpg) Casitika Colombian\nGifts. Have No Fear Colombia Coffee Mug. Novelty Cups With Flag ...\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjoyMDAxNTA5MDE1NDIzOTg6Ojo6&url=%2Fdp%2FB0BTR38M9C%2Fref%3Dsspa_dk_detail_1%3Fpsc%3D1%26pd_rd_i%3DB0BTR38M9C%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy \"Casitika\nColombian Gifts. Have No Fear Colombia Coffee Mug. Novelty Cups With Flag For\nProud Husband Or Wife. \\(11 oz Black Handle/Rim\\)\")\n\n[ 8\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjoyMDAxNTA5MDE1NDIzOTg6Ojo6&url=%2Fdp%2FB0BTR38M9C%2Fref%3Dsspa_dk_detail_1%3Fpsc%3D1%26pd_rd_i%3DB0BTR38M9C%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy#customerReviews)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjoyMDAxNTA5MDE1NDIzOTg6Ojo6&url=%2Fdp%2FB0BTR38M9C%2Fref%3Dsspa_dk_detail_1%3Fpsc%3D1%26pd_rd_i%3DB0BTR38M9C%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n  3. [Feedback](javascript:void\\(0\\))\n[ ![Rowdy Mushroom Coffee - Instant Coffee that Chills you Out - 6 Functional\nMushroom Blend, Ready in Seconds - Drink Hot or Cold - Lasts 1+ Months\n\\(6oz\\)](https://m.media-\namazon.com/images/I/41d0QB+qb2L._AC_UF480,480_SR480,480_.jpg) Rowdy Mushroom\nCoffee - Instant Coffee that Chills you Out - 6 Functional Mushroom ...\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA0MTM1NzQ3OTE4MDI6Ojo6&url=%2Fdp%2FB0CV5PWT6C%2Fref%3Dsspa_dk_detail_2%3Fpsc%3D1%26pd_rd_i%3DB0CV5PWT6C%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy \"Rowdy\nMushroom Coffee - Instant Coffee that Chills you Out - 6 Functional Mushroom\nBlend, Ready in Seconds - Drink Hot or Cold - Lasts 1+ Months \\(6oz\\)\")\n\n[ 58\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA0MTM1NzQ3OTE4MDI6Ojo6&url=%2Fdp%2FB0CV5PWT6C%2Fref%3Dsspa_dk_detail_2%3Fpsc%3D1%26pd_rd_i%3DB0CV5PWT6C%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy#customerReviews)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA0MTM1NzQ3OTE4MDI6Ojo6&url=%2Fdp%2FB0CV5PWT6C%2Fref%3Dsspa_dk_detail_2%3Fpsc%3D1%26pd_rd_i%3DB0CV5PWT6C%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n  4. [Feedback](javascript:void\\(0\\))\n[ ![Mushroom Coffee, Instant Organic Mushroom Coffee with Cordyceps, Reish, King\nTrumpet, Shitake, Turkey Tail, Lions Mane for Energy, Focus, Positive Mood &\nImmune Support \\(30 Servings\\)](https://m.media-\namazon.com/images/I/41A1Z0yyksL._AC_UF480,480_SR480,480_.jpg) Mushroom Coffee,\nInstant Organic Mushroom Coffee with Cordyceps, Reish,…\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA1MzU1MzM2MDMzMDI6Ojo6&url=%2Fdp%2FB0DJSKQRPM%2Fref%3Dsspa_dk_detail_3%3Fpsc%3D1%26pd_rd_i%3DB0DJSKQRPM%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy \"Mushroom\nCoffee, Instant Organic Mushroom Coffee with Cordyceps, Reish, King Trumpet,\nShitake, Turkey Tail, Lions Mane for Energy, Focus, Positive Mood & Immune\nSupport \\(30 Servings\\)\")\n\n[ 392\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA1MzU1MzM2MDMzMDI6Ojo6&url=%2Fdp%2FB0DJSKQRPM%2Fref%3Dsspa_dk_detail_3%3Fpsc%3D1%26pd_rd_i%3DB0DJSKQRPM%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy#customerReviews)\n\n[ _ Amazon's Choice _\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA1MzU1MzM2MDMzMDI6Ojo6&url=%2Fdp%2FB0DJSKQRPM%2Fref%3Dsspa_dk_detail_3%3Fpsc%3D1%26pd_rd_i%3DB0DJSKQRPM%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA1MzU1MzM2MDMzMDI6Ojo6&url=%2Fdp%2FB0DJSKQRPM%2Fref%3Dsspa_dk_detail_3%3Fpsc%3D1%26pd_rd_i%3DB0DJSKQRPM%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n  5. [Feedback](javascript:void\\(0\\))\n[ ![NüSpira - Latte Mushroom Coffee - Premium Blend of 6 Adaptogen Mushrooms for\nVitality, Brain Clarity, Gut Health & Immune Support - 12oz\nPack](https://m.media-\namazon.com/images/I/41POeDdHf2L._AC_UF480,480_SR480,480_.jpg) NüSpira - Latte\nMushroom Coffee - Premium Blend of 6 Adaptogen Mushrooms…\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA3MDQ0OTkxNzgwMDI6Ojo6&url=%2Fdp%2FB0DK24ZSHD%2Fref%3Dsspa_dk_detail_4%3Fpsc%3D1%26pd_rd_i%3DB0DK24ZSHD%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy \"NüSpira -\nLatte Mushroom Coffee - Premium Blend of 6 Adaptogen Mushrooms for Vitality,\nBrain Clarity, Gut Health & Immune Support - 12oz Pack\")\n\n[ 28\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA3MDQ0OTkxNzgwMDI6Ojo6&url=%2Fdp%2FB0DK24ZSHD%2Fref%3Dsspa_dk_detail_4%3Fpsc%3D1%26pd_rd_i%3DB0DK24ZSHD%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy#customerReviews)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA3MDQ0OTkxNzgwMDI6Ojo6&url=%2Fdp%2FB0DK24ZSHD%2Fref%3Dsspa_dk_detail_4%3Fpsc%3D1%26pd_rd_i%3DB0DK24ZSHD%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n  6. [Feedback](javascript:void\\(0\\))\n[ ![Mushroom Coffee, 90 Servings Organic Mushrooms Instant Coffea, Great Tasting\nColombian Keto Coffee, 7 Superfood Mishrooms Lions Mane, Chaga, Reishi,\nCordyceps, Maitake, Shiitake, and Turkey Tail](https://m.media-\namazon.com/images/I/51Ab8OgfKZL._AC_UF480,480_SR480,480_.jpg) Mushroom Coffee,\n90 Servings Organic Mushrooms Instant Coffea, Great Tasting…\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDAzMjA0NDU5MzMyMDI6Ojo6&url=%2Fdp%2FB0CLT4SLGD%2Fref%3Dsspa_dk_detail_5%3Fpsc%3D1%26pd_rd_i%3DB0CLT4SLGD%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy \"Mushroom\nCoffee, 90 Servings Organic Mushrooms Instant Coffea, Great Tasting Colombian\nKeto Coffee, 7 Superfood Mishrooms Lions Mane, Chaga, Reishi, Cordyceps,\nMaitake, Shiitake, and Turkey Tail\")\n\n[ 866\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDAzMjA0NDU5MzMyMDI6Ojo6&url=%2Fdp%2FB0CLT4SLGD%2Fref%3Dsspa_dk_detail_5%3Fpsc%3D1%26pd_rd_i%3DB0CLT4SLGD%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy#customerReviews)\n\n[ _ Amazon's Choice _\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDAzMjA0NDU5MzMyMDI6Ojo6&url=%2Fdp%2FB0CLT4SLGD%2Fref%3Dsspa_dk_detail_5%3Fpsc%3D1%26pd_rd_i%3DB0CLT4SLGD%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDAzMjA0NDU5MzMyMDI6Ojo6&url=%2Fdp%2FB0CLT4SLGD%2Fref%3Dsspa_dk_detail_5%3Fpsc%3D1%26pd_rd_i%3DB0CLT4SLGD%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n  7. [Feedback](javascript:void\\(0\\))\n[ ![Blackout Coffee Colombian Arabica Instant Coffee, Strong Aromatic, Fresh\nRoasted in the USA, Premium Coffee Single Serve Packets, 32\nCount](https://m.media-\namazon.com/images/I/51+QII54m4L._AC_UF480,480_SR480,480_.jpg) Blackout Coffee\nColombian Arabica Instant Coffee, Strong Aromatic, Fresh…\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA3Mzc1MDExMDUzMDI6Ojo6&url=%2Fdp%2FB0D1VN9691%2Fref%3Dsspa_dk_detail_6%3Fpsc%3D1%26pd_rd_i%3DB0D1VN9691%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy \"Blackout\nCoffee Colombian Arabica Instant Coffee, Strong Aromatic, Fresh Roasted in the\nUSA, Premium Coffee Single Serve Packets, 32 Count\")\n\n[ 22\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA3Mzc1MDExMDUzMDI6Ojo6&url=%2Fdp%2FB0D1VN9691%2Fref%3Dsspa_dk_detail_6%3Fpsc%3D1%26pd_rd_i%3DB0D1VN9691%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy#customerReviews)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToxNTYwMTYyMTI3MDUzNzcxOjE3NDcwNjcxODQ6c3BfZGV0YWlsMjozMDA3Mzc1MDExMDUzMDI6Ojo6&url=%2Fdp%2FB0D1VN9691%2Fref%3Dsspa_dk_detail_6%3Fpsc%3D1%26pd_rd_i%3DB0D1VN9691%26pd_rd_w%3DOwz4z%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3D4EKYGM2V36EZJDZEZTFS%26pd_rd_wg%3DWi8XS%26pd_rd_r%3D676a58ff-c50b-47b5-b4af-\ncac7b8e00192%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n[_Next page of related Sponsored\nProducts_](https://www.amazon.com/dp/B0BRGHLXHD)\n\n* * *\nSponsored\n\n* * *\n## Customer reviews\n\n  * 5 star4 star3 star2 star1 star5 star\n0%0%0%0%0%0%\n\n  * 5 star4 star3 star2 star1 star4 star\n0%0%0%0%0%0%\n\n  * 5 star4 star3 star2 star1 star3 star\n0%0%0%0%0%0%\n\n  * 5 star4 star3 star2 star1 star2 star\n0%0%0%0%0%0%\n\n  * 5 star4 star3 star2 star1 star1 star\n0%0%0%0%0%0%\n\n[How customer reviews and ratings work](javascript:void\\(0\\))\n\nCustomer Reviews, including Product Star Ratings help customers to learn more\nabout the product and decide whether it is the right product for them.\n\nTo calculate the overall star rating and percentage breakdown by star, we don’t\nuse a simple average. Instead, our system considers things like how recent a\nreview is and if the reviewer bought the item on Amazon. It also analyzed\nreviews to verify trustworthiness.\n\n[Learn more how customers reviews work on\nAmazon](https://www.amazon.com/gp/help/customer/display.html/ref=cm_cr_dp_d_omni_lm_btn?nodeId=G8UYX7LALQC8V9KA)\n\n* * *\n### Review this product\n\nShare your thoughts with other customers\n\n[Write a customer review](https://www.amazon.com/review/create-\nreview/ref=cm_cr_dp_d_wr_but_top?ie=UTF8&channel=glance-detail&asin=B0BRGHLXHD)\n\n* * *\nSponsored\n\n[ View Image Gallery  ](javascript:toggleSeeAllRankingView\\(\\))\n\n![Customer image](https://images-na.ssl-images-\namazon.com/images/G/01/x-locale/common/transparent-pixel._V192234675_.gif)\n\n[![](https://images-na.ssl-images-amazon.com/images/G/01/x-locale/common/grey-\npixel.gif) Amazon Customer](javascript:void\\(0\\))\n\n_5.0 out of 5 stars_  \n\n######  Images in this review\n\n### No customer reviews\n\nSponsored\n\n* * *\n**Disclaimer** : While we work to ensure that product information is correct, on\noccasion manufacturers may alter their ingredient lists. Actual product\npackaging and materials may contain more and/or different information than that\nshown on our Web site. We recommend that you do not solely rely on the\ninformation presented and that you always read labels, warnings, and directions\nbefore using or consuming a product. For additional information about a product,\nplease contact the manufacturer. Content on this site is for reference purposes\nand is not intended to substitute for advice given by a physician, pharmacist,\nor other licensed health-care professional. You should not use this information\nas self-diagnosis or for treating a health problem or disease. Contact your\nhealth-care provider immediately if you suspect that you have a medical problem.\nInformation and statements regarding dietary supplements have not been evaluated\nby the Food and Drug Administration and are not intended to diagnose, treat,\ncure, or prevent any disease or health condition. Amazon.com assumes no\nliability for inaccuracies or misstatements about products.\n\n[ Top ](https://www.amazon.com/dp/B0BRGHLXHD) [ About this item\n](https://www.amazon.com/dp/B0BRGHLXHD) [ Similar\n](https://www.amazon.com/dp/B0BRGHLXHD) [ Product information\n](https://www.amazon.com/dp/B0BRGHLXHD) [ Questions\n](https://www.amazon.com/dp/B0BRGHLXHD) [ Reviews\n](https://www.amazon.com/dp/B0BRGHLXHD)\n\n  \n\n![](https://m.media-\namazon.com/images/G/01/personalization/ybh/loading-4x-gray._CB485916920_.gif)  \n---  \nYour recently viewed items and featured recommendations\n\n›\n\n[ View or edit your browsing history ](https://www.amazon.com/gp/history)\n\nAfter viewing product detail pages, look here to find an easy way to navigate\nback to pages you are interested in.\n\n  \n\nBack to top\n\nGet to Know Us\n\n  * [Careers](https://www.amazon.jobs)\n  * [Blog](https://blog.aboutamazon.com/?utm_source=gateway&utm_medium=footer)\n  * [About Amazon](https://www.aboutamazon.com/?utm_source=gateway&utm_medium=footer)\n  * [Investor Relations](https://www.amazon.com/ir)\n  * [Amazon Devices](https://www.amazon.com/gp/browse.html?node=2102313011&ref_=footer_devices)\n  * [Amazon Science](https://www.amazon.science)\n\nMake Money with Us\n\n  * [Sell products on Amazon](https://services.amazon.com/sell.html?ld=AZFSSOA&ref_=footer_soa)\n  * [Sell on Amazon Business](https://services.amazon.com/amazon-business.html?ld=usb2bunifooter&ref_=footer_b2b)\n  * [Sell apps on Amazon](https://developer.amazon.com)\n  * [Become an Affiliate](https://affiliate-program.amazon.com/)\n  * [Advertise Your Products](https://advertising.amazon.com/?ref=ext_amzn_ftr)\n  * [Self-Publish with Us](https://www.amazon.com/gp/seller-account/mm-summary-page.html?ld=AZFooterSelfPublish&topic=*********&ref_=footer_publishing)\n  * [Host an Amazon Hub](https://go.thehub-amazon.com/amazon-hub-locker)\n  * ›[See More Make Money with Us](https://www.amazon.com/b/?node=***********&ld=AZUSSOA-seemore&ref_=footer_seemore)\n\nAmazon Payment Products\n\n  * [Amazon Business Card](https://www.amazon.com/dp/B07984JN3L?plattr=ACOMFO&ie=UTF-8)\n  * [Shop with Points](https://www.amazon.com/gp/browse.html?node=***********&ref_=footer_swp)\n  * [Reload Your Balance](https://www.amazon.com/dp/B0CHTVMXZJ?th=1?ref_=footer_reload_us)\n  * [Amazon Currency Converter](https://www.amazon.com/gp/browse.html?node=*********&ref_=footer_tfx)\n\nLet Us Help You\n\n  * [Amazon and COVID-19](https://www.amazon.com/gp/help/customer/display.html?nodeId=GDFU3JS5AL6SYHRD&ref_=footer_covid)\n  * [Your Account](https://www.amazon.com/gp/css/homepage.html?ref_=footer_ya)\n  * [Your Orders](https://www.amazon.com/gp/css/order-history?ref_=footer_yo)\n  * [Shipping Rates & Policies](https://www.amazon.com/gp/help/customer/display.html?nodeId=468520&ref_=footer_shiprates)\n  * [Returns & Replacements](https://www.amazon.com/gp/css/returns/homepage.html?ref_=footer_hy_f_4)\n  * [Manage Your Content and Devices](https://www.amazon.com/gp/digital/fiona/manage?ref_=footer_myk)\n  * [Help](https://www.amazon.com/gp/help/customer/display.html?nodeId=508510&ref_=footer_gw_m_b_he)\n\n[ ](https://www.amazon.com/?ref_=footer_logo)\n\n[ English](https://www.amazon.com/customer-\npreferences/edit?ie=UTF8&preferencesReturnUrl=%2F&ref_=footer_lang) [ $USD -\nU.S. Dollar ](https://www.amazon.com/customer-\npreferences/edit?ie=UTF8&ref_=footer_cop&preferencesReturnUrl=%2Fdp%2FB0BRGHLXHD)\n[ United States ](https://www.amazon.com/customer-\npreferences/country?ie=UTF8&preferencesReturnUrl=%2F&ref_=footer_icp_cp)\n\n  * ##### [Amazon Music Stream millions  \nof songs](https://music.amazon.com?ref=dm_aff_amz_com)\n\n  * ##### [Amazon Ads Reach customers  \nwherever they  \nspend their time](https://advertising.amazon.com/?ref=footer_advtsing_amzn_com)\n\n  * ##### [6pm Score deals  \non fashion brands](https://www.6pm.com)\n\n  * ##### [AbeBooks Books, art  \n& collectibles](https://www.abebooks.com)\n\n  * ##### [ACX  Audiobook Publishing  \nMade Easy](https://www.acx.com/)\n\n  * ##### [Sell on Amazon Start a Selling Account](https://sell.amazon.com/?ld=AZUSSOA-footer-aff&ref_=footer_sell)\n  * ##### [Veeqo Shipping Software  \nInventory\nManagement](https://www.veeqo.com/?utm_source=amazon&utm_medium=website&utm_campaign=footer)\n\n  * ##### [Amazon Business Everything For  \nYour Business](https://www.amazon.com/business?ref_=footer_retail_b2b)\n\n  * ##### [AmazonGlobal Ship Orders  \nInternationally](https://www.amazon.com/gp/browse.html?node=*********&ref_=footer_amazonglobal)\n\n  * ##### [Amazon Web Services Scalable Cloud  \nComputing Services](https://aws.amazon.com/what-is-cloud-\ncomputing/?sc_channel=EL&sc_campaign=amazonfooter)\n\n  * ##### [Audible Listen to Books & Original  \nAudio Performances](https://www.audible.com)\n\n  * ##### [Box Office Mojo Find Movie  \nBox Office Data](https://www.boxofficemojo.com/?ref_=amzn_nav_ftr)\n\n  * ##### [Goodreads Book reviews  \n& recommendations](https://www.goodreads.com)\n\n  * ##### [IMDb Movies, TV  \n& Celebrities](https://www.imdb.com)\n\n  * ##### [IMDbPro Get Info Entertainment  \nProfessionals Need](https://pro.imdb.com?ref_=amzn_nav_ftr)\n\n  * ##### [Kindle Direct Publishing Indie Digital & Print Publishing  \nMade Easy ](https://kdp.amazon.com)\n\n  * ##### [Prime Video Direct Video Distribution  \nMade Easy](https://videodirect.amazon.com/home/<USER>//www.shopbop.com)\n\n  * ##### [Woot! Deals and   \nShenanigans](https://www.woot.com/)\n\n  * ##### [Zappos Shoes &  \nClothing](https://www.zappos.com)\n\n  * ##### [Ring Smart Home  \nSecurity Systems ](https://ring.com)\n\n  * ##### [eero WiFi Stream 4K Video  \nin Every Room](https://eero.com/)\n\n  * ##### [Blink Smart Security  \nfor Every Home ](https://blinkforhome.com/?ref=nav_footer)\n\n  * ##### [Neighbors App  Real-Time Crime  \n& Safety Alerts ](https://shop.ring.com/pages/neighbors-app)\n\n  * ##### [Amazon Subscription Boxes Top subscription boxes – right to your door](https://www.amazon.com/gp/browse.html?node=14498690011&ref_=amzn_nav_ftr_swa)\n  * ##### [PillPack Pharmacy Simplified](https://www.pillpack.com)\n\n  * [Conditions of Use](https://www.amazon.com/gp/help/customer/display.html?nodeId=508088&ref_=footer_cou)\n  * [Privacy Notice](https://www.amazon.com/gp/help/customer/display.html?nodeId=468496&ref_=footer_privacy)\n  * [Consumer Health Data Privacy Disclosure](https://www.amazon.com/gp/help/customer/display.html?ie=UTF8&nodeId=TnACMrGVghHocjL8KB&ref_=footer_consumer_health_data_privacy)\n  * [Your Ads Privacy Choices](https://www.amazon.com/privacyprefs?ref_=footer_iba)\n\n© 1996-2025, Amazon.com, Inc. or its affiliates\n\n\n\nEnriched data:\n$ENRICHED_DATA\n=== OPENFOODFACTS RESULTS ===\n\n--- OpenFoodFacts Rank 1 (BM25 Score: 18.77) ---\ncode: 7702032111046\nurl: http://world-en.openfoodfacts.org/product/7702032111046/cafe-matiz\ncreator: smoothie-app\ncreated_t: **********\ncreated_datetime: 2024-01-31T20:32:16Z\nlast_modified_t: **********\nlast_modified_datetime: 2024-01-31T20:38:17Z\nlast_modified_by: smoothie-app\nlast_updated_t: **********.0\nlast_updated_datetime: 2025-02-06T07:03:54Z\nproduct_name: Café Matiz\ncategories: Plant-based foods and beverages, Plant-based foods, Coffees, Ground coffees\ncategories_tags: en:plant-based-foods-and-beverages,en:plant-based-foods,en:coffees,en:ground-coffees\ncategories_en: Plant-based foods and beverages,Plant-based foods,Coffees,Ground coffees\norigins: Colombia\norigins_tags: en:colombia\norigins_en: Colombia\ncountries: en:Panama\ncountries_tags: en:panama\ncountries_en: Panama\nstates: en:to-be-completed, en:nutrition-facts-to-be-completed, en:ingredients-to-be-completed, en:expiration-date-to-be-completed, en:packaging-code-to-be-completed, en:characteristics-to-be-completed, en:origins-completed, en:categories-completed, en:brands-to-be-completed, en:packaging-to-be-completed, en:quantity-to-be-completed, en:product-name-completed, en:photos-to-be-validated, en:packaging-photo-to-be-selected, en:nutrition-photo-to-be-selected, en:ingredients-photo-selected, en:front-photo-selected, en:photos-uploaded\nstates_tags: en:to-be-completed,en:nutrition-facts-to-be-completed,en:ingredients-to-be-completed,en:expiration-date-to-be-completed,en:packaging-code-to-be-completed,en:characteristics-to-be-completed,en:origins-completed,en:categories-completed,en:brands-to-be-completed,en:packaging-to-be-completed,en:quantity-to-be-completed,en:product-name-completed,en:photos-to-be-validated,en:packaging-photo-to-be-selected,en:nutrition-photo-to-be-selected,en:ingredients-photo-selected,en:front-photo-selected,en:photos-uploaded\nstates_en: To be completed,Nutrition facts to be completed,Ingredients to be completed,Expiration date to be completed,Packaging code to be completed,Characteristics to be completed,Origins completed,Categories completed,Brands to be completed,Packaging to be completed,Quantity to be completed,Product name completed,Photos to be validated,Packaging photo to be selected,Nutrition photo to be selected,Ingredients photo selected,Front photo selected,Photos uploaded\nunique_scans_n: 1.0\npopularity_tags: top-75-percent-scans-2024,top-80-percent-scans-2024,top-85-percent-scans-2024,top-90-percent-scans-2024,top-500-pa-scans-2024,top-1000-pa-scans-2024,top-5000-pa-scans-2024,top-10000-pa-scans-2024,top-50000-pa-scans-2024,top-100000-pa-scans-2024,top-country-pa-scans-2024\ncompleteness: 0.375\nlast_image_t: 1706733326.0\nlast_image_datetime: 2024-01-31T20:35:26Z\nmain_category: en:ground-coffees\nmain_category_en: Ground coffees\nimage_url: https://images.openfoodfacts.org/images/products/770/203/211/1046/front_en.4.400.jpg\nimage_small_url: https://images.openfoodfacts.org/images/products/770/203/211/1046/front_en.4.200.jpg\nimage_ingredients_url: https://images.openfoodfacts.org/images/products/770/203/211/1046/ingredients_en.6.400.jpg\nimage_ingredients_small_url: https://images.openfoodfacts.org/images/products/770/203/211/1046/ingredients_en.6.200.jpg\n\n--- OpenFoodFacts Rank 2 (BM25 Score: 15.96) ---\ncode: 7302032117453\nurl: http://world-en.openfoodfacts.org/product/7302032117453/cafe-matiz-baileys\ncreator: app-kaki\ncreated_t: 1736890235\ncreated_datetime: 2025-01-14T21:30:35Z\nlast_modified_t: 1736890235\nlast_modified_datetime: 2025-01-14T21:30:35Z\nlast_modified_by: app-kaki\nlast_updated_t: 1736890235.0\nlast_updated_datetime: 2025-01-14T21:30:35Z\nproduct_name: Café Matiz Baileys\nbrands: Matiz Baileys\nbrands_tags: matiz-baileys\nbrands_en: Matiz-baileys\ncountries: en:Colombia\ncountries_tags: en:colombia\ncountries_en: Colombia\nstates: en:to-be-completed, en:nutrition-facts-to-be-completed, en:ingredients-to-be-completed, en:expiration-date-to-be-completed, en:packaging-code-to-be-completed, en:characteristics-to-be-completed, en:origins-to-be-completed, en:categories-to-be-completed, en:brands-completed, en:packaging-to-be-completed, en:quantity-to-be-completed, en:product-name-completed, en:photos-to-be-uploaded\nstates_tags: en:to-be-completed,en:nutrition-facts-to-be-completed,en:ingredients-to-be-completed,en:expiration-date-to-be-completed,en:packaging-code-to-be-completed,en:characteristics-to-be-completed,en:origins-to-be-completed,en:categories-to-be-completed,en:brands-completed,en:packaging-to-be-completed,en:quantity-to-be-completed,en:product-name-completed,en:photos-to-be-uploaded\nstates_en: To be completed,Nutrition facts to be completed,Ingredients to be completed,Expiration date to be completed,Packaging code to be completed,Characteristics to be completed,Origins to be completed,Categories to be completed,Brands completed,Packaging to be completed,Quantity to be completed,Product name completed,Photos to be uploaded\ncompleteness: 0.2\n\n--- OpenFoodFacts Rank 3 (BM25 Score: 15.96) ---\ncode: 7702032117925\nurl: http://world-en.openfoodfacts.org/product/7702032117925/cafe-matiz-baileys\ncreator: app-kaki\ncreated_t: 1736890194\ncreated_datetime: 2025-01-14T21:29:54Z\nlast_modified_t: 1736890194\nlast_modified_datetime: 2025-01-14T21:29:54Z\nlast_modified_by: app-kaki\nlast_updated_t: 1736890194.0\nlast_updated_datetime: 2025-01-14T21:29:54Z\nproduct_name: Café Matiz Baileys\nbrands: Matiz Baileys\nbrands_tags: matiz-baileys\nbrands_en: Matiz-baileys\ncountries: en:Colombia\ncountries_tags: en:colombia\ncountries_en: Colombia\nstates: en:to-be-completed, en:nutrition-facts-to-be-completed, en:ingredients-to-be-completed, en:expiration-date-to-be-completed, en:packaging-code-to-be-completed, en:characteristics-to-be-completed, en:origins-to-be-completed, en:categories-to-be-completed, en:brands-completed, en:packaging-to-be-completed, en:quantity-to-be-completed, en:product-name-completed, en:photos-to-be-uploaded\nstates_tags: en:to-be-completed,en:nutrition-facts-to-be-completed,en:ingredients-to-be-completed,en:expiration-date-to-be-completed,en:packaging-code-to-be-completed,en:characteristics-to-be-completed,en:origins-to-be-completed,en:categories-to-be-completed,en:brands-completed,en:packaging-to-be-completed,en:quantity-to-be-completed,en:product-name-completed,en:photos-to-be-uploaded\nstates_en: To be completed,Nutrition facts to be completed,Ingredients to be completed,Expiration date to be completed,Packaging code to be completed,Characteristics to be completed,Origins to be completed,Categories to be completed,Brands completed,Packaging to be completed,Quantity to be completed,Product name completed,Photos to be uploaded\ncompleteness: 0.2\n\n--- OpenFoodFacts Rank 4 (BM25 Score: 14.01) ---\ncode: 832924008066\nurl: http://world-en.openfoodfacts.org/product/0832924008066/matiz-lightly-smoked-wild-sardines\ncreator: macrofactor\ncreated_t: 1743885481\ncreated_datetime: 2025-04-05T20:38:01Z\nlast_modified_t: 1743885481\nlast_modified_datetime: 2025-04-05T20:38:01Z\nlast_modified_by: macrofactor\nlast_updated_t: 1743885481.0\nlast_updated_datetime: 2025-04-05T20:38:01Z\nproduct_name: Matiz Lightly Smoked Wild Sardines\nbrands: Matiz\nbrands_tags: xx:matiz\nbrands_en: matiz\ncountries: en:Canada\ncountries_tags: en:canada\ncountries_en: Canada\nserving_size: 84 g\nserving_quantity: 84.0\nstates: en:to-be-completed, en:nutrition-facts-completed, en:ingredients-to-be-completed, en:expiration-date-to-be-completed, en:packaging-code-to-be-completed, en:characteristics-to-be-completed, en:origins-to-be-completed, en:categories-to-be-completed, en:brands-completed, en:packaging-to-be-completed, en:quantity-to-be-completed, en:product-name-completed, en:photos-to-be-uploaded\nstates_tags: en:to-be-completed,en:nutrition-facts-completed,en:ingredients-to-be-completed,en:expiration-date-to-be-completed,en:packaging-code-to-be-completed,en:characteristics-to-be-completed,en:origins-to-be-completed,en:categories-to-be-completed,en:brands-completed,en:packaging-to-be-completed,en:quantity-to-be-completed,en:product-name-completed,en:photos-to-be-uploaded\nstates_en: To be completed,Nutrition facts completed,Ingredients to be completed,Expiration date to be completed,Packaging code to be completed,Characteristics to be completed,Origins to be completed,Categories to be completed,Brands completed,Packaging to be completed,Quantity to be completed,Product name completed,Photos to be uploaded\ncompleteness: 0.3\nenergy-kcal_100g: 202.0\nenergy_100g: 846.0\nfat_100g: 11.9\nsaturated-fat_100g: 2.98\ntrans-fat_100g: 0.0\ncholesterol_100g: 0.0714\ncarbohydrates_100g: 0.0\nsugars_100g: 0.0\nfiber_100g: 0.0\nproteins_100g: 23.8\nsalt_100g: 0.625\nsodium_100g: 0.25\npotassium_100g: 0.417\ncalcium_100g: 0.417\niron_100g: 0.00357\n\n--- OpenFoodFacts Rank 5 (BM25 Score: 12.27) ---\ncode: 832924008035\nurl: http://world-en.openfoodfacts.org/product/0832924008035/matiz-small-wild-pepper-sardines\ncreator: macrofactor\ncreated_t: 1743358785\ncreated_datetime: 2025-03-30T18:19:45Z\nlast_modified_t: 1743358785\nlast_modified_datetime: 2025-03-30T18:19:45Z\nlast_modified_by: macrofactor\nlast_updated_t: 1743358785.0\nlast_updated_datetime: 2025-03-30T18:19:45Z\nproduct_name: Matiz Small wild Pepper Sardines\nbrands: Matiz\nbrands_tags: xx:matiz\nbrands_en: matiz\ncountries: en:Canada\ncountries_tags: en:canada\ncountries_en: Canada\nserving_size: 60 g\nserving_quantity: 60.0\nstates: en:to-be-completed, en:nutrition-facts-completed, en:ingredients-to-be-completed, en:expiration-date-to-be-completed, en:packaging-code-to-be-completed, en:characteristics-to-be-completed, en:origins-to-be-completed, en:categories-to-be-completed, en:brands-completed, en:packaging-to-be-completed, en:quantity-to-be-completed, en:product-name-completed, en:photos-to-be-uploaded\nstates_tags: en:to-be-completed,en:nutrition-facts-completed,en:ingredients-to-be-completed,en:expiration-date-to-be-completed,en:packaging-code-to-be-completed,en:characteristics-to-be-completed,en:origins-to-be-completed,en:categories-to-be-completed,en:brands-completed,en:packaging-to-be-completed,en:quantity-to-be-completed,en:product-name-completed,en:photos-to-be-uploaded\nstates_en: To be completed,Nutrition facts completed,Ingredients to be completed,Expiration date to be completed,Packaging code to be completed,Characteristics to be completed,Origins to be completed,Categories to be completed,Brands completed,Packaging to be completed,Quantity to be completed,Product name completed,Photos to be uploaded\ncompleteness: 0.3\nenergy-kcal_100g: 183.0\nenergy_100g: 767.0\nfat_100g: 11.7\nsaturated-fat_100g: 6.67\ntrans-fat_100g: 0.0\ncholesterol_100g: 0.0667\ncarbohydrates_100g: 0.0\nsugars_100g: 0.0\nfiber_100g: 0.0\nproteins_100g: 21.7\nsalt_100g: 1.17\nsodium_100g: 0.467\npotassium_100g: 0.333\ncalcium_100g: 0.5\niron_100g: 0.00167\n\n=== FDA RESULTS ===\n\n--- FDA Rank 1 (BM25 Score: 10.79) ---\nfdc_id: 530121\nfdc_id_food: 530121\nbrand_owner: NATIVA YERBA MATE\ngtin_upc: 830033002821\ningredients: 100% NATURAL GROUND MATE LEAVES GENUS: ILEX PARAGUARIENSIS\nserving_size: 6.0\nserving_size_unit: g\nhousehold_serving_fulltext: 1 Tbsp\nbranded_food_category: Coffee\ndata_source: LI\nmodified_date: 2018-02-14\navailable_date: 2019-04-01\nmarket_country: United States\nid_x: 1367662\nfdc_id_nutrient: 168529\nnutrient_id: 1051\namount: 91.3\nid_y: 1051\nname: Water\nunit_name: G\nnutrient_nbr: 255.0\nrank: 100.0\ndata_type: branded_food\ndescription: SUAVE BLEND MILD YERBA MATE TEA, HERBAL COFFEE ALTERNATIVE\nfood_category_id: Coffee\npublication_date: 2019-04-01\n\n--- FDA Rank 2 (BM25 Score: 10.79) ---\nfdc_id: 1796312\nfdc_id_food: 1796312\nbrand_owner: Nativa Yerba Mate Inc.\nbrand_name: NATIVA YERBA MATE\ngtin_upc: 830033002821\ningredients: 100% NATURAL GROUND MATE LEAVES GENUS: ILEX PARAGUARIENSIS\nnot_a_significant_source_of: NOT A SIGNIFICANT SOURCE OF CALORIES FROM FAT, SATURATED FAT, CHOLESTEROL, SUGARS, VITAMIN A, OR VITAMIN C.\nserving_size: 6.0\nserving_size_unit: g\nhousehold_serving_fulltext: 1 Tbsp\nbranded_food_category: Coffee\ndata_source: LI\npackage_weight: 1 lbs\nmodified_date: 2018-02-14\navailable_date: 2021-06-17\nmarket_country: United States\nid_x: 5758140\nfdc_id_nutrient: 389563\nnutrient_id: 1093\namount: 58824.0\nderivation_id: 70.0\npercent_daily_value: 2.0\nid_y: 1093\nname: Sodium, Na\nunit_name: MG\nnutrient_nbr: 307.0\nrank: 5800.0\ndata_type: branded_food\ndescription: SUAVE BLEND MILD YERBA MATE TEA, HERBAL COFFEE ALTERNATIVE\nfood_category_id: Coffee\npublication_date: 2021-06-17\n\n--- FDA Rank 3 (BM25 Score: 10.43) ---\nfdc_id: 1801938\nfdc_id_food: 1801938\nbrand_owner: Fante Inc\nbrand_name: RANCHERA\ngtin_upc: 78732004665\ningredients: TOMATOES, ONIONS, PEPPERS, WATER, VINEGAR, CILANTRO, SEA SALT, GARLIC, OREGANO, CITRIC ACID & CUMIN.\nserving_size: 30.0\nserving_size_unit: g\nhousehold_serving_fulltext: 2 Tbsp\nbranded_food_category: Dips & Salsa\ndata_source: LI\npackage_weight: 425 g\nmodified_date: 2017-10-13\navailable_date: 2021-06-17\nmarket_country: United States\nid_x: 3762299\nfdc_id_nutrient: 389995\nnutrient_id: 1253\namount: 0.0\nderivation_id: 75.0\npercent_daily_value: 0.0\nid_y: 1253\nname: Cholesterol\nunit_name: MG\nnutrient_nbr: 601.0\nrank: 15700.0\ndata_type: branded_food\ndescription: HOME STYLE SALSA, MILD SUAVE\nfood_category_id: Dips & Salsa\npublication_date: 2021-06-17\n\n--- FDA Rank 4 (BM25 Score: 10.43) ---\nfdc_id: 1931628\nfdc_id_food: 1931628\nbrand_owner: Fante Inc\nbrand_name: RANCHERA\ngtin_upc: 78732004665\ningredients: TOMATOES, ONIONS, PEPPERS, WATER, VINEGAR, CILANTRO, SEA SALT, GARLIC, OREGANO, CITRIC ACID & CUMIN.\nserving_size: 30.0\nserving_size_unit: g\nhousehold_serving_fulltext: 2 Tbsp\nbranded_food_category: Dips & Salsa\ndata_source: LI\npackage_weight: 15 oz/425 g\nmodified_date: 2017-10-13\navailable_date: 2021-07-29\nmarket_country: United States\nid_x: 4410323\nfdc_id_nutrient: 399506\nnutrient_id: 1003\namount: 7.14\nderivation_id: 70.0\npercent_daily_value: 0.0\nid_y: 1003\nname: Protein\nunit_name: G\nnutrient_nbr: 203.0\nrank: 600.0\ndata_type: branded_food\ndescription: HOME STYLE SALSA, MILD SUAVE\nfood_category_id: Dips & Salsa\npublication_date: 2021-07-29\n\n--- FDA Rank 5 (BM25 Score: 10.43) ---\nfdc_id: 2285164\nfdc_id_food: 2285164\nbrand_owner: Fante Inc\nbrand_name: RANCHERA\ngtin_upc: 78732004665\ningredients: TOMATOES, ONIONS, PEPPERS, WATER, VINEGAR, CILANTRO, SEA SALT, GARLIC, OREGANO, CITRIC ACID & CUMIN.\nserving_size: 30.0\nserving_size_unit: g\nhousehold_serving_fulltext: 2 Tbsp\nbranded_food_category: Dips & Salsa\ndata_source: LI\npackage_weight: 15 oz/425 g\nmodified_date: 2017-10-13\navailable_date: 2022-05-23\nmarket_country: United States\nid_x: 3178691\nfdc_id_nutrient: 426985\nnutrient_id: 1089\namount: 3.86\nderivation_id: 75.0\npercent_daily_value: 6.0\nid_y: 1089\nname: Iron, Fe\nunit_name: MG\nnutrient_nbr: 303.0\nrank: 5400.0\ndata_type: branded_food\ndescription: HOME STYLE SALSA, MILD SUAVE\nfood_category_id: Dips & Salsa\npublication_date: 2022-05-23\n\n\n\nInstructions:\n1. You are provided with product images and product details fetched from online sources in $PRODUCT_DATA\n2. You are also provided with products data from other sources which might be similar to the original product in $ENRICHED_DATA\n3. Your job is to go through the images of the product, $PRODUCT_DATA and $ENRICHED_DATA and extract information according to schema provided in RESPONSE SCHEMA\n4. You are also provided with the format in which the response must be in RESPONSE SCHEMA\n5. Absolutely avoid ```json or ``` in the final response\n6. If you get multiple images which are unrelated to each other, provide output only for one product \n7. Do not output the schema itself. Make sure your output is JSON formatted string which satisfies the JSON schema\n8. Do not leave the qualified fields empty. Do guess work and provide some value for the qualified fields in the response.",
  "response": "{\n  \"analysed_data\": {\n    \"generalData\": {\n      \"gtinOnPack\": \"7702032111046\",\n      \"gtin14\": \"07702032111046\",\n      \"upc12\": \"770203211104\",\n      \"brandOwner\": \"Matiz\",\n      \"brandName\": \"Matiz\",\n      \"department\": \"Grocery\",\n      \"superCategory\": \"Beverages\",\n      \"category\": \"Coffee\",\n      \"subCategory\": \"Ground Coffee\",\n      \"segment\": \"Light Roast\",\n      \"productTitle\": \"Matiz Colombian Light Coffee (2 Pack)\",\n      \"variant\": \"Suave\",\n      \"netWeight1Value\": 340,\n      \"netWeight1UOM\": \"g\",\n      \"netWeight2Value\": 11.99,\n      \"netWeight2UOM\": \"oz\",\n      \"unitsPerPack\": 2,\n      \"unitsPerPackDescriptor\": \"Bags\",\n      \"storage\": \"Room Temperature\",\n      \"numberOfIngredients\": 1\n    },\n    \"servingSize\": {\n      \"servingSize\": 6,\n      \"servingSizeUnit\": \"g\",\n      \"servesPerPack\": 56,\n      \"servingDescription\": \"1 Tbsp (6g)\",\n      \"servingSize2\": null,\n      \"servingSize2Unit\": null\n    },\n    \"nutritionalInformation\": {\n      \"stated\": {\n        \"totalAmount\": null,\n        \"totalAmountUOM\": null,\n        \"source\": \"Amazon Images\",\n        \"macronutrients\": [],\n        \"micronutrients\": []\n      },\n      \"qualified\": {\n        \"totalAmount\": 100,\n        \"totalAmountUOM\": \"g\",\n        \"source\": \"ScrapingQualificationAI\",\n        \"macronutrients\": [\n          {\n            \"name\": \"Protein\",\n            \"amount\": 0.2,\n            \"totalAmount\": 100,\n            \"UOM\": \"g\",\n            \"dailyValue\": null,\n            \"source\": \"ScrapingQualificationAI\"\n          },\n          {\n            \"name\": \"Carbohydrates\",\n            \"amount\": 0.1,\n            \"totalAmount\": 100,\n            \"UOM\": \"g\",\n            \"dailyValue\": null,\n            \"source\": \"ScrapingQualificationAI\"\n          },\n          {\n            \"name\": \"Fat\",\n            \"amount\": 0.0,\n            \"totalAmount\": 100,\n            \"UOM\": \"g\",\n            \"dailyValue\": null,\n            \"source\": \"ScrapingQualificationAI\"\n          }\n        ],\n        \"micronutrients\": []\n      }\n    },\n    \"ingredients\": {\n      \"stated\": {\n        \"source\": \"Amazon Images\",\n        \"ingredientList\": [\n          {\n            \"text\": \"100% Colombian Arabica Coffee\",\n            \"amount\": 340,\n            \"amountUOM\": \"g\",\n            \"dietary_preference\": {\n              \"Vegan\": true,\n              \"Vegetarian\": true,\n              \"Pescatarian\": true,\n              \"WhiteMeatOnly\": false,\n              \"KetoFriendly\": true,\n              \"LowFodmap\": true\n            },\n            \"allergens\": {\n              \"Sugar\": false,\n              \"Celery\": false,\n              \"Gluten\": false,\n              \"Crustaceans\": false,\n              \"Eggs\": false,\n              \"Fish\": false,\n              \"Lupin\": false,\n              \"Milk\": false,\n              \"Peanuts\": false,\n              \"Sesame\": false,\n              \"TreeNuts\": false\n            },\n            \"religious_labels\": {\n              \"Halal\": true,\n              \"Kosher\": true,\n              \"Hindu\": true,\n              \"Jain\": true\n            },\n            \"food_safety_labels\": {\n              \"GMO\": false,\n              \"NoGMO\": true,\n              \"Hormones\": false,\n              \"Carcinogenic\": false,\n              \"Organic\": true,\n              \"ProductRecalls\": false\n            },\n            \"sustainability_labels\": {\n              \"Recycled\": false,\n              \"AnimalWelfare\": true,\n              \"OrganicPositioning\": true,\n              \"PlantBased\": true,\n              \"SocialResponsibility\": true,\n              \"SustainablePackaging\": true\n            },\n            \"source\": \"Amazon Images\",\n            \"subIngredients\": []\n          }\n        ]\n      },\n      \"qualified\": {\n        \"source\": \"ScrapingQualificationAI\",\n        \"ingredientList\": [\n          {\n            \"text\": \"100% Colombian Arabica Coffee\",\n            \"amount\": 340,\n            \"amountUOM\": \"g\",\n            \"dietary_preference\": {\n              \"Vegan\": true,\n              \"Vegetarian\": true,\n              \"Pescatarian\": true,\n              \"WhiteMeatOnly\": false,\n              \"KetoFriendly\": true,\n              \"LowFodmap\": true\n            },\n            \"allergens\": {\n              \"Sugar\": false,\n              \"Celery\": false,\n              \"Gluten\": false,\n              \"Crustaceans\": false,\n              \"Eggs\": false,\n              \"Fish\": false,\n              \"Lupin\": false,\n              \"Milk\": false,\n              \"Peanuts\": false,\n              \"Sesame\": false,\n              \"TreeNuts\": false\n            },\n            \"religious_labels\": {\n              \"Halal\": true,\n              \"Kosher\": true,\n              \"Hindu\": true,\n              \"Jain\": true\n            },\n            \"food_safety_labels\": {\n              \"GMO\": false,\n              \"NoGMO\": true,\n              \"Hormones\": false,\n              \"Carcinogenic\": false,\n              \"Organic\": true,\n              \"ProductRecalls\": false\n            },\n            \"sustainability_labels\": {\n              \"Recycled\": false,\n              \"AnimalWelfare\": true,\n              \"OrganicPositioning\": true,\n              \"PlantBased\": true,\n              \"SocialResponsibility\": true,\n              \"SustainablePackaging\": true\n            },\n            \"source\": \"ScrapingQualificationAI\",\n            \"subIngredients\": []\n          }\n        ]\n      }\n    },\n    \"claims\": {\n      \"certifications\": [\n        \"100% Colombian Arabica Coffee\",\n        \"Sustainably Sourced\"\n      ],\n      \"nutritionalClaims\": [\n        \"Low Fat\",\n        \"No Sugar\"\n      ],\n      \"ingredientClaims\": [\n        \"No Artificial Ingredients\",\n        \"Organic\"\n      ],\n      \"preparation\": {\n        \"storage\": \"Store in a cool, dry place\",\n        \"heatingInstructions\": {\n          \"toaster\": null,\n          \"oven\": null\n        }\n      },\n      \"sustainability\": {\n        \"packaging\": [\n          \"100% Recyclable Packaging\"\n        ],\n        \"environmentalClaims\": [\n          \"Sustainably Sourced\",\n          \"Social Responsibility\"\n        ]\n      },\n      \"contact\": {\n        \"website\": \"https://www.matiz.com\",\n        \"phone\": \"**************\"\n      }\n    },\n    \"NPI 2.0 Food Packages - Allergens & Intolerances\": {\n      \"fdaRegulatedAllergens\": \"None\",\n      \"caseinQualified\": \"No\",\n      \"caseinStated\": \"No Casein Added\",\n      \"coconutQualified\": \"No\",\n      \"coconutStated\": \"No Coconut Added\",\n      \"cornQualified\": \"No\",\n      \"cornStated\": \"No Corn Added\",\n      \"dairyLevelStated\": \"Dairy-Free\",\n      \"dairyQualified\": \"No\",\n      \"dairyStated\": \"No Dairy Added\",\n      \"eggLevelStated\": \"Egg-Free\",\n      \"eggQualified\": \"No\",\n      \"eggStated\": \"No Eggs Added\",\n      \"falcpaCommonAllergensQualified\": \"No\",\n      \"falcpaCommonAllergensStated\": \"None\",\n      \"fishLevelStated\": \"Fish-Free\",\n      \"fishQualified\": \"No\",\n      \"fishStated\": \"No Fish Added\",\n      \"glutenLevelStated\": \"Gluten-Free\",\n      \"glutenQualified\": \"No\"\n    },\n    \"cleanLabel\": {\n      \"artisanalStated\": \"Handcrafted\",\n      \"coldPressedIngredientsStated\": null,\n      \"craftStated\": \"Craft Brewed\",\n      \"gourmetStated\": \"Gourmet Quality\",\n      \"localStated\": \"Locally Sourced\",\n      \"madeInUsaStated\": null,\n      \"premiumStated\": \"Premium Quality\",\n      \"smallBatchStated\": \"Small Batch Produced\",\n      \"animalByProductQualified\": \"No\",\n      \"animalByProductStated\": \"No Animal By-Products Added\",\n      \"antibioticsQualified\": \"No\",\n      \"antibioticsStated\": \"No Antibiotics Ever\",\n      \"artificialColorsQualified\": \"No\",\n      \"artificialColorsStated\": \"No Artificial Colors Added\",\n      \"artificialFlavorsQualified\": \"No\",\n      \"artificialFlavorsStated\": \"No Artificial Flavors Added\",\n      \"artificialIngredientsQualified\": \"No\",\n      \"artificialPreservativesQualified\": \"No\",\n      \"artificialPreservativesStated\": \"No Artificial Preservatives Added\",\n      \"artificialSweetenersQualified\": \"No\",\n      \"artificialSweetenersStated\": \"No Artificial Sweeteners Added\",\n      \"countOfIngredientsQualified\": \"1\",\n      \"gmoPresenceQualified\": \"No\",\n      \"gmoPresenceStated\": \"Non-GMO Project Verified\",\n      \"highFructoseCornSyrupQualified\": \"No\",\n      \"highFructoseCornSyrupStated\": \"No High Fructose Corn Syrup Added\",\n      \"hormonesQualified\": \"No\",\n      \"hormonesStated\": \"No Hormones Added\",\n      \"naturalColorsQualified\": \"Yes\",\n      \"naturalColorsStated\": \"Colored with Natural Ingredients\",\n      \"naturalFlavorsQualified\": \"Yes\",\n      \"naturalFlavorsStated\": \"Flavored with Natural Ingredients\",\n      \"naturalPreservativesQualified\": \"Yes\",\n      \"naturalPreservativesStated\": \"Preserved with Natural Ingredients\",\n      \"naturalSweetenersQualified\": \"Yes\",\n      \"naturalSweetenersStated\": \"Sweetened with Natural Ingredients\",\n      \"preservativesQualified\": \"No\",\n      \"preservativesStated\": \"No Preservatives Added\",\n      \"rbstQualified\": \"No\",\n      \"rbstStated\": \"No rBST Added\",\n      \"recognizableIngredientsQualified\": \"Yes\",\n      \"sugarAlcoholsQualified\": \"No\",\n      \"sugarAlcoholsStated\": \"No Sugar Alcohols Added\"\n    },\n    \"additionalInfo\": {\n      \"energy\": null,\n      \"weight\": 340,\n      \"categories\": \"Plant-based foods and beverages, Coffees, Ground coffees\",\n      \"packaging\": \"Plastic\",\n      \"ecoscore\": \"B\",\n      \"nova_group\": \"1\",\n      \"nutriscore_grade\": \"A\",\n      \"data_source\": \"Manufactruer_claims,FoodScanGenius_AI\",\n      \"dietary_preference\": {\n        \"Vegan\": true,\n        \"Vegetarian\": true,\n        \"Pescatarian\": true,\n        \"WhiteMeatOnly\": false,\n        \"KetoFriendly\": true,\n        \"LowFodmap\": true\n      },\n      \"allergens\": {\n        \"Sugar\": false,\n        \"Celery\": false,\n        \"Gluten\": false,\n        \"Crustaceans\": false,\n        \"Eggs\": false,\n        \"Fish\": false,\n        \"Lupin\": false,\n        \"Milk\": false,\n        \"Peanuts\": false,\n        \"Sesame\": false,\n        \"TreeNuts\": false\n      },\n      \"religious_labels\": {\n        \"Halal\": true,\n        \"Kosher\": true,\n        \"Hindu\": true,\n        \"Jain\": true\n      },\n      \"food_safety_labels\": {\n        \"GMO\": false,\n        \"NoGMO\": true,\n        \"Hormones\": false,\n        \"Carcinogenic\": false,\n        \"Organic\": true,\n        \"ProductRecalls\": false\n      },\n      \"sustainability_labels\": {\n        \"Recycled\": false,\n        \"AnimalWelfare\": true,\n        \"OrganicPositioning\": true,\n        \"PlantBased\": true,\n        \"SocialResponsibility\": true,\n        \"SustainablePackaging\": true\n      },\n      \"average_customer_rating\": null,\n      \"ASIN\": \"B0BRGHLXHD\",\n      \"traces\": \"None\",\n      \"country_of_origin\": \"Colombia\",\n      \"customerCareNumber\": \"**************\",\n      \"email\": \"<EMAIL>\",\n      \"websiteLink\": \"https://www.matiz.com\"\n    }\n  },\n  \"scraped_data\": {\n    \"g\": 500,\n    \"url\": \"https://www.amazon.com/dp/B0BRGHLXHD\",\n    \"asin\": \"B0BRGHLXHD\",\n    \"size\": \"5.5 Ounce (Pack of 2)\",\n    \"type\": \"COFFEE\",\n    \"brand\": \"RUUFE\",\n    \"color\": null,\n    \"isB2B\": false,\n    \"isSNS\": false,\n    \"model\": null,\n    \"stats\": null,\n    \"title\": \"Matiz Colombian Light Coffee (2 Pack) Matiz Cafe Colombiano Suave roasted and ground coffee Matiz Suave\",\n    \"author\": null,\n    \"coupon\": null,\n    \"format\": null,\n    \"images\": [\n      {\n        \"m\": \"51h7aTHB51L.jpg\",\n        \"mH\": 500,\n        \"mW\": 500\n      },\n      {\n        \"m\": \"41IEemPPiQL.jpg\",\n        \"mH\": 500,\n        \"mW\": 500\n      },\n      {\n        \"m\": \"51eXNUQDwML.jpg\",\n        \"mH\": 500,\n        \"mW\": 500\n      },\n      {\n        \"m\": \"51NaxHcy7iL.jpg\",\n        \"mH\": 500,\n        \"mW\": 500\n      },\n      {\n        \"m\": \"41szrlvu-rL.jpg\",\n        \"mH\": 500,\n        \"mW\": 500\n      },\n      {\n        \"m\": \"61C5itIkxDL.jpg\",\n        \"mH\": 500,\n        \"mW\": 500\n      }\n    ],\n    \"offers\": null,\n    \"binding\": null,\n    \"eanList\": [\n      \"7706512558604\"\n    ],\n    \"edition\": null,\n    \"fbaFees\": null,\n    \"upcList\": null,\n    \"urlSlug\": \"Colombian-Coffee-Colombiano-roasted-ground\",\n    \"domainId\": 1,\n    \"features\": [\n      \"QUALITY: Premium 100% Colombian Coffee.\",\n      \"FLAVOR: Light\",\n      \"ROAST LEVEL: Roast Level: Light\",\n      \"HOW TO PREPARE: It can be diluted in milk or hot or cold water.\",\n      \"100% COLOMBIAN ARABICA COFFEE: Pure Arabica Colombian coffee. No blends. Features a medium roast & grind, which produces a smooth balanced body with notes of sweetness.\",\n      \"PRIME CLIMATE: The Andes Green Mountains provide the perfect recipe of elevation, soil, and climate for our coffee's globally recognized quality.\",\n      \"BEHIND THE BEANS: This coffee is sourced directly from family farms who take pride in their meticulously hand-picked coffee beans. Harvested at their peak, our roasted coffee beans are pristinely washed and sundried to perfection\"\n    ],\n    \"itemForm\": \"Powder\",\n    \"imagesCSV\": \"51h7aTHB51L.jpg,41IEemPPiQL.jpg,51eXNUQDwML.jpg,51NaxHcy7iL.jpg,41szrlvu-rL.jpg,61C5itIkxDL.jpg\",\n    \"itemWidth\": 0,\n    \"languages\": null,\n    \"launchpad\": false,\n    \"unitCount\": {\n      \"unitType\": \"Ounce\",\n      \"unitValue\": 11.0,\n      \"eachUnitCount\": 1.0\n    },\n    \"hasReviews\": false,\n    \"images_url\": [\n      \"https://m.media-amazon.com/images/I/51h7aTHB51L.jpg\",\n      \"https://m.media-amazon.com/images/I/41IEemPPiQL.jpg\",\n      \"https://m.media-amazon.com/images/I/51eXNUQDwML.jpg\",\n      \"https://m.media-amazon.com/images/I/51NaxHcy7iL.jpg\",\n      \"https://m.media-amazon.com/images/I/41szrlvu-rL.jpg\",\n      \"https://m.media-amazon.com/images/I/61C5itIkxDL.jpg\"\n    ],\n    \"itemHeight\": 0,\n    \"itemLength\": 0,\n    \"itemWeight\": 0,\n    \"lastUpdate\": 7534854,\n    \"parentAsin\": null,\n    \"partNumber\": null,\n    \"promotions\": null,\n    \"description\": \"Premium 100% Colombian Coffee. Light coffee that has been subjected to a coffee dehydration process at low temperatures that allow the aroma and flavor notes to stand out. It can be diluted in milk or hot or cold water. Manufacturer : Matiz Item Form: Ground Brand: Matiz Flavor: Intense Caffeine Content: Yes Roast Level: Medium_roast Organic, local, global and fair trade options available\",\n    \"listedSince\": 6128524,\n    \"productType\": 0,\n    \"releaseDate\": -1,\n    \"categoryTree\": [\n      {\n        \"name\": \"Grocery & Gourmet Food\",\n        \"catId\": 16310101\n      },\n      {\n        \"name\": \"Beverages\",\n        \"catId\": 16310231\n      },\n      {\n        \"name\": \"Coffee\",\n        \"catId\": 16318031\n      },\n      {\n        \"name\": \"Instant Coffee\",\n        \"catId\": 2251594011\n      }\n    ],\n    \"manufacturer\": \"Matiz\",\n    \"packageWidth\": 0,\n    \"productGroup\": \"Grocery\",\n    \"rootCategory\": 16310101,\n    \"variationCSV\": null,\n    \"brandStoreUrl\": \"/stores/RUUFE/page/E7C1E31F-8174-45D6-93E6-DEF59FD23571\",\n    \"newPriceIsMAP\": false,\n    \"numberOfItems\": 2,\n    \"numberOfPages\": -1,\n    \"packageHeight\": 0,\n    \"packageLength\": 0,\n    \"packageWeight\": 0,\n    \"trackingSince\": 6321850,\n    \"brandStoreName\": \"RUUFE\",\n    \"ebayListingIds\": null,\n    \"isAdultProduct\": false,\n    \"isRedirectASIN\": false,\n    \"lastEbayUpdate\": 0,\n    \"isHeatSensitive\": false,\n    \"itemTypeKeyword\": \"instant-coffee\",\n    \"lastPriceChange\": 7419718,\n    \"liveOffersOrder\": null,\n    \"packageQuantity\": -1,\n    \"publicationDate\": -1,\n    \"lastRatingUpdate\": 7530588,\n    \"offersSuccessful\": false,\n    \"brandStoreUrlName\": \"RUUFE\",\n    \"availabilityAmazon\": -1,\n    \"salesRankReference\": 16310101,\n    \"websiteDisplayGroup\": \"grocery_display_on_website\",\n    \"isEligibleForTradeIn\": false,\n    \"buyBoxSellerIdHistory\": null,\n    \"websiteDisplayGroupName\": \"Grocery\",\n    \"frequentlyBoughtTogether\": [\n      \"B000LXB9TC\",\n      \"B00AYLWSOQ\"\n    ],\n    \"buyBoxEligibleOfferCounts\": [\n      0,\n      1,\n      0,\n      0,\n      0,\n      0,\n      0,\n      0\n    ],\n    \"isEligibleForSuperSaverShipping\": false\n  }\n}",
  "images": [
    "https://m.media-amazon.com/images/I/51h7aTHB51L.jpg",
    "https://m.media-amazon.com/images/I/41IEemPPiQL.jpg",
    "https://m.media-amazon.com/images/I/51eXNUQDwML.jpg",
    "https://m.media-amazon.com/images/I/51NaxHcy7iL.jpg",
    "https://m.media-amazon.com/images/I/41szrlvu-rL.jpg",
    "https://m.media-amazon.com/images/I/61C5itIkxDL.jpg"
  ]
}
{
  "prompt": "\nYou are a helpful assistant who can extract product information from images and provide it in JSON format based on the schema below\n\nRESPONSE SCHEMA\n{\n  \"$schema\": \"http://json-schema.org/draft-07/schema#\",\n  \"title\": \"Comprehensive Product Information Schema\",\n  \"description\": \"A comprehensive schema that combines all product information including general data, nutritional information, serving sizes, ingredients, allergens, and product claims. This schema represents a complete view of product attributes, certifications, and nutritional data in a structured format.\",\n  \"type\": \"object\",\n  \"properties\": {\n    \"generalData\": {\n      \"type\": \"object\",\n      \"description\": \"General product identification and classification information\",\n      \"properties\": {\n        \"gtinOnPack\": {\n          \"type\": \"string\",\n          \"description\": \"Global Trade Item Number (GTIN) as it appears on the product packaging. This is the standard barcode number used for product identification. Example: '013562130627' for a specific product variant.\"\n        },\n        \"gtin14\": {\n          \"type\": \"string\",\n          \"description\": \"14-digit GTIN format with leading zeros. This is the standardized format for GTIN numbers, always 14 digits long. Example: '00013562130627' (same as GTIN On Pack but with leading zeros to make it 14 digits).\"\n        },\n        \"upc12\": {\n          \"type\": \"string\",\n          \"description\": \"12-digit Universal Product Code (UPC) format without check digit. This is the standard retail barcode format used in North America. Example: '001356213062' (first digit is typically 0 for standard UPC).\"\n        },\n        \"brandOwner\": {\n          \"type\": \"string\",\n          \"description\": \"NielsenIQ Brand Owner/Manufacturer name. This is the legal entity that owns the brand. Example: 'ANNIE'S HOMEGROWN INC.' for Annie's products.\"\n        },\n        \"brandName\": {\n          \"type\": \"string\",\n          \"description\": \"NielsenIQ Brand name including parent company. This combines the brand name with its parent company for clarity. Example: 'ANNIE'S (ANNIE'S HOMEGROWN INC.)' shows both the consumer-facing brand and its corporate owner.\"\n        },\n        \"department\": {\n          \"type\": \"string\",\n          \"description\": \"NielsenIQ Department category - the highest level of product classification. Example: 'FROZEN' for frozen food products, 'DAIRY' for dairy products, 'BAKERY' for bakery items.\"\n        },\n        \"superCategory\": {\n          \"type\": \"string\",\n          \"description\": \"NielsenIQ Super Category classification - the second level of product classification. Example: 'PREPARED FOODS' for ready-to-eat items, 'BEVERAGES' for drinks, 'SNACKS' for snack products.\"\n        },\n        \"category\": {\n          \"type\": \"string\",\n          \"description\": \"NielsenIQ Category classification - a more specific product grouping. Example: 'WAFFLE' for waffle products, 'YOGURT' for yogurt products, 'CHIPS' for potato chips.\"\n        },\n        \"subCategory\": {\n          \"type\": \"string\",\n          \"description\": \"NielsenIQ Sub Category classification - a detailed product grouping. Example: 'REGULAR' for standard products, 'LIGHT' for reduced-fat items, 'ORGANIC' for organic products.\"\n        },\n        \"segment\": {\n          \"type\": \"string\",\n          \"description\": \"NielsenIQ Segment classification - the most specific product grouping. Example: 'REGULAR' for standard products, 'FLAVORED' for flavored variants, 'WHOLE GRAIN' for whole grain products.\"\n        },\n        \"productTitle\": {\n          \"type\": \"string\",\n          \"description\": \"Product title from identifying header information. This is the full product name as it appears in the system. Example: 'BIRTHDAY CAKE ORGANIC WAFFLES' or 'STRAWBERRY SHORTCAKE ORGANIC WAFFLES'.\"\n        },\n        \"variant\": {\n          \"type\": \"string\",\n          \"description\": \"Product variant or flavor information. This specifies the specific version or flavor of the product. Example: 'BIRTHDAY CAKE' or 'STRAWBERRY SHORTCAKE' for different waffle flavors.\"\n        },\n        \"netWeight1Value\": {\n          \"type\": \"number\",\n          \"description\": \"Primary net weight value of the product. This is the main weight measurement. Example: 9.8 for 9.8 ounces, 280 for 280 grams.\"\n        },\n        \"netWeight1UOM\": {\n          \"type\": \"string\",\n          \"description\": \"Primary net weight unit of measurement. This specifies the unit used for the primary weight. Example: 'oz' for ounces, 'g' for grams, 'lb' for pounds.\"\n        },\n        \"netWeight2Value\": {\n          \"type\": \"number\",\n          \"description\": \"Secondary net weight value of the product. This is an alternative weight measurement, often in a different unit. Example: 280 for 280 grams when primary is in ounces.\"\n        },\n        \"netWeight2UOM\": {\n          \"type\": \"string\",\n          \"description\": \"Secondary net weight unit of measurement. This specifies the unit used for the secondary weight. Example: 'g' for grams when primary is in ounces, or 'ml' for milliliters for liquids.\"\n        },\n        \"unitsPerPack\": {\n          \"type\": \"integer\",\n          \"description\": \"Number of individual packaging units per pack. This indicates how many individual items are in the package. Example: 8 for a pack of 8 waffles, 6 for a 6-pack of yogurt.\"\n        },\n        \"unitsPerPackDescriptor\": {\n          \"type\": \"string\",\n          \"description\": \"Description of the individual packaging units. This specifies what each unit represents. Example: 'WAFFLES' for waffle products, 'BARS' for snack bars, 'POUCHES' for individual pouches.\"\n        },\n        \"storage\": {\n          \"type\": \"string\",\n          \"description\": \"Product storage requirements. This indicates how the product should be stored. Example: 'Frozen' for frozen products, 'Refrigerated' for cold items, 'Room Temperature' for shelf-stable products.\"\n        },\n        \"numberOfIngredients\": {\n          \"type\": \"integer\",\n          \"description\": \"Total number of ingredients in the product, including all nested ingredients. This should be a count of all individual ingredients at all levels of nesting.\"\n        }\n      },\n      \"required\": [\n        \"gtinOnPack\",\n        \"gtin14\",\n        \"upc12\",\n        \"brandOwner\",\n        \"brandName\",\n        \"department\",\n        \"superCategory\",\n        \"category\",\n        \"subCategory\",\n        \"segment\",\n        \"productTitle\",\n        \"variant\",\n        \"netWeight1Value\",\n        \"netWeight1UOM\",\n        \"netWeight2Value\",\n        \"netWeight2UOM\",\n        \"unitsPerPack\",\n        \"unitsPerPackDescriptor\",\n        \"storage\",\n        \"numberOfIngredients\"\n      ]\n    },\n    \"servingSize\": {\n      \"type\": \"object\",\n      \"description\": \"Product serving size information\",\n      \"properties\": {\n        \"servingSize\": {\n          \"type\": \"number\",\n          \"description\": \"Primary serving size value. This represents the standard serving amount for the product. For example: 70 for a 70g serving, 1 for a 1 cup serving, or 2 for a 2-piece serving.\"\n        },\n        \"servingSizeUnit\": {\n          \"type\": \"string\",\n          \"description\": \"Unit of measure for the primary serving size. Common units include: 'g' for grams, 'oz' for ounces, 'ml' for milliliters, 'cup' for cups, or 'piece' for individual items. Example: 'g' for a 70g serving.\"\n        },\n        \"servesPerPack\": {\n          \"type\": \"integer\",\n          \"description\": \"Number of servings per package. This indicates how many standard servings are contained in the entire package. For example: 4 means the package contains 4 servings, 6 means 6 servings, etc.\"\n        },\n        \"servingDescription\": {\n          \"type\": \"string\",\n          \"description\": \"Textual description of the serving size that provides context about how the serving size should be interpreted. Examples: '2 Waffles', '1/2 cup (120g)', '1 bar (40g)', '1 pouch (28g)'.\"\n        },\n        \"servingSize2\": {\n          \"type\": [\n            \"number\",\n            \"null\"\n          ],\n          \"description\": \"Optional secondary serving size value. This is used when a product provides an alternative serving size measurement. For example: if primary is in grams (70g), secondary might be in pieces (2 pieces). Can be null if no secondary serving size is provided.\"\n        },\n        \"servingSize2Unit\": {\n          \"type\": [\n            \"string\",\n            \"null\"\n          ],\n          \"description\": \"Unit of measure for the secondary serving size. Used in conjunction with servingSize2. Examples: 'piece' for number of items, 'oz' for ounces, 'ml' for milliliters. Can be null if no secondary serving size is provided.\"\n        }\n      },\n      \"required\": [\n        \"servingSize\",\n        \"servingSizeUnit\",\n        \"servesPerPack\",\n        \"servingDescription\"\n      ]\n    },\n    \"nutritionalInformation\": {\n      \"type\": \"object\",\n      \"description\": \"Nutritional information divided into stated (explicitly mentioned) and qualified (AI-inferred) values\",\n      \"properties\": {\n        \"stated\": {\n          \"type\": \"object\",\n          \"description\": \"Nutritional information as explicitly stated on the product packaging\",\n          \"properties\": {\n            \"totalAmount\": {\n              \"type\": \"number\",\n              \"description\": \"The total amount that all nutrient values are measured against as stated on packaging. For example, if nutrients are measured per 100g, this would be 100. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n            },\n            \"totalAmountUOM\": {\n              \"type\": \"string\",\n              \"description\": \"Unit of measurement for the total amount as stated on packaging. Common units include: 'g' for grams, 'oz' for ounces, 'ml' for milliliters. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n            },\n            \"source\": {\n              \"type\": \"string\",\n              \"enum\": [\n                \"Amazon Images\",\n                \"Product Scrape\",\n                \"ScrapingQualificationAI\"\n              ],\n              \"description\": \"Source of the stated nutritional information\"\n            },\n            \"macronutrients\": {\n              \"type\": \"array\",\n              \"description\": \"Primary nutrients essential in larger quantities as stated on packaging\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Name of the nutrient as stated. Like 'Protein', 'Carbohydrates', 'Fat', 'Fibre', 'Sugar', 'Salt', etc.\"\n                  },\n                  \"amount\": {\n                    \"type\": \"number\",\n                    \"description\": \"Amount of the nutrient as stated on packaging. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"totalAmount\": {\n                    \"type\": \"number\",\n                    \"description\": \"The total amount that the nutrient value is measured against as stated. For example, if the nutrient is 10g out of 100g, this would be 100. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"UOM\": {\n                    \"type\": \"string\",\n                    \"description\": \"Unit of measurement for the nutrient as stated, for example 'g' or 'mg' or 'oz'. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"dailyValue\": {\n                    \"type\": \"number\",\n                    \"description\": \"Percentage of recommended daily intake as stated. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"source\": {\n                    \"type\": \"string\",\n                    \"enum\": [\n                      \"Amazon Images\",\n                      \"Product Scrape\",\n                      \"ScrapingQualificationAI\"\n                    ],\n                    \"description\": \"Source of this stated nutrient information\"\n                  }\n                },\n                \"required\": [\n                  \"name\",\n                  \"amount\",\n                  \"totalAmount\",\n                  \"UOM\",\n                  \"dailyValue\",\n                  \"source\"\n                ]\n              }\n            },\n            \"micronutrients\": {\n              \"type\": \"array\",\n              \"description\": \"Essential nutrients required in smaller quantities as stated on packaging\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Name of the nutrient as stated. Like 'Vitamin A', 'Vitamin C', 'Calcium', 'Iron', 'Zinc', etc.\"\n                  },\n                  \"amount\": {\n                    \"type\": \"number\",\n                    \"description\": \"Amount of the nutrient as stated on packaging. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"totalAmount\": {\n                    \"type\": \"number\",\n                    \"description\": \"The total amount that the nutrient value is measured against as stated. For example, if the nutrient is 10mg out of 100g, this would be 100. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"UOM\": {\n                    \"type\": \"string\",\n                    \"description\": \"Unit of measurement for the nutrient as stated, for example 'g' or 'mg' or 'oz'. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"dailyValue\": {\n                    \"type\": \"number\",\n                    \"description\": \"Percentage of recommended daily intake as stated. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n                  },\n                  \"source\": {\n                    \"type\": \"string\",\n                    \"enum\": [\n                      \"Amazon Images\",\n                      \"Product Scrape\",\n                      \"ScrapingQualificationAI\"\n                    ],\n                    \"description\": \"Source of this stated nutrient information\"\n                  }\n                },\n                \"required\": [\n                  \"name\",\n                  \"amount\",\n                  \"totalAmount\",\n                  \"UOM\",\n                  \"dailyValue\",\n                  \"source\"\n                ]\n              }\n            }\n          },\n          \"required\": [\n            \"totalAmount\",\n            \"totalAmountUOM\",\n            \"source\",\n            \"macronutrients\",\n            \"micronutrients\"\n          ]\n        },\n        \"qualified\": {\n          \"type\": \"object\",\n          \"description\": \"Nutritional information inferred or calculated by AI based on ingredients and product knowledge. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\",\n          \"properties\": {\n            \"totalAmount\": {\n              \"type\": \"number\",\n              \"description\": \"The total amount that all nutrient values are measured against as inferred by AI. For example, if nutrients are estimated per 100g, this would be 100. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n            },\n            \"totalAmountUOM\": {\n              \"type\": \"string\",\n              \"description\": \"Unit of measurement for the total amount as inferred by AI. Common units include: 'g' for grams, 'oz' for ounces, 'ml' for milliliters. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n            },\n            \"source\": {\n              \"type\": \"string\",\n              \"enum\": [\n                \"Amazon Images\",\n                \"Product Scrape\",\n                \"ScrapingQualificationAI\"\n              ],\n              \"description\": \"Source of the qualified nutritional information (typically 'ScrapingQualificationAI' for inferred values)\"\n            },\n            \"macronutrients\": {\n              \"type\": \"array\",\n              \"description\": \"Primary nutrients essential in larger quantities as inferred by AI. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Name of the nutrient as inferred. Like 'Protein', 'Carbohydrates', 'Fat', 'Fibre', 'Sugar', 'Salt', etc.\"\n                  },\n                  \"amount\": {\n                    \"type\": \"number\",\n                    \"description\": \"Amount of the nutrient as estimated by AI based on ingredients and product knowledge. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"totalAmount\": {\n                    \"type\": \"number\",\n                    \"description\": \"The total amount that the nutrient value is measured against as inferred. For example, if the nutrient is estimated as 10g out of 100g, this would be 100. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"UOM\": {\n                    \"type\": \"string\",\n                    \"description\": \"Unit of measurement for the nutrient as inferred, for example 'g' or 'mg' or 'oz'. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"dailyValue\": {\n                    \"type\": \"number\",\n                    \"description\": \"Percentage of recommended daily intake as calculated by AI. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"source\": {\n                    \"type\": \"string\",\n                    \"enum\": [\n                      \"Amazon Images\",\n                      \"Product Scrape\",\n                      \"ScrapingQualificationAI\"\n                    ],\n                    \"description\": \"Source of this qualified nutrient information (typically 'ScrapingQualificationAI' for inferred values)\"\n                  }\n                },\n                \"required\": [\n                  \"name\",\n                  \"amount\",\n                  \"totalAmount\",\n                  \"UOM\",\n                  \"dailyValue\",\n                  \"source\"\n                ]\n              }\n            },\n            \"micronutrients\": {\n              \"type\": \"array\",\n              \"description\": \"Essential nutrients required in smaller quantities as inferred by AI. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Name of the nutrient as inferred. Like 'Vitamin A', 'Vitamin C', 'Calcium', 'Iron', 'Zinc', etc.\"\n                  },\n                  \"amount\": {\n                    \"type\": \"number\",\n                    \"description\": \"Amount of the nutrient as estimated by AI based on ingredients and product knowledge. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"totalAmount\": {\n                    \"type\": \"number\",\n                    \"description\": \"The total amount that the nutrient value is measured against as inferred. For example, if the nutrient is estimated as 10mg out of 100g, this would be 100. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"UOM\": {\n                    \"type\": \"string\",\n                    \"description\": \"Unit of measurement for the nutrient as inferred, for example 'g' or 'mg' or 'oz'. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"dailyValue\": {\n                    \"type\": \"number\",\n                    \"description\": \"Percentage of recommended daily intake as calculated by AI. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\"\n                  },\n                  \"source\": {\n                    \"type\": \"string\",\n                    \"enum\": [\n                      \"Amazon Images\",\n                      \"Product Scrape\",\n                      \"ScrapingQualificationAI\"\n                    ],\n                    \"description\": \"Source of this qualified nutrient information (typically 'ScrapingQualificationAI' for inferred values)\"\n                  }\n                },\n                \"required\": [\n                  \"name\",\n                  \"amount\",\n                  \"totalAmount\",\n                  \"UOM\",\n                  \"dailyValue\",\n                  \"source\"\n                ]\n              }\n            }\n          },\n          \"required\": [\n            \"totalAmount\",\n            \"totalAmountUOM\",\n            \"source\",\n            \"macronutrients\",\n            \"micronutrients\"\n          ]\n        }\n      },\n      \"required\": [\n        \"stated\",\n        \"qualified\"\n      ]\n    },\n    \"ingredients\": {\n      \"type\": \"object\",\n      \"description\": \"Ingredient information divided into stated (explicitly mentioned) and qualified (AI-inferred) values\",\n      \"properties\": {\n        \"stated\": {\n          \"type\": \"object\",\n          \"description\": \"Ingredients as explicitly stated on the product packaging\",\n          \"properties\": {\n            \"source\": {\n              \"type\": \"string\",\n              \"enum\": [\n                \"Amazon Images\",\n                \"Product Scrape\",\n                \"ScrapingQualificationAI\"\n              ],\n              \"description\": \"Source of the stated ingredient information\"\n            },\n            \"ingredientList\": {\n              \"type\": \"array\",\n              \"description\": \"List of ingredients as explicitly stated on packaging. If you find the ingredients to be nested, extract the nested ingredients as well. For example, if the text is 'Chocolate (sugar, whole milk, cocoa butter, chocolate liquor, soy lecithin 'an emulsifier' and pure vanilla)' then 'Chocolate' is the parent ingredient and 'sugar', 'whole milk', 'cocoa butter', 'chocolate liquor', 'soy lecithin 'an emulsifier' and 'pure vanilla' are the nested ingredients. All the properties of ingredients like amount, amountUOM, dietary_preference, allergens, religious_labels, food_safety_labels, sustainability_labels, etc. should be extracted for the parent as well as the nested ingredients.\",\n              \"items\": {\n                \"$ref\": \"#/definitions/Ingredient\"\n              }\n            }\n          },\n          \"required\": [\n            \"source\",\n            \"ingredientList\"\n          ]\n        },\n        \"qualified\": {\n          \"type\": \"object\",\n          \"description\": \"Ingredients as analyzed and inferred by AI based on product knowledge and additional research. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\",\n          \"properties\": {\n            \"source\": {\n              \"type\": \"string\",\n              \"enum\": [\n                \"Amazon Images\",\n                \"Product Scrape\",\n                \"ScrapingQualificationAI\"\n              ],\n              \"description\": \"Source of the qualified ingredient information (typically 'ScrapingQualificationAI' for inferred values)\"\n            },\n            \"ingredientList\": {\n              \"type\": \"array\",\n              \"description\": \"List of ingredients as analyzed by AI, including potential hidden ingredients, processing aids, or ingredients that may not be explicitly listed but are commonly found in similar products. If this information is not present in the data provided, make sure to populate based on your understanding of the data.\",\n              \"items\": {\n                \"$ref\": \"#/definitions/Ingredient\"\n              }\n            }\n          },\n          \"required\": [\n            \"source\",\n            \"ingredientList\"\n          ]\n        }\n      },\n      \"required\": [\n        \"stated\",\n        \"qualified\"\n      ]\n    },\n    \"claims\": {\n      \"type\": \"object\",\n      \"description\": \"Product claims and marketing information\",\n      \"properties\": {\n        \"certifications\": {\n          \"type\": \"array\",\n          \"description\": \"Product certifications and approvals from recognized organizations. Examples include: ['USDA Organic', 'Non-GMO Project Verified', 'Gluten-Free Certified', 'Kosher', 'Vegan Certified']. These certifications indicate that the product meets specific standards set by certifying bodies.\",\n          \"items\": {\n            \"type\": \"string\"\n          }\n        },\n        \"nutritionalClaims\": {\n          \"type\": \"array\",\n          \"description\": \"Nutritional claims and benefits stated on the product. Examples include: ['Good Source of Fiber', 'Low Sodium', 'High in Protein', 'No Added Sugar', 'Reduced Fat']. These claims must comply with regulatory guidelines for nutritional labeling.\",\n          \"items\": {\n            \"type\": \"string\"\n          }\n        },\n        \"ingredientClaims\": {\n          \"type\": \"array\",\n          \"description\": \"Claims about ingredients and their sourcing. Examples include: ['Made with Real Fruit', '100% Whole Grain', 'No Artificial Colors', 'No Preservatives', 'Locally Sourced Ingredients']. These claims highlight specific aspects of the ingredients used.\",\n          \"items\": {\n            \"type\": \"string\"\n          }\n        },\n        \"preparation\": {\n          \"type\": \"object\",\n          \"description\": \"Product preparation instructions and storage requirements\",\n          \"properties\": {\n            \"storage\": {\n              \"type\": \"string\",\n              \"description\": \"Storage instructions for maintaining product quality. Examples: 'Keep frozen until ready to use', 'Store in a cool, dry place', 'Refrigerate after opening'. These instructions are crucial for food safety and product quality.\"\n            },\n            \"heatingInstructions\": {\n              \"type\": \"object\",\n              \"description\": \"Detailed heating/cooking instructions for different preparation methods\",\n              \"properties\": {\n                \"toaster\": {\n                  \"type\": \"string\",\n                  \"description\": \"Toaster preparation instructions. Example: 'Toast on medium setting for 2-3 minutes until golden brown'. These instructions are specific to toaster preparation.\"\n                },\n                \"oven\": {\n                  \"type\": \"string\",\n                  \"description\": \"Oven preparation instructions. Example: 'Preheat oven to 375\\u00b0F, bake for 8-10 minutes until crispy'. These instructions are specific to oven preparation.\"\n                }\n              },\n              \"required\": [\n                \"toaster\",\n                \"oven\"\n              ]\n            }\n          },\n          \"required\": [\n            \"storage\",\n            \"heatingInstructions\"\n          ]\n        },\n        \"sustainability\": {\n          \"type\": \"object\",\n          \"description\": \"Environmental and sustainability claims about the product and its packaging\",\n          \"properties\": {\n            \"packaging\": {\n              \"type\": \"array\",\n              \"description\": \"Information about packaging materials and recyclability. Examples include: ['100% Recyclable Packaging', 'Made from 30% Post-Consumer Recycled Material', 'Compostable Packaging']. These claims indicate the environmental attributes of the packaging.\",\n              \"items\": {\n                \"type\": \"string\"\n              }\n            },\n            \"environmentalClaims\": {\n              \"type\": \"array\",\n              \"description\": \"Environmental impact claims about the product and its production. Examples include: ['Carbon Neutral', 'Sustainably Sourced', 'Water Conservation Practices', 'Reduced Carbon Footprint']. These claims highlight the product's environmental benefits.\",\n              \"items\": {\n                \"type\": \"string\"\n              }\n            }\n          },\n          \"required\": [\n            \"packaging\",\n            \"environmentalClaims\"\n          ]\n        },\n        \"contact\": {\n          \"type\": \"object\",\n          \"description\": \"Company contact information for customer inquiries and support\",\n          \"properties\": {\n            \"website\": {\n              \"type\": \"string\",\n              \"description\": \"Company website URL for additional information. Example: 'https://www.annies.com'. This is the primary online resource for product information.\"\n            },\n            \"phone\": {\n              \"type\": \"string\",\n              \"description\": \"Customer service phone number for inquiries. Example: '**************'. This provides direct access to customer support.\"\n            }\n          },\n          \"required\": [\n            \"website\",\n            \"phone\"\n          ]\n        }\n      },\n      \"required\": [\n        \"certifications\",\n        \"nutritionalClaims\",\n        \"ingredientClaims\",\n        \"preparation\",\n        \"sustainability\",\n        \"contact\"\n      ]\n    },\n    \"NPI 2.0 Food Packages - Allergens & Intolerances\": {\n      \"type\": \"object\",\n      \"description\": \"Allergen and intolerance information\",\n      \"properties\": {\n        \"fdaRegulatedAllergens\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI All FDA Regulated Allergens. This field lists all allergens that are regulated by the FDA and present in the product. Example: 'Contains: Milk, Eggs, Wheat, Soy' or 'May contain traces of: Tree Nuts, Peanuts'.\"\n        },\n        \"caseinQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Casein Qualified. Indicates if the product contains casein or casein derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        },\n        \"caseinStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Casein Stated. Explicit statement about casein content on the product label. Example: 'Made with Casein' or 'No Casein Added'.\"\n        },\n        \"coconutQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Coconut Qualified. Indicates if the product contains coconut or coconut derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        },\n        \"coconutStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Coconut Stated. Explicit statement about coconut content on the product label. Example: 'Made with Coconut' or 'No Coconut Added'.\"\n        },\n        \"cornQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Corn Qualified. Indicates if the product contains corn or corn derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        },\n        \"cornStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Corn Stated. Explicit statement about corn content on the product label. Example: 'Made with Corn' or 'No Corn Added'.\"\n        },\n        \"dairyLevelStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Dairy Level Stated. Specifies the level of dairy content in the product. Example: 'Contains 2% Milk' or 'Lactose-Free'.\"\n        },\n        \"dairyQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Dairy Qualified. Indicates if the product contains dairy or dairy derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        },\n        \"dairyStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Dairy Stated. Explicit statement about dairy content on the product label. Example: 'Made with Real Dairy' or 'No Dairy Added'.\"\n        },\n        \"eggLevelStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Egg Level Stated. Specifies the level of egg content in the product. Example: 'Contains Whole Eggs' or 'Egg-Free'.\"\n        },\n        \"eggQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Egg Qualified. Indicates if the product contains eggs or egg derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        },\n        \"eggStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Egg Stated. Explicit statement about egg content on the product label. Example: 'Made with Real Eggs' or 'No Eggs Added'.\"\n        },\n        \"falcpaCommonAllergensQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI FALCPA Common Allergens Qualified. Indicates if the product contains any of the major food allergens as defined by FALCPA. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        },\n        \"falcpaCommonAllergensStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI FALCPA Common Allergens Stated. Explicit statement about FALCPA-defined allergen content on the product label. Example: 'Contains: Milk, Eggs, Fish, Shellfish, Tree Nuts, Peanuts, Wheat, Soybeans'.\"\n        },\n        \"fishLevelStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Fish Level Stated. Specifies the level of fish content in the product. Example: 'Contains Fish' or 'Fish-Free'.\"\n        },\n        \"fishQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Fish Qualified. Indicates if the product contains fish or fish derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        },\n        \"fishStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Fish Stated. Explicit statement about fish content on the product label. Example: 'Made with Real Fish' or 'No Fish Added'.\"\n        },\n        \"glutenLevelStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Gluten Level Stated. Specifies the level of gluten content in the product. Example: 'Gluten-Free' or 'Contains Gluten'.\"\n        },\n        \"glutenQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Allergens & Intolerances - PI Gluten Qualified. Indicates if the product contains gluten or gluten derivatives. Extract based on known facts about the product even if not explicitly stated. Do not let this be null.\"\n        }\n      }\n    },\n    \"cleanLabel\": {\n      \"type\": \"object\",\n      \"description\": \"Clean label and ingredient quality claims\",\n      \"properties\": {\n        \"artisanalStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Artisanal Stated. Explicit statement about artisanal production methods. Example: 'Handcrafted' or 'Artisan Made'.\"\n        },\n        \"coldPressedIngredientsStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Cold Pressed Ingredients Stated. Explicit statement about cold-pressed ingredients. Example: 'Made with Cold-Pressed Oils' or 'Cold-Pressed Juices'.\"\n        },\n        \"craftStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Craft Stated. Explicit statement about craft production methods. Example: 'Craft Brewed' or 'Small Batch Crafted'.\"\n        },\n        \"gourmetStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Gourmet Stated. Explicit statement about gourmet quality. Example: 'Gourmet Quality' or 'Premium Gourmet'.\"\n        },\n        \"localStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Local Stated. Explicit statement about local sourcing. Example: 'Locally Sourced' or 'Made with Local Ingredients'.\"\n        },\n        \"madeInUsaStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Made In USA Stated. Explicit statement about USA origin. Example: 'Made in USA' or 'Product of USA'.\"\n        },\n        \"premiumStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Premium Stated. Explicit statement about premium quality. Example: 'Premium Quality' or 'Premium Ingredients'.\"\n        },\n        \"smallBatchStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Artisan/Premium - PI Small Batch Stated. Explicit statement about small batch production. Example: 'Small Batch Produced' or 'Crafted in Small Batches'.\"\n        },\n        \"animalByProductQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Animal By Product Qualified. Indicates if the product contains animal by-products. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"animalByProductStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Animal By Product Stated. Explicit statement about animal by-product content. Example: 'Made with Animal By-Products' or 'No Animal By-Products Added'.\"\n        },\n        \"antibioticsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Antibiotics Qualified. Indicates if the product contains ingredients from animals treated with antibiotics. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"antibioticsStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Antibiotics Stated. Explicit statement about antibiotic use. Example: 'No Antibiotics Ever' or 'Raised Without Antibiotics'.\"\n        },\n        \"artificialColorsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Colors Qualified. Indicates if the product contains artificial colors. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"artificialColorsStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Colors Stated. Explicit statement about artificial color content. Example: 'No Artificial Colors Added' or 'Colored with Artificial Dyes'.\"\n        },\n        \"artificialFlavorsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Flavors Qualified. Indicates if the product contains artificial flavors. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"artificialFlavorsStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Flavors Stated. Explicit statement about artificial flavor content. Example: 'No Artificial Flavors Added' or 'Flavored with Artificial Ingredients'.\"\n        },\n        \"artificialIngredientsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Ingredients Qualified. Indicates if the product contains any artificial ingredients. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"artificialPreservativesQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Preservatives Qualified. Indicates if the product contains artificial preservatives. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"artificialPreservativesStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Preservatives Stated. Explicit statement about artificial preservative content. Example: 'No Artificial Preservatives Added' or 'Preserved with Artificial Ingredients'.\"\n        },\n        \"artificialSweetenersQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Sweeteners Qualified. Indicates if the product contains artificial sweeteners. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"artificialSweetenersStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Artificial Sweeteners Stated. Explicit statement about artificial sweetener content. Example: 'No Artificial Sweeteners Added' or 'Sweetened with Artificial Sweeteners'.\"\n        },\n        \"countOfIngredientsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Count Of Ingredients Qualified. Indicates the number of ingredients in the product. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"gmoPresenceQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI GMO Presence Qualified. Indicates if the product contains genetically modified organisms. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"gmoPresenceStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI GMO Presence Stated. Explicit statement about GMO content. Example: 'Non-GMO Project Verified' or 'Made with GMO Ingredients'.\"\n        },\n        \"highFructoseCornSyrupQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI High Fructose Corn Syrup Qualified. Indicates if the product contains high fructose corn syrup. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"highFructoseCornSyrupStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI High Fructose Corn Syrup Stated. Explicit statement about high fructose corn syrup content. Example: 'No High Fructose Corn Syrup Added' or 'Sweetened with High Fructose Corn Syrup'.\"\n        },\n        \"hormonesQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Hormones Qualified. Indicates if the product contains ingredients from animals treated with hormones. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"hormonesStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Hormones Stated. Explicit statement about hormone use. Example: 'No Hormones Added' or 'Raised Without Added Hormones'.\"\n        },\n        \"naturalColorsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Colors Qualified. Indicates if the product contains natural colors. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"naturalColorsStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Colors Stated. Explicit statement about natural color content. Example: 'Colored with Natural Ingredients' or 'Naturally Colored with Fruit and Vegetable Extracts'.\"\n        },\n        \"naturalFlavorsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Flavors Qualified. Indicates if the product contains natural flavors. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"naturalFlavorsStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Flavors Stated. Explicit statement about natural flavor content. Example: 'Flavored with Natural Ingredients' or 'Naturally Flavored with Real Fruit'.\"\n        },\n        \"naturalPreservativesQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Preservatives Qualified. Indicates if the product contains natural preservatives. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"naturalPreservativesStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Preservatives Stated. Explicit statement about natural preservative content. Example: 'Preserved with Natural Ingredients' or 'Naturally Preserved with Vitamin E'.\"\n        },\n        \"naturalSweetenersQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Sweeteners Qualified. Indicates if the product contains natural sweeteners. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"naturalSweetenersStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Natural Sweeteners Stated. Explicit statement about natural sweetener content. Example: 'Sweetened with Natural Ingredients' or 'Naturally Sweetened with Honey'.\"\n        },\n        \"preservativesQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Preservatives Qualified. Indicates if the product contains any preservatives. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"preservativesStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Preservatives Stated. Explicit statement about preservative content. Example: 'No Preservatives Added' or 'Preserved to Maintain Freshness'.\"\n        },\n        \"rbstQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI RBST Qualified. Indicates if the product contains ingredients from cows treated with rBST. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"rbstStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI RBST Stated. Explicit statement about rBST content. Example: 'No rBST Added' or 'From Cows Not Treated with rBST'.\"\n        },\n        \"recognizableIngredientsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Recognizable Ingredients Qualified. Indicates if the product contains easily recognizable ingredients. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"sugarAlcoholsQualified\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Sugar Alcohols Qualified. Indicates if the product contains sugar alcohols. Extract based on known facts about the product even if not explicitly stated.\"\n        },\n        \"sugarAlcoholsStated\": {\n          \"type\": \"string\",\n          \"description\": \"NPI 2.0 Food Packages - Clean Label Trends - PI Sugar Alcohols Stated. Explicit statement about sugar alcohol content. Example: 'No Sugar Alcohols Added' or 'Sweetened with Sugar Alcohols'.\"\n        }\n      }\n    },\n    \"additionalInfo\": {\n      \"type\": \"object\",\n      \"description\": \"Additional product information and characteristics\",\n      \"properties\": {\n        \"energy\": {\n          \"type\": \"number\",\n          \"description\": \"Energy content in calories. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n        },\n        \"weight\": {\n          \"type\": \"number\",\n          \"description\": \"Total product weight in grams. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n        },\n        \"categories\": {\n          \"type\": \"string\",\n          \"description\": \"Product category classifications\"\n        },\n        \"packaging\": {\n          \"type\": \"string\",\n          \"description\": \"Type of packaging material or container. For example, 'Plastic', 'Glass', 'Paper', 'Aluminum', 'Other'\"\n        },\n        \"ecoscore\": {\n          \"type\": \"string\",\n          \"description\": \"Environmental impact score. Try to compute this from the ingredients and other product information. Follow the calculation method from Open Food Facts. Do not let this be null. Provide your best guess if not mentioned.\"\n        },\n        \"nova_group\": {\n          \"type\": \"string\",\n          \"description\": \"NOVA food classification group. Try to compute this from the ingredients and other product information. Follow the calculation method from Open Food Facts. Do not let this be null. Provide your best guess if not mentioned.\"\n        },\n        \"nutriscore_grade\": {\n          \"type\": \"string\",\n          \"description\": \"Nutri-Score grade (A to E) indicating nutritional quality. Try to compute this from the ingredients and other product information. Follow the calculation method from Open Food Facts. Do not let this be null. Provide your best guess if not mentioned.\"\n        },\n        \"data_source\": {\n          \"type\": \"string\",\n          \"description\": \"Always respond with 'Manufactruer_claims,FoodScanGenius_AI'\"\n        },\n        \"dietary_preference\": {\n          \"type\": \"object\",\n          \"description\": \"Dietary preferences of the combined ingredients\",\n          \"properties\": {\n            \"Vegan\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a vegan diet\"\n            },\n            \"Vegetarian\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a vegetarian diet\"\n            },\n            \"Pescatarian\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a pescatarian diet\"\n            },\n            \"WhiteMeatOnly\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain only white meat\"\n            },\n            \"KetoFriendly\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a ketogenic diet\"\n            },\n            \"LowFodmap\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a low FODMAP diet\"\n            }\n          }\n        },\n        \"allergens\": {\n          \"type\": \"object\",\n          \"description\": \"Allergens present in the combined ingredients\",\n          \"properties\": {\n            \"Sugar\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain sugar\"\n            },\n            \"Celery\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain celery\"\n            },\n            \"Gluten\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain gluten\"\n            },\n            \"Crustaceans\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain crustaceans\"\n            },\n            \"Eggs\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain eggs\"\n            },\n            \"Fish\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain fish\"\n            },\n            \"Lupin\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain lupin\"\n            },\n            \"Milk\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain milk\"\n            },\n            \"Peanuts\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain peanuts\"\n            },\n            \"Sesame\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain sesame\"\n            },\n            \"TreeNuts\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain tree nuts\"\n            }\n          }\n        },\n        \"religious_labels\": {\n          \"type\": \"object\",\n          \"description\": \"Religious labels present in the combined ingredients\",\n          \"properties\": {\n            \"Halal\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are Halal certified\"\n            },\n            \"Kosher\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are Kosher certified\"\n            },\n            \"Hindu\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for Hindu dietary requirements\"\n            },\n            \"Jain\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for Jain dietary requirements\"\n            }\n          }\n        },\n        \"food_safety_labels\": {\n          \"type\": \"object\",\n          \"description\": \"Food safety labels present in the combined ingredients\",\n          \"properties\": {\n            \"GMO\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain GMO components\"\n            },\n            \"NoGMO\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are GMO-free\"\n            },\n            \"Hormones\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain hormones\"\n            },\n            \"Carcinogenic\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are potentially carcinogenic\"\n            },\n            \"Organic\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are organic\"\n            },\n            \"ProductRecalls\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients have been subject to product recalls\"\n            }\n          }\n        },\n        \"sustainability_labels\": {\n          \"type\": \"object\",\n          \"description\": \"Sustainability labels present in the combined ingredients\",\n          \"properties\": {\n            \"Recycled\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are made from recycled materials\"\n            },\n            \"AnimalWelfare\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients meet animal welfare standards\"\n            },\n            \"OrganicPositioning\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are positioned as organic\"\n            },\n            \"PlantBased\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are plant-based\"\n            },\n            \"SocialResponsibility\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients meet social responsibility standards\"\n            },\n            \"SustainablePackaging\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients use sustainable packaging\"\n            }\n          }\n        },\n        \"average_customer_rating\": {\n          \"type\": \"number\",\n          \"description\": \"Average customer rating of the product. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n        },\n        \"ASIN\": {\n          \"type\": \"string\",\n          \"description\": \"Amazon Standard Identification Number. Only extract if this is mentioned in the input data. Otherwise, set to null.\"\n        },\n        \"traces\": {\n          \"type\": \"string\",\n          \"description\": \"Potential trace ingredients or contamination warnings\"\n        },\n        \"country_of_origin\": {\n          \"type\": \"string\",\n          \"description\": \"Country where the product was manufactured or produced. For example, 'United States', 'United Kingdom', 'France', 'Germany', 'Italy', 'Spain', 'Portugal', 'Greece', 'Turkey', 'Other'\"\n        },\n        \"customerCareNumber\": {\n          \"type\": \"string\",\n          \"description\": \"Contact number for customer support\"\n        },\n        \"email\": {\n          \"type\": \"string\",\n          \"description\": \"Contact email address for the manufacturer\"\n        },\n        \"websiteLink\": {\n          \"type\": \"string\",\n          \"description\": \"Official product or manufacturer website URL\"\n        }\n      },\n      \"required\": [\n        \"data_source\",\n        \"dietary_preference\",\n        \"allergens\",\n        \"religious_labels\",\n        \"food_safety_labels\",\n        \"sustainability_labels\",\n        \"country_of_origin\",\n        \"customerCareNumber\",\n        \"email\",\n        \"websiteLink\"\n      ]\n    }\n  },\n  \"definitions\": {\n    \"Ingredient\": {\n      \"type\": \"object\",\n      \"description\": \"Nested structure of ingredients used in the product\",\n      \"additionalProperties\": false,\n      \"properties\": {\n        \"text\": {\n          \"type\": \"string\",\n          \"description\": \"Name of the ingredient. Do not add more than one ingredient in the text field. For example, text can be 'Basmati Rice' or 'Biryani Paste' or 'Whole Spices' or 'Mango' or 'Chocolate' etc. and not 'Basmati Rice, Biryani Paste, Whole Spices, Mango, Chocolate'.\"\n        },\n        \"amount\": {\n          \"type\": \"number\",\n          \"description\": \"Total amount of all ingredients combined. Guesstimate if not mentioned. Do not let this be null and provide your best guess.\"\n        },\n        \"amountUOM\": {\n          \"type\": \"string\",\n          \"description\": \"Unit of measurement for the total amount. Guesstimate if not mentioned. Do not let this be null and provide your best guess.\"\n        },\n        \"dietary_preference\": {\n          \"type\": \"object\",\n          \"description\": \"Dietary preferences of the combined ingredients\",\n          \"properties\": {\n            \"Vegan\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a vegan diet\"\n            },\n            \"Vegetarian\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a vegetarian diet\"\n            },\n            \"Pescatarian\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a pescatarian diet\"\n            },\n            \"WhiteMeatOnly\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain only white meat\"\n            },\n            \"KetoFriendly\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a ketogenic diet\"\n            },\n            \"LowFodmap\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for a low FODMAP diet\"\n            }\n          }\n        },\n        \"allergens\": {\n          \"type\": \"object\",\n          \"description\": \"Allergens present in the combined ingredients\",\n          \"properties\": {\n            \"Sugar\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain sugar\"\n            },\n            \"Celery\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain celery\"\n            },\n            \"Gluten\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain gluten\"\n            },\n            \"Crustaceans\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain crustaceans\"\n            },\n            \"Eggs\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain eggs\"\n            },\n            \"Fish\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain fish\"\n            },\n            \"Lupin\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain lupin\"\n            },\n            \"Milk\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain milk\"\n            },\n            \"Peanuts\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain peanuts\"\n            },\n            \"Sesame\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain sesame\"\n            },\n            \"TreeNuts\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain tree nuts\"\n            }\n          }\n        },\n        \"religious_labels\": {\n          \"type\": \"object\",\n          \"description\": \"Religious labels present in the combined ingredients\",\n          \"properties\": {\n            \"Halal\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are Halal certified\"\n            },\n            \"Kosher\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are Kosher certified\"\n            },\n            \"Hindu\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for Hindu dietary requirements\"\n            },\n            \"Jain\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are suitable for Jain dietary requirements\"\n            }\n          }\n        },\n        \"food_safety_labels\": {\n          \"type\": \"object\",\n          \"description\": \"Food safety labels present in the combined ingredients\",\n          \"properties\": {\n            \"GMO\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain GMO components\"\n            },\n            \"NoGMO\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are GMO-free\"\n            },\n            \"Hormones\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients contain hormones\"\n            },\n            \"Carcinogenic\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are potentially carcinogenic\"\n            },\n            \"Organic\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are organic\"\n            },\n            \"ProductRecalls\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients have been subject to product recalls\"\n            }\n          }\n        },\n        \"sustainability_labels\": {\n          \"type\": \"object\",\n          \"description\": \"Sustainability labels present in the combined ingredients\",\n          \"properties\": {\n            \"Recycled\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are made from recycled materials\"\n            },\n            \"AnimalWelfare\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients meet animal welfare standards\"\n            },\n            \"OrganicPositioning\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are positioned as organic\"\n            },\n            \"PlantBased\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients are plant-based\"\n            },\n            \"SocialResponsibility\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients meet social responsibility standards\"\n            },\n            \"SustainablePackaging\": {\n              \"type\": \"boolean\",\n              \"description\": \"Whether the combined ingredients use sustainable packaging\"\n            }\n          }\n        },\n        \"subIngredients\": {\n          \"type\": \"array\",\n          \"description\": \"Any ingredients nested inside this one\",\n          \"items\": {\n            \"$ref\": \"#/definitions/Ingredient\"\n          }\n        },\n        \"source\": {\n          \"type\": \"string\",\n          \"enum\": [\n            \"Amazon Images\",\n            \"Product Scrape\",\n            \"ScrapingQualificationAI\"\n          ],\n          \"description\": \"Source of this ingredient information\"\n        }\n      },\n      \"required\": [\n        \"text\",\n        \"amount\",\n        \"amountUOM\",\n        \"dietary_preference\",\n        \"allergens\",\n        \"religious_labels\",\n        \"food_safety_labels\",\n        \"sustainability_labels\",\n        \"source\"\n      ]\n    }\n  },\n  \"required\": [\n    \"generalData\",\n    \"servingSize\",\n    \"nutritionalInformation\",\n    \"ingredients\",\n    \"claims\",\n    \"NPI 2.0 Food Packages - Allergens & Intolerances\",\n    \"cleanLabel\",\n    \"additionalInfo\"\n  ]\n}\n\nProduct details:\n$PRODUCT_DATA\n{\n  \"g\": 1354,\n  \"url\": \"https://www.amazon.com/dp/B0CKY1LY33\",\n  \"asin\": \"B0CKY1LY33\",\n  \"size\": null,\n  \"type\": \"CHOCOLATE_CANDY\",\n  \"brand\": \"Glicks\",\n  \"color\": null,\n  \"isB2B\": false,\n  \"isSNS\": false,\n  \"model\": null,\n  \"stats\": null,\n  \"style\": \"Coins\",\n  \"title\": \"Glicks Dark Chocolate Silver Coins (24 Bags = 72 Total Coins) Dairy Free Chocolate Coins | Hanukah Coins | St. Patrick\\u2019s Day Chocolate Coins | Premium Rich Kosher Dark Chocolate Coins | Gluten Free | Product Of Israel\",\n  \"author\": null,\n  \"coupon\": null,\n  \"format\": null,\n  \"images\": [\n    {\n      \"l\": \"91vtctbBHTL.jpg\",\n      \"m\": \"51ygp1NrjBL.jpg\",\n      \"lH\": 2278,\n      \"lW\": 2023,\n      \"mH\": 500,\n      \"mW\": 444\n    },\n    {\n      \"l\": \"81pqcJQc4QL.jpg\",\n      \"m\": \"5102YKwZSYL.jpg\",\n      \"lH\": 1904,\n      \"lW\": 2560,\n      \"mH\": 372,\n      \"mW\": 500\n    },\n    {\n      \"l\": \"81gVD1WCP4L.jpg\",\n      \"m\": \"51-aoPwKH-L.jpg\",\n      \"lH\": 2000,\n      \"lW\": 2000,\n      \"mH\": 500,\n      \"mW\": 500\n    }\n  ],\n  \"offers\": null,\n  \"binding\": null,\n  \"eanList\": null,\n  \"edition\": null,\n  \"fbaFees\": {\n    \"lastUpdate\": 7555352,\n    \"pickAndPackFee\": 455\n  },\n  \"upcList\": null,\n  \"urlSlug\": \"Glicks-Chocolate-Hanukah-Chanukah-Premium\",\n  \"domainId\": 1,\n  \"features\": [\n    \"RICH CHOCOLATE \\u2013 Glicks chocolate coins are made with rich and dark chocolate that is melt in your mouth delicious, if you are looking for gold coin candy that will be a winner with all, you\\u2019ve hit the jackpot.\",\n    \"GREAT FOR GIFTING \\u2013 Treat your loved ones with the great holiday gift of gold coin chocolate, whether they are spending the holiday with you or from afar, they are sure to delight in the gift of chocolate gold coins.\",\n    \"GET CREATIVE \\u2013 Getting our chocolate coins bulk allows for some creative fun! Gift our gold chocolate coins in your goodie bags, use them to decorate your holiday tables and play party games with, or add them to your desserts for an extra special holiday element\",\n    \"WHAT YOU GET \\u2013 Our chocolate gold coins bulk pack comes with 24 individual bags with about 3 coins in each bag. Our bags are great for school events, party favors, prizes and more.\",\n    \"CERTIFIED \\u2013 Glicks Dark Chocolate Coins are certified Kosher non dairy and gluten free.\"\n  ],\n  \"imagesCSV\": \"91vtctbBHTL.jpg,81pqcJQc4QL.jpg,81gVD1WCP4L.jpg\",\n  \"itemWidth\": 0,\n  \"languages\": null,\n  \"launchpad\": false,\n  \"unitCount\": {\n    \"unitType\": \"Ounce\",\n    \"unitValue\": 12.7\n  },\n  \"hasReviews\": true,\n  \"itemHeight\": 0,\n  \"itemLength\": 0,\n  \"itemWeight\": 0,\n  \"lastUpdate\": 7564664,\n  \"parentAsin\": null,\n  \"partNumber\": null,\n  \"promotions\": null,\n  \"description\": \"Glicks chocolate coins offer a delectable experience with their rich, dark chocolate that simply melts in your mouth, making them a crowd-pleaser. Whether you're seeking a delightful holiday gift or looking to get creative with your treats, these gold chocolate coins fit the bill. Our chocolate gold coins are packed in convenient individual bags, each containing approximately 3 coins, making them ideal for various occasions like school events, party favors, and prizes. Moreover, Glicks Dark Chocolate Coins are certified Kosher non dairy and gluten-free, ensuring a treat that suits diverse dietary preferences.\",\n  \"listedSince\": 6721426,\n  \"productType\": 0,\n  \"releaseDate\": -1,\n  \"categoryTree\": [\n    {\n      \"name\": \"Grocery & Gourmet Food\",\n      \"catId\": 16310101\n    },\n    {\n      \"name\": \"Snacks & Sweets\",\n      \"catId\": 23759921011\n    },\n    {\n      \"name\": \"Chocolate Candy\",\n      \"catId\": 23759922011\n    },\n    {\n      \"name\": \"Assortment Boxes\",\n      \"catId\": 115815603011\n    }\n  ],\n  \"manufacturer\": null,\n  \"packageWidth\": 112,\n  \"productGroup\": \"Grocery\",\n  \"rootCategory\": 16310101,\n  \"variationCSV\": null,\n  \"newPriceIsMAP\": false,\n  \"numberOfItems\": 72,\n  \"numberOfPages\": -1,\n  \"packageHeight\": 95,\n  \"packageLength\": 199,\n  \"packageWeight\": 355,\n  \"trackingSince\": 6774788,\n  \"ebayListingIds\": null,\n  \"isAdultProduct\": false,\n  \"isRedirectASIN\": false,\n  \"lastEbayUpdate\": 0,\n  \"lastSoldUpdate\": 7500376,\n  \"isHeatSensitive\": false,\n  \"itemTypeKeyword\": \"chocolate-candy-assortment-boxes\",\n  \"lastPriceChange\": 7324628,\n  \"liveOffersOrder\": null,\n  \"packageQuantity\": -1,\n  \"publicationDate\": -1,\n  \"lastRatingUpdate\": 7562648,\n  \"offersSuccessful\": false,\n  \"availabilityAmazon\": -1,\n  \"referralFeePercent\": 15,\n  \"salesRankReference\": 16310101,\n  \"websiteDisplayGroup\": \"grocery_display_on_website\",\n  \"isEligibleForTradeIn\": false,\n  \"buyBoxSellerIdHistory\": null,\n  \"referralFeePercentage\": 15.01,\n  \"salesRankDisplayGroup\": \"grocery_display_on_website\",\n  \"websiteDisplayGroupName\": \"Grocery\",\n  \"frequentlyBoughtTogether\": [\n    \"B07ZJP5NC2\",\n    \"B0184IFB08\"\n  ],\n  \"buyBoxEligibleOfferCounts\": [\n    1,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0\n  ],\n  \"isEligibleForSuperSaverShipping\": true\n}\n\nScraped Data:\n$SCRAPED_DATA\n![](https://fls-\nna.amazon.com/1/batch/1/OP/ATVPDKIKX0DER:131-1068421-8105349:K66DDHHJD6523D8R92SE$uedata=s:%2Frd%2Fuedata%3Fstaticb%26id%3DK66DDHHJD6523D8R92SE:0)\n![](https://m.media-amazon.com/images/G/01/gno/sprites/nav-sprite-\nglobal-1x-reorg-privacy._CB546805360_.png) Shortcuts menu\n\n## Skip to\n\n  * [ Main content ](https://www.amazon.com/dp/B0CKY1LY33#skippedLink)\n  * [ About this item ](https://www.amazon.com/dp/B0CKY1LY33#featurebullets_feature_div)\n  * [ About this item ](https://www.amazon.com/dp/B0CKY1LY33#nic-po-expander-heading)\n  * [ About this item ](https://www.amazon.com/dp/B0CKY1LY33#productFactsDesktopExpander)\n  * [ Buying options ](https://www.amazon.com/dp/B0CKY1LY33#buybox)\n  * [ Compare with similar items ](https://www.amazon.com/dp/B0CKY1LY33#product-comparison_feature_div)\n  * [ Videos ](https://www.amazon.com/dp/B0CKY1LY33#va-related-videos-widget_feature_div)\n  * [ Reviews ](https://www.amazon.com/dp/B0CKY1LY33#customerReviews)\n\n* * *\n##  Keyboard shortcuts\n\n  * [ Search alt + / ](https://www.amazon.com/dp/B0CKY1LY33#twotabsearchtextbox)\n  * [ Cart shift + alt + C ](https://www.amazon.com/gp/cart/view.html/?ref_=nav_assist)\n  * [ Home shift + alt + H ](https://www.amazon.com/?ref_=nav_assist)\n  * [ Orders shift + alt + O ](https://www.amazon.com/gp/css/order-history/?ref_=nav_assist)\n  * Add to cart\nshift + alt + K\n\n  * Open/close shortcuts menu\nshift + alt + Z\n\nTo move between items, use your keyboard's up or down arrows.\n\n[ .us ](https://www.amazon.com/ref=nav_logo)\n\n[ Deliver to  India  ](https://www.amazon.com/dp/B0CKY1LY33)\n\nAll\n\nSelect the department you want to search in All Departments Arts & Crafts\nAutomotive Baby Beauty & Personal Care Books Boys' Fashion Computers Deals\nDigital Music Electronics Girls' Fashion Health & Household Home & Kitchen\nIndustrial & Scientific Kindle Store Luggage Men's Fashion Movies & TV Music,\nCDs & Vinyl Pet Supplies Prime Video Software Sports & Outdoors Tools & Home\nImprovement Toys & Games Video Games Women's Fashion\n\nSearch Amazon\n\n[ EN ](https://www.amazon.com/customer-\npreferences/edit?ie=UTF8&preferencesReturnUrl=%2F&ref_=topnav_lang_ais)\n\n[ Hello, sign in Account & Lists\n](https://www.amazon.com/ap/signin?openid.pape.max_auth_age=0&openid.return_to=https%3A%2F%2Fwww.amazon.com%2Fdp%2FB0CKY1LY33%3Fref_%3Dnav_ya_signin&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.assoc_handle=usflex&openid.mode=checkid_setup&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0)\n\n[ Returns & Orders ](https://www.amazon.com/gp/css/order-\nhistory?ref_=nav_orders_first) [ 0 Cart\n](https://www.amazon.com/gp/cart/view.html?ref_=nav_cart)\n\n[ All ](javascript:%20void\\(0\\))\n\n  * [Today's Deals](https://www.amazon.com/gp/goldbox?ref_=nav_cs_gb)\n  * [Registry](https://www.amazon.com/gp/browse.html?node=***********&ref_=nav_cs_registry)\n  * [Prime Video](https://www.amazon.com/Amazon-Video/b/?ie=UTF8&node=**********&ref_=nav_cs_prime_video)\n  * [Gift Cards](https://www.amazon.com/gift-cards/b/?ie=UTF8&node=**********&ref_=nav_cs_gc)\n  * [Customer Service](https://www.amazon.com/gp/help/customer/display.html?nodeId=508510&ref_=nav_cs_customerservice)\n  * [Sell](https://www.amazon.com/b/?_encoding=UTF8&ld=AZUSSOA-sell&node=12766669011&ref_=nav_cs_sell)\n[Disability Customer\nSupport](https://www.amazon.com/gp/help/customer/accessibility)\n\n[ Grocery  ](https://www.amazon.com/grocery-breakfast-foods-snacks-\norganic/b/?ie=UTF8&node=16310101&ref_=topnav_storetab_grocery_sn_fo) [ Deals\n](https://www.amazon.com/Sales-\nGrocery/b/?ie=UTF8&node=52129011&ref_=sv_grocery_sn_fo_1) [ Snacks\n](https://www.amazon.com/Snacks-Chips-Cookies-Gum-Gluten-\nFree/b/?ie=UTF8&node=16322721&ref_=sv_grocery_sn_fo_2) [ Breakfast\n](https://www.amazon.com/Breakfast-Foods-\nGrocery/b/?ie=UTF8&node=16310251&ref_=sv_grocery_sn_fo_3) [ Warm Beverages\n](https://www.amazon.com/b/?ie=UTF8&node=16521305011&ref_=sv_grocery_sn_fo_4) [\nCold Beverages  ](https://www.amazon.com/Cold-\nBeverages/b/?ie=UTF8&node=14808787011&ref_=sv_grocery_sn_fo_5) [ Cooking Staples\n](https://www.amazon.com/Canned-Jarred-Packaged-\nFoods/b/?ie=UTF8&node=6464939011&ref_=sv_grocery_sn_fo_6) [ Baby Food\n](https://www.amazon.com/Baby-Food-Formula-\nPouches/b/?ie=UTF8&node=16323111&ref_=sv_grocery_sn_fo_7) [ Candy & Chocolate\n](https://www.amazon.com/Candy-\nChocolate/b/?ie=UTF8&node=16322461&ref_=sv_grocery_sn_fo_8) [ Subscribe & Save\n](https://www.amazon.com/Subscribe-\nSave/b/?ie=UTF8&node=5856181011&ref_=sv_grocery_sn_fo_9) [ International Foods\n](https://www.amazon.com/b/?ie=UTF8&node=17428419011&ref_=sv_grocery_sn_fo_10) [\nSNAP-eligible Groceries  ](https://www.amazon.com/snap-\nebt/b/?ie=UTF8&node=19097785011&ref_=sv_grocery_sn_fo_11)\n\nAmazon.com: Glicks Dark Chocolate Silver Coins (24 Bags = 72 Total Coins) Dairy Free Chocolate Coins | Hanukah Coins | St. Patrick’s Day Chocolate Coins | Premium Rich Kosher Dark Chocolate Coins | Gluten Free | Product Of Israel : Grocery & Gourmet Food\nSponsored\n\n  * [Grocery & Gourmet Food](https://www.amazon.com/grocery-breakfast-foods-snacks-organic/b/ref=dp_bc_1?ie=UTF8&node=16310101)\n  * ›\n  * [Snacks & Sweets](https://www.amazon.com/Snacks-Sweets/b/ref=dp_bc_2?ie=UTF8&node=23759921011)\n  * ›\n  * [Chocolate Candy](https://www.amazon.com/Chocolate/b/ref=dp_bc_3?ie=UTF8&node=23759922011)\n  * ›\n  * [Assortment Boxes](https://www.amazon.com/Chocolate-Candy-Assortment-Boxes/b/ref=dp_bc_4?ie=UTF8&node=115815603011)\n\nNo featured offers available  \n[ Learn more ](javascript:void\\(0\\))\n\nNo featured offers available\n\nWe feature offers with an Add to Cart button when an offer meets our high\nstandards for:\n\n  * Quality Price,\n  * Reliable delivery option, and\n  * Seller who offers good customer service\n\n“No featured offers available” means no offers currently meet all of these\nexpectations. Select See All Buying Options to shop available offers.\n\nThis item cannot be shipped to your selected delivery location. Please choose a\ndifferent delivery location.  \n\nSimilar items shipping to India\n\nIN\n\nIndia\n\nSee Similar Items\n\n[ Deliver to India ](https://www.amazon.com/dp/B0CKY1LY33)\n\n* * *\n[ Add to List\n](https://www.amazon.com/ap/signin?openid.return_to=https%3A%2F%2Fwww.amazon.com%2Fgp%2Faw%2Fd%2FB0CKY1LY33&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.assoc_handle=usflex&openid.mode=checkid_setup&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0&\n\"Add to List\")\n\nAdded to\n\n[ ](https://www.amazon.com/gp/registry/wishlist/)\n\nUnable to add item to List. Please try again.\n\n###  Sorry, there was a problem.\n\nThere was an error retrieving your Wish Lists. Please try again.\n\n###  Sorry, there was a problem.\n\nList unavailable.\n\nSponsored\n\n[](javascript:void\\(0\\) \"Share\")\n\n  * ![](https://m.media-amazon.com/images/G/01/HomeCustomProduct/360_icon_73x73v2._CB485971279_SX38_SY50_CR,0,0,38,50_FMpng_RI_.png)\n  *   * ![](https://m.media-amazon.com/images/I/51ygp1NrjBL._SX38_SY50_CR,0,0,38,50_.jpg) 3+\n  * ![](https://m.media-amazon.com/images/I/5102YKwZSYL._SX38_SY50_CR,0,0,38,50_.jpg) 2+\n  * ![](https://m.media-amazon.com/images/I/51-aoPwKH-L._SX38_SY50_CR,0,0,38,50_.jpg) 1+\n\n#### Image Unavailable\n\nImage not available for  \nColor:\n\n  * ![Glicks Dark Chocolate Silver Coins \\(24 Bags = 72 Total Coins\\) Dairy Free Chocolate Coins | Hanukah Coins | St. Patrick’s Day Chocolate Coins | Premium Rich Kosher Dark Chocolate Coins | Gluten Free | Product Of Israel](https://m.media-amazon.com/images/I/91vtctbBHTL._SX385_PIbundle-72,TopRight,0,0_SX385SY434SH20_.jpg)\n  * To view this video download [ Flash Player ](https://get.adobe.com/flashplayer)\n\nRoll over image to zoom in\n\n  * [ VIDEOS ](https://www.amazon.com/dp/B0CKY1LY33)\n  * [ 360° VIEW ](https://www.amazon.com/dp/B0CKY1LY33)\n  * [ IMAGES ](https://www.amazon.com/dp/B0CKY1LY33)\n  * [ ](https://www.amazon.com/dp/B0CKY1LY33)\n\n![](https://m.media-amazon.com/images/S/sash//pLB3SkYb3bHZzHQ.svg)\n\n![](https://m.media-amazon.com/images/S/sash//swRPyHOrgnz358_.svg)\n\n#  Glicks Dark Chocolate Silver Coins (24 Bags = 72 Total Coins) Dairy Free Chocolate Coins | Hanukah Coins | St. Patrick’s Day Chocolate Coins | Premium Rich Kosher Dark Chocolate Coins | Gluten Free | Product Of Israel \n[Brand:\nGlicks](https://www.amazon.com/Glicks/b/ref=bl_dp_s_web_17927485011?ie=UTF8&node=17927485011&field-\nlbr_brands_browse-bin=Glicks)\n\n[ 3.9  _3.9 out of 5 stars_ ](javascript:void\\(0\\)) [ 18 ratings\n](https://www.amazon.com/dp/B0CKY1LY33#averageCustomerReviewsAnchor)\n\n|  [ Search this page  ](https://www.amazon.com/dp/B0CKY1LY33#Ask)\n\n* * *\nThis item cannot be shipped to your selected delivery location. Please choose a\ndifferent delivery location.  \n\nBrand |  Glicks  \n---|---  \nOccasion |  Holiday  \nChocolate Type |  Dark  \nFlavor |  Dark Chocolate  \nUnit Count |  12.7 Ounce  \n* * *\n#  About this item\n\n  * RICH CHOCOLATE – Glicks chocolate coins are made with rich and dark chocolate that is melt in your mouth delicious, if you are looking for gold coin candy that will be a winner with all, you’ve hit the jackpot. \n  * GREAT FOR GIFTING – Treat your loved ones with the great holiday gift of gold coin chocolate, whether they are spending the holiday with you or from afar, they are sure to delight in the gift of chocolate gold coins. \n  * GET CREATIVE – Getting our chocolate coins bulk allows for some creative fun! Gift our gold chocolate coins in your goodie bags, use them to decorate your holiday tables and play party games with, or add them to your desserts for an extra special holiday element \n  * WHAT YOU GET – Our chocolate gold coins bulk pack comes with 24 individual bags with about 3 coins in each bag. Our bags are great for school events, party favors, prizes and more. \n  * CERTIFIED – Glicks Dark Chocolate Coins are certified Kosher non dairy and gluten free. \n\n› [ See more product details\n](https://www.amazon.com/dp/B0CKY1LY33#productDetails)\n\n[](https://www.amazon.com/dp/B0CKY1LY33)\n\n* * *\nSponsored\n\n[](https://aax-us-\niad.amazon.com/x/c/JNWUw75qYAc9qBQoXK5ULOgAAAGW7KeaHwEAAAH2AQBvbm9fdHhuX2JpZDYgICBvbm9fdHhuX2ltcDIgICD8uP9i/https://www.amazon.com/stores/page/254591A2-8068-4775-B985-B2A6F5D98558/?_encoding=UTF8&store_ref=SB_A0187499F9GGVFXIG51C-A09443886WMDIQVVSP5U&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&lp_asins=B081T67P84%2CB085N8P7P3&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad1&ref_=sbx_be_dp_arbies_lsi4d_bkgd&pd_rd_w=Th4Ri&content-\nid=amzn1.sym.e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_p=e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n* * *\n## Brand in this category on Amazon\n\n[![Raaka](https://m.media-amazon.com/images/S/al-\nna-9d5791cf-3faf/fffb11f4-7dc5-416f-bcf7-c2cbc6372783._CR0,0,1705,1029_AC_SX130_SY60_QL70_.jpg)](https://aax-\nus-\niad.amazon.com/x/c/JNWUw75qYAc9qBQoXK5ULOgAAAGW7KeaHwEAAAH2AQBvbm9fdHhuX2JpZDYgICBvbm9fdHhuX2ltcDIgICD8uP9i/https://www.amazon.com/stores/page/254591A2-8068-4775-B985-B2A6F5D98558/?_encoding=UTF8&store_ref=SB_A0187499F9GGVFXIG51C-A09443886WMDIQVVSP5U&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&lp_asins=B081T67P84%2CB085N8P7P3&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad1&ref_=sbx_be_dp_arbies_lsi4d_logo&pd_rd_w=Th4Ri&content-\nid=amzn1.sym.e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_p=e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n[Raaka Unsweetened Vegan Dark ChocolateRaaka Unsweetened Vegan Dark\nChocolate](https://aax-us-\niad.amazon.com/x/c/JNWUw75qYAc9qBQoXK5ULOgAAAGW7KeaHwEAAAH2AQBvbm9fdHhuX2JpZDYgICBvbm9fdHhuX2ltcDIgICD8uP9i/https://www.amazon.com/stores/page/254591A2-8068-4775-B985-B2A6F5D98558/?_encoding=UTF8&store_ref=SB_A0187499F9GGVFXIG51C-A09443886WMDIQVVSP5U&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&lp_asins=B081T67P84%2CB085N8P7P3&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad1&ref_=sbx_be_dp_arbies_lsi4d_hl&pd_rd_w=Th4Ri&content-\nid=amzn1.sym.e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_p=e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n[Shop Raaka Shop now ](https://aax-us-\niad.amazon.com/x/c/JNWUw75qYAc9qBQoXK5ULOgAAAGW7KeaHwEAAAH2AQBvbm9fdHhuX2JpZDYgICBvbm9fdHhuX2ltcDIgICD8uP9i/https://www.amazon.com/stores/page/254591A2-8068-4775-B985-B2A6F5D98558/?_encoding=UTF8&store_ref=SB_A0187499F9GGVFXIG51C-A09443886WMDIQVVSP5U&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&lp_asins=B081T67P84%2CB085N8P7P3&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad1&ref_=sbx_be_dp_arbies_lsi4d_cta&pd_rd_w=Th4Ri&content-\nid=amzn1.sym.e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_p=e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n[![Branded image from Raaka](https://m.media-amazon.com/images/S/al-\nna-9d5791cf-3faf/4b7fb3b5-c687-4a6d-93e0-695fd89e3909._CR29,0,2002,1048_SX507_QL70_.jpg)](https://aax-\nus-\niad.amazon.com/x/c/JNWUw75qYAc9qBQoXK5ULOgAAAGW7KeaHwEAAAH2AQBvbm9fdHhuX2JpZDYgICBvbm9fdHhuX2ltcDIgICD8uP9i/https://www.amazon.com/stores/page/254591A2-8068-4775-B985-B2A6F5D98558/?_encoding=UTF8&store_ref=SB_A0187499F9GGVFXIG51C-A09443886WMDIQVVSP5U&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&lp_asins=B081T67P84%2CB085N8P7P3&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad1&ref_=sbx_be_dp_arbies_lsi4d_ls&pd_rd_w=Th4Ri&content-\nid=amzn1.sym.e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_p=e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n[](https://aax-us-\niad.amazon.com/x/c/JNWUw75qYAc9qBQoXK5ULOgAAAGW7KeaHwEAAAH2AQBvbm9fdHhuX2JpZDYgICBvbm9fdHhuX2ltcDIgICD8uP9i/https://www.amazon.com/gp/aw/d/B081T67P84/?_encoding=UTF8&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&sr=1-1-undefined&ref_=sbx_be_dp_arbies_lsi4d_asin_0_bkgd&pd_rd_w=Th4Ri&content-\nid=amzn1.sym.e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_p=e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)[![Raaka\n100% Dark Chocolate Bars - Unsweetened, Bitter, Sugar Free, Pure Cacao -\nOrganic, Vegan, Soy Free, Gluten Free, Kosher, Keto, Nut Free - 1.8oz 100%\nChocolate Bars, 3-Pack](https://m.media-\namazon.com/images/I/819k0elJpFL._FMavif_AC_SR160,134_PQ50_.jpg)](https://aax-us-\niad.amazon.com/x/c/JNWUw75qYAc9qBQoXK5ULOgAAAGW7KeaHwEAAAH2AQBvbm9fdHhuX2JpZDYgICBvbm9fdHhuX2ltcDIgICD8uP9i/https://www.amazon.com/gp/aw/d/B081T67P84/?_encoding=UTF8&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&sr=1-1-undefined&ref_=sbx_be_dp_arbies_lsi4d_asin_0_img&pd_rd_w=Th4Ri&content-\nid=amzn1.sym.e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_p=e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n[](https://aax-us-\niad.amazon.com/x/c/JNWUw75qYAc9qBQoXK5ULOgAAAGW7KeaHwEAAAH2AQBvbm9fdHhuX2JpZDYgICBvbm9fdHhuX2ltcDIgICD8uP9i/https://www.amazon.com/gp/aw/d/B081T67P84/?_encoding=UTF8&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&sr=1-1-undefined&ref_=sbx_be_dp_arbies_lsi4d_asin_0_bkgd&pd_rd_w=Th4Ri&content-\nid=amzn1.sym.e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_p=e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n[Raaka 100% Dark Chocolate Bars - Unsweetened, Bitter, Sugar Free, Pure Cacao -\nOrganic, Vegan, Soy Free, Gluten Free, Kosher, Keto, Nut Free - 1.8oz 100%\nChocolate Bars, 3-PackRaaka 100% Dark Chocolate Bars…](https://aax-us-\niad.amazon.com/x/c/JNWUw75qYAc9qBQoXK5ULOgAAAGW7KeaHwEAAAH2AQBvbm9fdHhuX2JpZDYgICBvbm9fdHhuX2ltcDIgICD8uP9i/https://www.amazon.com/gp/aw/d/B081T67P84/?_encoding=UTF8&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&sr=1-1-undefined&ref_=sbx_be_dp_arbies_lsi4d_asin_0_title&pd_rd_w=Th4Ri&content-\nid=amzn1.sym.e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_p=e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n[ _3.9 out of 5 stars._ 706 ](https://aax-us-\niad.amazon.com/x/c/JNWUw75qYAc9qBQoXK5ULOgAAAGW7KeaHwEAAAH2AQBvbm9fdHhuX2JpZDYgICBvbm9fdHhuX2ltcDIgICD8uP9i/https://www.amazon.com/gp/aw/d/B081T67P84/?_encoding=UTF8&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&sr=1-1-undefined&ref_=sbx_be_dp_arbies_lsi4d_asin_0_rating&pd_rd_w=Th4Ri&content-\nid=amzn1.sym.e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_p=e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2#customerReviews)\n\n[](https://aax-us-iad.amazon.com/x/c/JNWUw75qYAc9qBQoXK5ULOgAAAGW7KeaHwEAAAH2AQBvbm9fdHhuX2JpZDYgICBvbm9fdHhuX2ltcDIgICD8uP9i/https://www.amazon.com/gp/aw/d/B085N8P7P3/?_encoding=UTF8&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&sr=1-2-undefined&ref_=sbx_be_dp_arbies_lsi4d_asin_1_bkgd&pd_rd_w=Th4Ri&content-id=amzn1.sym.e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_p=e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)[![Raaka 100% Dark Chocolate Gift Box | Unsweetened, No Sugar Added Chocolate | Organic, Vegan, Gluten Free, Paleo | Box of 30 Individually Wrapped Chocolates](https://m.media-amazon.com/images/I/71-kRmaTNsL._FMavif_AC_SR160,134_PQ50_.jpg)](https://aax-us-iad.amazon.com/x/c/JNWUw75qYAc9qBQoXK5ULOgAAAGW7KeaHwEAAAH2AQBvbm9fdHhuX2JpZDYgICBvbm9fdHhuX2ltcDIgICD8uP9i/https://www.amazon.com/gp/aw/d/B085N8P7P3/?_encoding=UTF8&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&sr=1-2-undefined&ref_=sbx_be_dp_arbies_lsi4d_asin_1_img&pd_rd_w=Th4Ri&content-id=amzn1.sym.e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_p=e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n[](https://aax-us-\niad.amazon.com/x/c/JNWUw75qYAc9qBQoXK5ULOgAAAGW7KeaHwEAAAH2AQBvbm9fdHhuX2JpZDYgICBvbm9fdHhuX2ltcDIgICD8uP9i/https://www.amazon.com/gp/aw/d/B085N8P7P3/?_encoding=UTF8&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&sr=1-2-undefined&ref_=sbx_be_dp_arbies_lsi4d_asin_1_bkgd&pd_rd_w=Th4Ri&content-\nid=amzn1.sym.e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_p=e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n[Raaka 100% Dark Chocolate Gift Box | Unsweetened, No Sugar Added Chocolate | Organic, Vegan, Gluten Free, Paleo | Box of 30 Individually Wrapped ChocolatesRaaka 100% Dark Chocolate Gift B…](https://aax-us-iad.amazon.com/x/c/JNWUw75qYAc9qBQoXK5ULOgAAAGW7KeaHwEAAAH2AQBvbm9fdHhuX2JpZDYgICBvbm9fdHhuX2ltcDIgICD8uP9i/https://www.amazon.com/gp/aw/d/B085N8P7P3/?_encoding=UTF8&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&sr=1-2-undefined&ref_=sbx_be_dp_arbies_lsi4d_asin_1_title&pd_rd_w=Th4Ri&content-id=amzn1.sym.e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_p=e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n[ _3.9 out of 5 stars._ 379 ](https://aax-us-\niad.amazon.com/x/c/JNWUw75qYAc9qBQoXK5ULOgAAAGW7KeaHwEAAAH2AQBvbm9fdHhuX2JpZDYgICBvbm9fdHhuX2ltcDIgICD8uP9i/https://www.amazon.com/gp/aw/d/B085N8P7P3/?_encoding=UTF8&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&sr=1-2-undefined&ref_=sbx_be_dp_arbies_lsi4d_asin_1_rating&pd_rd_w=Th4Ri&content-\nid=amzn1.sym.e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_p=e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2#customerReviews)\n\n[](https://aax-us-\niad.amazon.com/x/c/JNWUw75qYAc9qBQoXK5ULOgAAAGW7KeaHwEAAAH2AQBvbm9fdHhuX2JpZDYgICBvbm9fdHhuX2ltcDIgICD8uP9i/https://www.amazon.com/stores/page/254591A2-8068-4775-B985-B2A6F5D98558/?_encoding=UTF8&store_ref=SB_A0187499F9GGVFXIG51C-A09443886WMDIQVVSP5U&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&lp_asins=B081T67P84%2CB085N8P7P3&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad1&ref_=sbx_be_dp_arbies_lsi4d_bkgd&pd_rd_w=Th4Ri&content-\nid=amzn1.sym.e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_p=e3bc2133-ada0-4c48-8d74-989f69ff3ad2&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\nSponsored\n\n![](https://aax-us-\niad.amazon.com/e/loi/imp?b=JNWUw75qYAc9qBQoXK5ULOgAAAGW7KeaHwEAAAH2AQBvbm9fdHhuX2JpZDYgICBvbm9fdHhuX2ltcDIgICD8uP9i)\n\n##  Product Description\n\nGlicks chocolate coins offer a delectable experience with their rich, dark\nchocolate that simply melts in your mouth, making them a crowd-pleaser. Whether\nyou're seeking a delightful holiday gift or looking to get creative with your\ntreats, these gold chocolate coins fit the bill. Our chocolate gold coins are\npacked in convenient individual bags, each containing approximately 3 coins,\nmaking them ideal for various occasions like school events, party favors, and\nprizes. Moreover, Glicks Dark Chocolate Coins are certified Kosher non dairy and\ngluten-free, ensuring a treat that suits diverse dietary preferences.\n\n* * *\n## Product details\n\n  * Package Dimensions ‏ : ‎  7.83 x 4.41 x 3.74 inches; 12.52 ounces\n  * ASIN ‏ : ‎  B0CKY1LY33\n  * Units ‏ : ‎  12.7 Ounce\n\n  * Best Sellers Rank:  #95,868 in Grocery & Gourmet Food ([See Top 100 in Grocery & Gourmet Food](https://www.amazon.com/gp/bestsellers/grocery/ref=pd_zg_ts_grocery)) \n    * #1,000 in [Chocolate Candy Assortment Boxes](https://www.amazon.com/gp/bestsellers/grocery/115815603011/ref=pd_zg_hrsr_grocery)\n\n  * Customer Reviews: \n[ 3.9  _3.9 out of 5 stars_ ](javascript:void\\(0\\)) [ 18 ratings\n](https://www.amazon.com/dp/B0CKY1LY33#averageCustomerReviewsAnchor)\n\nBrief content visible, double tap to read full content.\n\nFull content visible, double tap to read brief content.\n\n## Videos\n\nHelp others learn more about this product by uploading a video!\n\n[Upload your\nvideo](https://www.amazon.com/creatorhub/video/upload?productASIN=B0CKY1LY33&referringURL=ZHAvQjBDS1kxTFkzMw%3D%3D&ref=RVSW)\n\n* * *\n## Important information\n\nLegal Disclaimer\n\nStatements regarding dietary supplements have not been evaluated by the FDA and\nare not intended to diagnose, treat, cure, or prevent any disease or health\ncondition.\n\n* * *\n## Products related to this item\n\n[ Sponsored  ](https://www.amazon.com/dp/B0CKY1LY33#sp_detail2_feedbackForm)\n\nPage 1 of 1[Start over](https://www.amazon.com/dp/B0CKY1LY33)Page 1 of 1\n\n[_Previous page of related Sponsored\nProducts_](https://www.amazon.com/dp/B0CKY1LY33)\n\n  1. [Feedback](javascript:void\\(0\\))\n[ ![Bulyoou 200 Pcs Fake Gold Coins and 12 Pcs Canvas Money Bags with Drawstring\nPirate Treasure Hunt Prop Money Coins Gold Coin Toy for Halloween Pirate\nAdventure Party Birthday Decorations](https://m.media-\namazon.com/images/I/81mzeu23zpL._AC_UF480,480_SR480,480_.jpg) Bulyoou 200 Pcs\nFake Gold Coins and 12 Pcs Canvas Money Bags with Drawstring Pirate...\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=********************************************************************************&url=%2Fdp%2FB0D7PJ4B56%2Fref%3Dsspa_dk_detail_0%3Fpsc%3D1%26pd_rd_i%3DB0D7PJ4B56%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy\n\"Bulyoou 200 Pcs Fake Gold Coins and 12 Pcs Canvas Money Bags with Drawstring\nPirate Treasure Hunt Prop Money Coins Gold Coin Toy for Halloween Pirate\nAdventure Party Birthday Decorations\")\n\n[ 4\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=********************************************************************************&url=%2Fdp%2FB0D7PJ4B56%2Fref%3Dsspa_dk_detail_0%3Fpsc%3D1%26pd_rd_i%3DB0D7PJ4B56%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy#customerReviews)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=********************************************************************************&url=%2Fdp%2FB0D7PJ4B56%2Fref%3Dsspa_dk_detail_0%3Fpsc%3D1%26pd_rd_i%3DB0D7PJ4B56%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n  2. [Feedback](javascript:void\\(0\\))\n[ ![BestStar 200 Pack Disposable Soup Bowls with Silver Rim, 12oz Premium Heavy\nDuty Plastic Dinner Bowls for Party, Wedding and Holiday](https://m.media-\namazon.com/images/I/71zpAaGjfdL._AC_UF480,480_SR480,480_.jpg) BestStar 200 Pack\nDisposable Soup Bowls with Silver Rim, 12oz Premium Heavy Duty…\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=********************************************************************************&url=%2Fdp%2FB0CZLMWNSF%2Fref%3Dsspa_dk_detail_1%3Fpsc%3D1%26pd_rd_i%3DB0CZLMWNSF%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy\n\"BestStar 200 Pack Disposable Soup Bowls with Silver Rim, 12oz Premium Heavy\nDuty Plastic Dinner Bowls for Party, Wedding and Holiday\")\n\n[ 196\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=********************************************************************************&url=%2Fdp%2FB0CZLMWNSF%2Fref%3Dsspa_dk_detail_1%3Fpsc%3D1%26pd_rd_i%3DB0CZLMWNSF%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy#customerReviews)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=********************************************************************************&url=%2Fdp%2FB0CZLMWNSF%2Fref%3Dsspa_dk_detail_1%3Fpsc%3D1%26pd_rd_i%3DB0CZLMWNSF%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n  3. [Feedback](javascript:void\\(0\\))\n[ ![DANFORTH Pewter Four Leaf Clover Pocket Tokens, Shamrock Coins, Good Luck\nCharm, Pewter, Made in USA, Gift Bag \\(Pack of 10\\)](https://m.media-\namazon.com/images/I/71Ilw-ze2WL._AC_UF480,480_SR480,480_.jpg) DANFORTH Pewter\nFour Leaf Clover Pocket Tokens, Shamrock Coins, Good Luck Charm, Pe...\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=********************************************************************************&url=%2Fdp%2FB079LCQ4JT%2Fref%3Dsspa_dk_detail_2%3Fpsc%3D1%26pd_rd_i%3DB079LCQ4JT%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy\n\"DANFORTH Pewter Four Leaf Clover Pocket Tokens, Shamrock Coins, Good Luck\nCharm, Pewter, Made in USA, Gift Bag \\(Pack of 10\\)\")\n\n[ 99\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=********************************************************************************&url=%2Fdp%2FB079LCQ4JT%2Fref%3Dsspa_dk_detail_2%3Fpsc%3D1%26pd_rd_i%3DB079LCQ4JT%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy#customerReviews)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=********************************************************************************&url=%2Fdp%2FB079LCQ4JT%2Fref%3Dsspa_dk_detail_2%3Fpsc%3D1%26pd_rd_i%3DB079LCQ4JT%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n  4. [Feedback](javascript:void\\(0\\))\n[ ![Organic Dental Floss Picks for Kids - Natural Biodegradable Floss Sticks\nwith Strong Bamboo Charcoal Thread & Vegan Corn Starch Handle - No Plastics & No\nArtificial Flavours](https://m.media-\namazon.com/images/I/61pysGcPcCL._AC_UF480,480_SR480,480_.jpg) Organic Dental\nFloss Picks for Kids - Natural Biodegradable Floss Sticks with Stron...\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=********************************************************************************&url=%2Fdp%2FB0CZTNPXDX%2Fref%3Dsspa_dk_detail_3%3Fpsc%3D1%26pd_rd_i%3DB0CZTNPXDX%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy\n\"Organic Dental Floss Picks for Kids - Natural Biodegradable Floss Sticks with\nStrong Bamboo Charcoal Thread & Vegan Corn Starch Handle - No Plastics & No\nArtificial Flavours\")\n\n[ 74\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=********************************************************************************&url=%2Fdp%2FB0CZTNPXDX%2Fref%3Dsspa_dk_detail_3%3Fpsc%3D1%26pd_rd_i%3DB0CZTNPXDX%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy#customerReviews)\n\n[ _ Amazon's Choice _\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=********************************************************************************&url=%2Fdp%2FB0CZTNPXDX%2Fref%3Dsspa_dk_detail_3%3Fpsc%3D1%26pd_rd_i%3DB0CZTNPXDX%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=********************************************************************************&url=%2Fdp%2FB0CZTNPXDX%2Fref%3Dsspa_dk_detail_3%3Fpsc%3D1%26pd_rd_i%3DB0CZTNPXDX%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n  5. [Feedback](javascript:void\\(0\\))\n[ ![Beistle 100 Piece Silver Embossed Plastic Coins for Western Casino and\nPirate Theme Treasure Chest Party Favors, 1.5\"](https://m.media-\namazon.com/images/I/51vFt+bw1WS._AC_UF480,480_SR480,480_.jpg) Beistle 100 Piece\nSilver Embossed Plastic Coins for Western Casino and Pirate Theme...\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=********************************************************************************&url=%2Fdp%2FB000R4MSQI%2Fref%3Dsspa_dk_detail_4%3Fpsc%3D1%26pd_rd_i%3DB000R4MSQI%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy\n\"Beistle 100 Piece Silver Embossed Plastic Coins for Western Casino and Pirate\nTheme Treasure Chest Party Favors, 1.5\"\")\n\n[ 301\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=********************************************************************************&url=%2Fdp%2FB000R4MSQI%2Fref%3Dsspa_dk_detail_4%3Fpsc%3D1%26pd_rd_i%3DB000R4MSQI%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy#customerReviews)\n\n[ $8.59$8.59\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=********************************************************************************&url=%2Fdp%2FB000R4MSQI%2Fref%3Dsspa_dk_detail_4%3Fpsc%3D1%26pd_rd_i%3DB000R4MSQI%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=********************************************************************************&url=%2Fdp%2FB000R4MSQI%2Fref%3Dsspa_dk_detail_4%3Fpsc%3D1%26pd_rd_i%3DB000R4MSQI%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n  6. [Feedback](javascript:void\\(0\\))\n[ ![Gold Metal Pirate Coins - 35 Gold Treasure Coin Set, Metal Replica Spanish\nDoubloons for Board Games, Tokens, Toys, Cosplay - Realistic Money Imitation,\nPirate Treasure Chest, Medium Size 7/8\"](https://m.media-\namazon.com/images/I/81r1iP66Z5L._AC_UF480,480_SR480,480_.jpg) Gold Metal Pirate\nCoins - 35 Gold Treasure Coin Set, Metal Replica Spanish Doubloon...\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToyMjg1MjY2NTU0NjAyOTQ0OjE3NDc3MjcxMjc6c3BfZGV0YWlsMjoyMDAwMjAwNDAwODY2ODE6Ojo6&url=%2Fdp%2FB07YNWB26M%2Fref%3Dsspa_dk_detail_5%3Fpsc%3D1%26pd_rd_i%3DB07YNWB26M%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy\n\"Gold Metal Pirate Coins - 35 Gold Treasure Coin Set, Metal Replica Spanish\nDoubloons for Board Games, Tokens, Toys, Cosplay - Realistic Money Imitation,\nPirate Treasure Chest, Medium Size 7/8\"\")\n\n[ 3,598\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToyMjg1MjY2NTU0NjAyOTQ0OjE3NDc3MjcxMjc6c3BfZGV0YWlsMjoyMDAwMjAwNDAwODY2ODE6Ojo6&url=%2Fdp%2FB07YNWB26M%2Fref%3Dsspa_dk_detail_5%3Fpsc%3D1%26pd_rd_i%3DB07YNWB26M%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy#customerReviews)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToyMjg1MjY2NTU0NjAyOTQ0OjE3NDc3MjcxMjc6c3BfZGV0YWlsMjoyMDAwMjAwNDAwODY2ODE6Ojo6&url=%2Fdp%2FB07YNWB26M%2Fref%3Dsspa_dk_detail_5%3Fpsc%3D1%26pd_rd_i%3DB07YNWB26M%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n  7. [Feedback](javascript:void\\(0\\))\n[ ![Kosher Sticker Labels, \\(24 Pack\\) 8 Blue Dairy, 8 Red Meat, 8 Green Parve\nStickers, Ovenproof up to 500°, Freezable, Microwavable, Dishwasher Safe,\nEnglish, Color Coded Kitchen Tools \\(Rectangle\\)](https://m.media-\namazon.com/images/I/71RosnH75JL._AC_UF480,480_SR480,480_.jpg) Kosher Sticker\nLabels, (24 Pack) 8 Blue Dairy, 8 Red Meat, 8 Green Parve Stickers, ...\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToyMjg1MjY2NTU0NjAyOTQ0OjE3NDc3MjcxMjc6c3BfZGV0YWlsMjozMDA3NTg1ODc0NTY2MDI6Ojo6&url=%2Fdp%2FB09CWMGVV2%2Fref%3Dsspa_dk_detail_6%3Fpsc%3D1%26pd_rd_i%3DB09CWMGVV2%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy\n\"Kosher Sticker Labels, \\(24 Pack\\) 8 Blue Dairy, 8 Red Meat, 8 Green Parve\nStickers, Ovenproof up to 500°, Freezable, Microwavable, Dishwasher Safe,\nEnglish, Color Coded Kitchen Tools \\(Rectangle\\)\")\n\n[ 98\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToyMjg1MjY2NTU0NjAyOTQ0OjE3NDc3MjcxMjc6c3BfZGV0YWlsMjozMDA3NTg1ODc0NTY2MDI6Ojo6&url=%2Fdp%2FB09CWMGVV2%2Fref%3Dsspa_dk_detail_6%3Fpsc%3D1%26pd_rd_i%3DB09CWMGVV2%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy#customerReviews)\n\n[ _ Amazon's Choice _\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToyMjg1MjY2NTU0NjAyOTQ0OjE3NDc3MjcxMjc6c3BfZGV0YWlsMjozMDA3NTg1ODc0NTY2MDI6Ojo6&url=%2Fdp%2FB09CWMGVV2%2Fref%3Dsspa_dk_detail_6%3Fpsc%3D1%26pd_rd_i%3DB09CWMGVV2%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n[\n](https://www.amazon.com/sspa/click?ie=UTF8&spc=MToyMjg1MjY2NTU0NjAyOTQ0OjE3NDc3MjcxMjc6c3BfZGV0YWlsMjozMDA3NTg1ODc0NTY2MDI6Ojo6&url=%2Fdp%2FB09CWMGVV2%2Fref%3Dsspa_dk_detail_6%3Fpsc%3D1%26pd_rd_i%3DB09CWMGVV2%26pd_rd_w%3D77WZS%26content-\nid%3Damzn1.sym.953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_p%3D953c7d66-4120-4d22-a777-f19dbfa69309%26pf_rd_r%3DK66DDHHJD6523D8R92SE%26pd_rd_wg%3DkKVxF%26pd_rd_r%3Dd8450f24-4c85-4597-a18c-9abea42060e2%26s%3Dgrocery%26sp_csd%3Dd2lkZ2V0TmFtZT1zcF9kZXRhaWwy)\n\n[_Next page of related Sponsored\nProducts_](https://www.amazon.com/dp/B0CKY1LY33)\n\n* * *\nSponsored\n\n* * *\n## Brands in this category on Amazon\n\nSponsored\n\n  * [](https://aax-us-iad.amazon.com/x/c/JBCkl6k7ddJVIK8LLH1fPTMAAAGW7KeaLQEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICCZ1nBB/https://www.amazon.com/stores/page/254591A2-8068-4775-B985-B2A6F5D98558/?_encoding=UTF8&store_ref=SB_A0187499F9GGVFXIG51C-A09443886WMDIQVVSP5U&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&lp_asins=B081T67P84%2CB085N8P7P3&lp_slot=desktop-arbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad1&ref_=sbx_be_dp_arbies_mblsd_mb0_bkgd&pd_rd_w=9oC0N&content-id=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n[![Branded image from Raaka](https://m.media-amazon.com/images/S/al-\nna-9d5791cf-3faf/4b7fb3b5-c687-4a6d-93e0-695fd89e3909._CR29,0,2002,1048_SX460_QL70_.jpg)](https://aax-\nus-\niad.amazon.com/x/c/JBCkl6k7ddJVIK8LLH1fPTMAAAGW7KeaLQEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICCZ1nBB/https://www.amazon.com/stores/page/254591A2-8068-4775-B985-B2A6F5D98558/?_encoding=UTF8&store_ref=SB_A0187499F9GGVFXIG51C-A09443886WMDIQVVSP5U&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&lp_asins=B081T67P84%2CB085N8P7P3&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad1&ref_=sbx_be_dp_arbies_mblsd_mb0_ls&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n[](https://aax-us-\niad.amazon.com/x/c/JBCkl6k7ddJVIK8LLH1fPTMAAAGW7KeaLQEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICCZ1nBB/https://www.amazon.com/stores/page/254591A2-8068-4775-B985-B2A6F5D98558/?_encoding=UTF8&store_ref=SB_A0187499F9GGVFXIG51C-A09443886WMDIQVVSP5U&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&lp_asins=B081T67P84%2CB085N8P7P3&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad1&ref_=sbx_be_dp_arbies_mblsd_mb0_bkgd&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)[![Raaka](https://m.media-\namazon.com/images/S/al-\nna-9d5791cf-3faf/fffb11f4-7dc5-416f-bcf7-c2cbc6372783._CR0,0,1705,1029_AC_SX96_SY48_QL70_.jpg)](https://aax-\nus-\niad.amazon.com/x/c/JBCkl6k7ddJVIK8LLH1fPTMAAAGW7KeaLQEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICCZ1nBB/https://www.amazon.com/stores/page/254591A2-8068-4775-B985-B2A6F5D98558/?_encoding=UTF8&store_ref=SB_A0187499F9GGVFXIG51C-A09443886WMDIQVVSP5U&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&lp_asins=B081T67P84%2CB085N8P7P3&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad1&ref_=sbx_be_dp_arbies_mblsd_mb0_logo&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n[](https://aax-us-\niad.amazon.com/x/c/JBCkl6k7ddJVIK8LLH1fPTMAAAGW7KeaLQEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICCZ1nBB/https://www.amazon.com/stores/page/254591A2-8068-4775-B985-B2A6F5D98558/?_encoding=UTF8&store_ref=SB_A0187499F9GGVFXIG51C-A09443886WMDIQVVSP5U&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&lp_asins=B081T67P84%2CB085N8P7P3&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad1&ref_=sbx_be_dp_arbies_mblsd_mb0_bkgd&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)[Raaka\nUnsweetened Vegan Dark ChocolateRaaka Unsweetened Vegan Dark\nChocolate](https://aax-us-\niad.amazon.com/x/c/JBCkl6k7ddJVIK8LLH1fPTMAAAGW7KeaLQEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICCZ1nBB/https://www.amazon.com/stores/page/254591A2-8068-4775-B985-B2A6F5D98558/?_encoding=UTF8&store_ref=SB_A0187499F9GGVFXIG51C-A09443886WMDIQVVSP5U&pd_rd_plhdr=t&aaxitk=ccce6a6f851a23d1bf856487a862256a&hsa_cr_id=0&lp_asins=B081T67P84%2CB085N8P7P3&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad1&ref_=sbx_be_dp_arbies_mblsd_mb0_hl&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n![](https://aax-us-\niad.amazon.com/e/loi/imp?b=JBCkl6k7ddJVIK8LLH1fPTMAAAGW7KeaLQEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICCZ1nBB)\n\n  * [](https://aax-us-iad.amazon.com/x/c/JL9SHf_HhRIm2-vZ8iOaSOUAAAGW7KeaLgEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICAdz72x/https://www.amazon.com/stores/page/D5B975F9-F35D-4EF6-9EF6-EBCE05150B76/?_encoding=UTF8&store_ref=SB_A01827592605KZE4IKJ06-A0561070MGSQPERT4IX8&pd_rd_plhdr=t&aaxitk=7136e96a94b9565677a02d3453fa596d&hsa_cr_id=0&lp_asins=B0CK8LKFSD%2CB0B3LN1CD3%2CB09WQQ8MHF&lp_slot=desktop-arbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad2&ref_=sbx_be_dp_arbies_mblsd_mb1_bkgd&pd_rd_w=9oC0N&content-id=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n[![Branded image from Red delight](https://m.media-amazon.com/images/S/al-\nna-9d5791cf-3faf/013f0b4b-96d2-4456-8f7d-8c4020a10d37._CR0,1,1200,629_SX460_QL70_.jpg)](https://aax-\nus-\niad.amazon.com/x/c/JL9SHf_HhRIm2-vZ8iOaSOUAAAGW7KeaLgEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICAdz72x/https://www.amazon.com/stores/page/D5B975F9-F35D-4EF6-9EF6-EBCE05150B76/?_encoding=UTF8&store_ref=SB_A01827592605KZE4IKJ06-A0561070MGSQPERT4IX8&pd_rd_plhdr=t&aaxitk=7136e96a94b9565677a02d3453fa596d&hsa_cr_id=0&lp_asins=B0CK8LKFSD%2CB0B3LN1CD3%2CB09WQQ8MHF&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad2&ref_=sbx_be_dp_arbies_mblsd_mb1_ls&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n[](https://aax-us-\niad.amazon.com/x/c/JL9SHf_HhRIm2-vZ8iOaSOUAAAGW7KeaLgEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICAdz72x/https://www.amazon.com/stores/page/D5B975F9-F35D-4EF6-9EF6-EBCE05150B76/?_encoding=UTF8&store_ref=SB_A01827592605KZE4IKJ06-A0561070MGSQPERT4IX8&pd_rd_plhdr=t&aaxitk=7136e96a94b9565677a02d3453fa596d&hsa_cr_id=0&lp_asins=B0CK8LKFSD%2CB0B3LN1CD3%2CB09WQQ8MHF&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad2&ref_=sbx_be_dp_arbies_mblsd_mb1_bkgd&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)[![Red\ndelight](https://m.media-amazon.com/images/S/al-\nna-9d5791cf-3faf/7567d84e-bd6b-4532-af02-d5637cb16279._CR0,0,600,600_AC_SX96_SY48_QL70_.png)](https://aax-\nus-\niad.amazon.com/x/c/JL9SHf_HhRIm2-vZ8iOaSOUAAAGW7KeaLgEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICAdz72x/https://www.amazon.com/stores/page/D5B975F9-F35D-4EF6-9EF6-EBCE05150B76/?_encoding=UTF8&store_ref=SB_A01827592605KZE4IKJ06-A0561070MGSQPERT4IX8&pd_rd_plhdr=t&aaxitk=7136e96a94b9565677a02d3453fa596d&hsa_cr_id=0&lp_asins=B0CK8LKFSD%2CB0B3LN1CD3%2CB09WQQ8MHF&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad2&ref_=sbx_be_dp_arbies_mblsd_mb1_logo&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n[](https://aax-us-\niad.amazon.com/x/c/JL9SHf_HhRIm2-vZ8iOaSOUAAAGW7KeaLgEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICAdz72x/https://www.amazon.com/stores/page/D5B975F9-F35D-4EF6-9EF6-EBCE05150B76/?_encoding=UTF8&store_ref=SB_A01827592605KZE4IKJ06-A0561070MGSQPERT4IX8&pd_rd_plhdr=t&aaxitk=7136e96a94b9565677a02d3453fa596d&hsa_cr_id=0&lp_asins=B0CK8LKFSD%2CB0B3LN1CD3%2CB09WQQ8MHF&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad2&ref_=sbx_be_dp_arbies_mblsd_mb1_bkgd&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)[Delicious\nKeto & Sugar Free ChocolatesDelicious Keto & Sugar Free Chocolates](https://aax-\nus-\niad.amazon.com/x/c/JL9SHf_HhRIm2-vZ8iOaSOUAAAGW7KeaLgEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICAdz72x/https://www.amazon.com/stores/page/D5B975F9-F35D-4EF6-9EF6-EBCE05150B76/?_encoding=UTF8&store_ref=SB_A01827592605KZE4IKJ06-A0561070MGSQPERT4IX8&pd_rd_plhdr=t&aaxitk=7136e96a94b9565677a02d3453fa596d&hsa_cr_id=0&lp_asins=B0CK8LKFSD%2CB0B3LN1CD3%2CB09WQQ8MHF&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad2&ref_=sbx_be_dp_arbies_mblsd_mb1_hl&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n![](https://aax-us-\niad.amazon.com/e/loi/imp?b=JL9SHf_HhRIm2-vZ8iOaSOUAAAGW7KeaLgEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICAdz72x)\n\n  * [](https://aax-us-iad.amazon.com/x/c/JKZrAzMxROX8vwcycIfnjNQAAAGW7KeaLwEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICAMSFJM/https://www.amazon.com/stores/page/4C52F8EF-DE66-4DA7-AE42-82D672DC60AD/?_encoding=UTF8&store_ref=SB_A041637611VHAXRS8NROG-A02094602XOYUC3NAXOUE&pd_rd_plhdr=t&aaxitk=9e64fe21ef7198a9c33fb0f682060eea&hsa_cr_id=0&lp_asins=B09SJ43TTY%2CB0BHH19PY2%2CB09SJ431TD&lp_slot=desktop-arbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad3&ref_=sbx_be_dp_arbies_mblsd_mb2_bkgd&pd_rd_w=9oC0N&content-id=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n[![Branded image from Ferrero Rocher](https://m.media-amazon.com/images/S/al-\nna-9d5791cf-3faf/272a4767-dc74-48cc-\nbba2-5f1e4f467d0b._CR0,527,2500,1309_SX460_QL70_.jpg)](https://aax-us-\niad.amazon.com/x/c/JKZrAzMxROX8vwcycIfnjNQAAAGW7KeaLwEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICAMSFJM/https://www.amazon.com/stores/page/4C52F8EF-\nDE66-4DA7-AE42-82D672DC60AD/?_encoding=UTF8&store_ref=SB_A041637611VHAXRS8NROG-A02094602XOYUC3NAXOUE&pd_rd_plhdr=t&aaxitk=9e64fe21ef7198a9c33fb0f682060eea&hsa_cr_id=0&lp_asins=B09SJ43TTY%2CB0BHH19PY2%2CB09SJ431TD&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad3&ref_=sbx_be_dp_arbies_mblsd_mb2_ls&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n[](https://aax-us-\niad.amazon.com/x/c/JKZrAzMxROX8vwcycIfnjNQAAAGW7KeaLwEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICAMSFJM/https://www.amazon.com/stores/page/4C52F8EF-\nDE66-4DA7-AE42-82D672DC60AD/?_encoding=UTF8&store_ref=SB_A041637611VHAXRS8NROG-A02094602XOYUC3NAXOUE&pd_rd_plhdr=t&aaxitk=9e64fe21ef7198a9c33fb0f682060eea&hsa_cr_id=0&lp_asins=B09SJ43TTY%2CB0BHH19PY2%2CB09SJ431TD&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad3&ref_=sbx_be_dp_arbies_mblsd_mb2_bkgd&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)[![Ferrero\nRocher](https://m.media-amazon.com/images/S/al-\nna-9d5791cf-3faf/31648e66-1735-4351-9fa6-67f7e50c78da._CR0,0,1000,421_AC_SX96_SY48_QL70_.jpg)](https://aax-\nus-\niad.amazon.com/x/c/JKZrAzMxROX8vwcycIfnjNQAAAGW7KeaLwEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICAMSFJM/https://www.amazon.com/stores/page/4C52F8EF-\nDE66-4DA7-AE42-82D672DC60AD/?_encoding=UTF8&store_ref=SB_A041637611VHAXRS8NROG-A02094602XOYUC3NAXOUE&pd_rd_plhdr=t&aaxitk=9e64fe21ef7198a9c33fb0f682060eea&hsa_cr_id=0&lp_asins=B09SJ43TTY%2CB0BHH19PY2%2CB09SJ431TD&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad3&ref_=sbx_be_dp_arbies_mblsd_mb2_logo&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n[](https://aax-us-\niad.amazon.com/x/c/JKZrAzMxROX8vwcycIfnjNQAAAGW7KeaLwEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICAMSFJM/https://www.amazon.com/stores/page/4C52F8EF-\nDE66-4DA7-AE42-82D672DC60AD/?_encoding=UTF8&store_ref=SB_A041637611VHAXRS8NROG-A02094602XOYUC3NAXOUE&pd_rd_plhdr=t&aaxitk=9e64fe21ef7198a9c33fb0f682060eea&hsa_cr_id=0&lp_asins=B09SJ43TTY%2CB0BHH19PY2%2CB09SJ431TD&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad3&ref_=sbx_be_dp_arbies_mblsd_mb2_bkgd&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)[Ferrero\nRocher Bars - Celebrate the MomentFerrero Rocher Bars - Celebrate the\nMoment](https://aax-us-\niad.amazon.com/x/c/JKZrAzMxROX8vwcycIfnjNQAAAGW7KeaLwEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICAMSFJM/https://www.amazon.com/stores/page/4C52F8EF-\nDE66-4DA7-AE42-82D672DC60AD/?_encoding=UTF8&store_ref=SB_A041637611VHAXRS8NROG-A02094602XOYUC3NAXOUE&pd_rd_plhdr=t&aaxitk=9e64fe21ef7198a9c33fb0f682060eea&hsa_cr_id=0&lp_asins=B09SJ43TTY%2CB0BHH19PY2%2CB09SJ431TD&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad3&ref_=sbx_be_dp_arbies_mblsd_mb2_hl&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n  * [](https://aax-us-iad.amazon.com/x/c/JJpnDd2okk2X_T-lo2UG3t0AAAGW7KeaMAEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICDO8E9q/https://www.amazon.com/stores/page/CE4CC1BF-493C-40EE-A7C2-8C5CB33BE231/?_encoding=UTF8&store_ref=SB_A0366585A31N6IRH0GKT-A0158708ORT0OOOCVYH8&pd_rd_plhdr=t&aaxitk=6740d61f3673bd6dfae2b4dd9109d9b5&hsa_cr_id=0&lp_asins=B0D27Q6S2Q%2CB07YBVKHNB%2CB0BWKYN5W7&lp_slot=desktop-arbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad4&ref_=sbx_be_dp_arbies_mblsd_mb3_bkgd&pd_rd_w=9oC0N&content-id=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n[![Branded image from M&M’S WORLD](https://m.media-amazon.com/images/S/al-\nna-9d5791cf-3faf/f6141846-67d1-4ff7-90e2-4263e3ac9bf4._CR0,783,1365,715_SX460_QL70_.jpg)](https://aax-\nus-iad.amazon.com/x/c/JJpnDd2okk2X_T-\nlo2UG3t0AAAGW7KeaMAEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICDO8E9q/https://www.amazon.com/stores/page/CE4CC1BF-493C-40EE-A7C2-8C5CB33BE231/?_encoding=UTF8&store_ref=SB_A0366585A31N6IRH0GKT-A0158708ORT0OOOCVYH8&pd_rd_plhdr=t&aaxitk=6740d61f3673bd6dfae2b4dd9109d9b5&hsa_cr_id=0&lp_asins=B0D27Q6S2Q%2CB07YBVKHNB%2CB0BWKYN5W7&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad4&ref_=sbx_be_dp_arbies_mblsd_mb3_ls&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n[](https://aax-us-iad.amazon.com/x/c/JJpnDd2okk2X_T-\nlo2UG3t0AAAGW7KeaMAEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICDO8E9q/https://www.amazon.com/stores/page/CE4CC1BF-493C-40EE-A7C2-8C5CB33BE231/?_encoding=UTF8&store_ref=SB_A0366585A31N6IRH0GKT-A0158708ORT0OOOCVYH8&pd_rd_plhdr=t&aaxitk=6740d61f3673bd6dfae2b4dd9109d9b5&hsa_cr_id=0&lp_asins=B0D27Q6S2Q%2CB07YBVKHNB%2CB0BWKYN5W7&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad4&ref_=sbx_be_dp_arbies_mblsd_mb3_bkgd&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)[![M&M’S\nWORLD](https://m.media-amazon.com/images/S/al-\nna-9d5791cf-3faf/80b2b806-06e9-43e3-8472-b758b2ab4935._CR0,0,1250,1042_AC_SX96_SY48_QL70_.jpg)](https://aax-\nus-iad.amazon.com/x/c/JJpnDd2okk2X_T-\nlo2UG3t0AAAGW7KeaMAEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICDO8E9q/https://www.amazon.com/stores/page/CE4CC1BF-493C-40EE-A7C2-8C5CB33BE231/?_encoding=UTF8&store_ref=SB_A0366585A31N6IRH0GKT-A0158708ORT0OOOCVYH8&pd_rd_plhdr=t&aaxitk=6740d61f3673bd6dfae2b4dd9109d9b5&hsa_cr_id=0&lp_asins=B0D27Q6S2Q%2CB07YBVKHNB%2CB0BWKYN5W7&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad4&ref_=sbx_be_dp_arbies_mblsd_mb3_logo&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n[](https://aax-us-iad.amazon.com/x/c/JJpnDd2okk2X_T-\nlo2UG3t0AAAGW7KeaMAEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICDO8E9q/https://www.amazon.com/stores/page/CE4CC1BF-493C-40EE-A7C2-8C5CB33BE231/?_encoding=UTF8&store_ref=SB_A0366585A31N6IRH0GKT-A0158708ORT0OOOCVYH8&pd_rd_plhdr=t&aaxitk=6740d61f3673bd6dfae2b4dd9109d9b5&hsa_cr_id=0&lp_asins=B0D27Q6S2Q%2CB07YBVKHNB%2CB0BWKYN5W7&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad4&ref_=sbx_be_dp_arbies_mblsd_mb3_bkgd&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)[Bring\nfutures. Delicious treats.Bring futures. Delicious treats.](https://aax-us-\niad.amazon.com/x/c/JJpnDd2okk2X_T-\nlo2UG3t0AAAGW7KeaMAEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICDO8E9q/https://www.amazon.com/stores/page/CE4CC1BF-493C-40EE-A7C2-8C5CB33BE231/?_encoding=UTF8&store_ref=SB_A0366585A31N6IRH0GKT-A0158708ORT0OOOCVYH8&pd_rd_plhdr=t&aaxitk=6740d61f3673bd6dfae2b4dd9109d9b5&hsa_cr_id=0&lp_asins=B0D27Q6S2Q%2CB07YBVKHNB%2CB0BWKYN5W7&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad4&ref_=sbx_be_dp_arbies_mblsd_mb3_hl&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n  * [](https://aax-us-iad.amazon.com/x/c/JA2GipYA8Q1G8tGbWCDU8i8AAAGW7KeaMQEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICConQu5/https://www.amazon.com/stores/page/1F894943-CCD2-468A-982F-0A1219A94C69/?_encoding=UTF8&store_ref=SB_A04914591V4W1B273T0F6-A03713292LNNK77D94UBG&pd_rd_plhdr=t&aaxitk=4d3bd56f6f39210e6b5523b8f01e1091&hsa_cr_id=0&lp_asins=B07CYLP8D1%2CB08WHCYH8Q%2CB0CTBJCR95&lp_slot=desktop-arbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad5&ref_=sbx_be_dp_arbies_mblsd_mb4_bkgd&pd_rd_w=9oC0N&content-id=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n[![Branded image from Just Candy](https://m.media-amazon.com/images/S/al-\nna-9d5791cf-3faf/2c2854a2-22ed-43e4-9959-42a7f733b4cf._CR0,0,1376,720_SX460_QL70_.jpg)](https://aax-\nus-\niad.amazon.com/x/c/JA2GipYA8Q1G8tGbWCDU8i8AAAGW7KeaMQEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICConQu5/https://www.amazon.com/stores/page/1F894943-CCD2-468A-982F-0A1219A94C69/?_encoding=UTF8&store_ref=SB_A04914591V4W1B273T0F6-A03713292LNNK77D94UBG&pd_rd_plhdr=t&aaxitk=4d3bd56f6f39210e6b5523b8f01e1091&hsa_cr_id=0&lp_asins=B07CYLP8D1%2CB08WHCYH8Q%2CB0CTBJCR95&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad5&ref_=sbx_be_dp_arbies_mblsd_mb4_ls&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n[](https://aax-us-\niad.amazon.com/x/c/JA2GipYA8Q1G8tGbWCDU8i8AAAGW7KeaMQEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICConQu5/https://www.amazon.com/stores/page/1F894943-CCD2-468A-982F-0A1219A94C69/?_encoding=UTF8&store_ref=SB_A04914591V4W1B273T0F6-A03713292LNNK77D94UBG&pd_rd_plhdr=t&aaxitk=4d3bd56f6f39210e6b5523b8f01e1091&hsa_cr_id=0&lp_asins=B07CYLP8D1%2CB08WHCYH8Q%2CB0CTBJCR95&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad5&ref_=sbx_be_dp_arbies_mblsd_mb4_bkgd&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)[![Just\nCandy](https://m.media-amazon.com/images/S/al-\nna-9d5791cf-3faf/53c16dc7-23cf-4694-8a55-7f84fc343422._CR0,0,512,512_AC_SX96_SY48_QL70_.jpg)](https://aax-\nus-\niad.amazon.com/x/c/JA2GipYA8Q1G8tGbWCDU8i8AAAGW7KeaMQEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICConQu5/https://www.amazon.com/stores/page/1F894943-CCD2-468A-982F-0A1219A94C69/?_encoding=UTF8&store_ref=SB_A04914591V4W1B273T0F6-A03713292LNNK77D94UBG&pd_rd_plhdr=t&aaxitk=4d3bd56f6f39210e6b5523b8f01e1091&hsa_cr_id=0&lp_asins=B07CYLP8D1%2CB08WHCYH8Q%2CB0CTBJCR95&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad5&ref_=sbx_be_dp_arbies_mblsd_mb4_logo&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n[](https://aax-us-\niad.amazon.com/x/c/JA2GipYA8Q1G8tGbWCDU8i8AAAGW7KeaMQEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICConQu5/https://www.amazon.com/stores/page/1F894943-CCD2-468A-982F-0A1219A94C69/?_encoding=UTF8&store_ref=SB_A04914591V4W1B273T0F6-A03713292LNNK77D94UBG&pd_rd_plhdr=t&aaxitk=4d3bd56f6f39210e6b5523b8f01e1091&hsa_cr_id=0&lp_asins=B07CYLP8D1%2CB08WHCYH8Q%2CB0CTBJCR95&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad5&ref_=sbx_be_dp_arbies_mblsd_mb4_bkgd&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)[View\nAll Personalized Candy Party FavorsView All Personalized Candy Party\nFavors](https://aax-us-\niad.amazon.com/x/c/JA2GipYA8Q1G8tGbWCDU8i8AAAGW7KeaMQEAAAH2AQBvbm9fdHhuX2JpZDUgICBvbm9fdHhuX2ltcDEgICConQu5/https://www.amazon.com/stores/page/1F894943-CCD2-468A-982F-0A1219A94C69/?_encoding=UTF8&store_ref=SB_A04914591V4W1B273T0F6-A03713292LNNK77D94UBG&pd_rd_plhdr=t&aaxitk=4d3bd56f6f39210e6b5523b8f01e1091&hsa_cr_id=0&lp_asins=B07CYLP8D1%2CB08WHCYH8Q%2CB0CTBJCR95&lp_slot=desktop-\narbies&lp_page_asin=B0CKY1LY33&pd_rd_i=ad5&ref_=sbx_be_dp_arbies_mblsd_mb4_hl&pd_rd_w=9oC0N&content-\nid=amzn1.sym.bf2270ca-1a07-451b-902b-520f48296153&pf_rd_p=bf2270ca-1a07-451b-902b-520f48296153&pf_rd_r=K66DDHHJD6523D8R92SE&pd_rd_wg=kKVxF&pd_rd_r=d8450f24-4c85-4597-a18c-9abea42060e2)\n\n* * *\n## Customer reviews\n\n_3.9 out of 5 stars_\n\n3.9 out of 5\n\n18 global ratings\n\n  * [ 5 star4 star3 star2 star1 star5 star 54%23%0%8%15%54%](https://www.amazon.com/product-reviews/B0CKY1LY33/ref=acr_dp_hist_5?ie=UTF8&filterByStar=five_star&reviewerType=all_reviews#reviews-filter-bar)\n  * [ 5 star4 star3 star2 star1 star4 star 54%23%0%8%15%23%](https://www.amazon.com/product-reviews/B0CKY1LY33/ref=acr_dp_hist_4?ie=UTF8&filterByStar=four_star&reviewerType=all_reviews#reviews-filter-bar)\n  * 5 star4 star3 star2 star1 star3 star\n54%23%0%8%15%0%\n\n  * [ 5 star4 star3 star2 star1 star2 star 54%23%0%8%15%8%](https://www.amazon.com/product-reviews/B0CKY1LY33/ref=acr_dp_hist_2?ie=UTF8&filterByStar=two_star&reviewerType=all_reviews#reviews-filter-bar)\n  * [ 5 star4 star3 star2 star1 star1 star 54%23%0%8%15%15%](https://www.amazon.com/product-reviews/B0CKY1LY33/ref=acr_dp_hist_1?ie=UTF8&filterByStar=one_star&reviewerType=all_reviews#reviews-filter-bar)\n\n[How customer reviews and ratings work](javascript:void\\(0\\))\n\nCustomer Reviews, including Product Star Ratings help customers to learn more\nabout the product and decide whether it is the right product for them.\n\nTo calculate the overall star rating and percentage breakdown by star, we don’t\nuse a simple average. Instead, our system considers things like how recent a\nreview is and if the reviewer bought the item on Amazon. It also analyzed\nreviews to verify trustworthiness.\n\n[Learn more how customers reviews work on\nAmazon](https://www.amazon.com/gp/help/customer/display.html/ref=cm_cr_dp_d_omni_lm_btn?nodeId=G8UYX7LALQC8V9KA)\n\n* * *\n### Review this product\n\nShare your thoughts with other customers\n\n[Write a customer review](https://www.amazon.com/review/create-\nreview/ref=cm_cr_dp_d_wr_but_top?ie=UTF8&channel=glance-detail&asin=B0CKY1LY33)\n\n* * *\nSponsored\n\n[ View Image Gallery  ](javascript:toggleSeeAllRankingView\\(\\))\n\n![Customer image](https://images-na.ssl-images-\namazon.com/images/G/01/x-locale/common/transparent-pixel._V192234675_.gif)\n\n[![](https://images-na.ssl-images-amazon.com/images/G/01/x-locale/common/grey-\npixel.gif) Amazon Customer](javascript:void\\(0\\))\n\n_5.0 out of 5 stars_  \n\n######  Images in this review\n\n### Reviews with images\n\n[See all photos](javascript:void\\(0\\))\n\n[_Previous page_](https://www.amazon.com/dp/B0CKY1LY33)\n\n  1. ![Customer Image, click to open customer review](https://m.media-amazon.com/images/I/71BhJUa9ihL._AC_UC154,154_CACC,154,154_QL85_.jpg?aicid=community-reviews)\n  2. ![Customer Image, click to open customer review](https://m.media-amazon.com/images/I/71gfjtydv6L._AC_UC154,154_CACC,154,154_QL85_.jpg?aicid=community-reviews)\n  3. ![Customer Image, click to open customer review](https://m.media-amazon.com/images/I/61pHr7sJN9L._AC_UC154,154_CACC,154,154_QL85_.jpg?aicid=community-reviews)\n  4. ![Customer Image, click to open customer review](https://m.media-amazon.com/images/I/61WoXs98pdL._AC_UC154,154_CACC,154,154_QL85_.jpg?aicid=community-reviews)\n  5. ![Customer Image, click to open customer review](https://m.media-amazon.com/images/I/51PE8qiUZ-L._AC_UC154,154_CACC,154,154_QL85_.jpg?aicid=community-reviews)\n\n[_Next page_](https://www.amazon.com/dp/B0CKY1LY33)\n\n* * *\n![](https://m.media-amazon.com/images/S/sash//CMYhpeaIeR9vguf.svg)All photos\n\n![](https://m.media-amazon.com/images/S/sash//23pID5Mp1WTA-31.svg)\n\n![Not allergy-friendly](https://images-na.ssl-images-\namazon.com/images/G/01/x-locale/common/transparent-pixel._V192234675_.gif)\n\n![](https://m.media-amazon.com/images/S/sash//7D8iRtQ0DrKAF4O.svg)\n\n[![](https://images-na.ssl-images-amazon.com/images/G/01/x-locale/common/grey-\npixel.gif) Amazon Customer](javascript:void\\(0\\))\n\n_2 out of 5 stars_\n\n##### Not allergy-friendly\n\nThe chocolates I recieved may technically be Parve but they're not milk-free.\nThey have a warning not present on the website listing for; soy, milk, peanuts,\nalmond, hazelnut, pecan, coconut, walnut, pistachio and cashew. It's hard enough\nenough to find dairy-free stuff without having omitted information.\n\n[More](javascript:void\\(0\\))[Hide](javascript:void\\(0\\))\n\n  * ![Customer Image](https://m.media-amazon.com/images/I/71BhJUa9ihL.jpg)\n\nThank you for your feedback\n\nClose\n\nSorry, there was an error\n\nClose\n\nSorry we couldn't load the review\n\nTry again\n\n###  Top reviews from the United States\n\n#### There was a problem filtering reviews. Please reload the page.\n\n  * [![](https://images-na.ssl-images-amazon.com/images/G/01/x-locale/common/grey-pixel.gif) Justin Jones](https://www.amazon.com/gp/profile/amzn1.account.AFNBCAWKPFSMGOC776ZASJL5CSGA/ref=cm_cr_dp_d_gw_tr?ie=UTF8)\n##### [_5.0 out of 5 stars_ Dairy free delight!\n](https://www.amazon.com/gp/customer-\nreviews/R2KU3XTMCSYWM7/ref=cm_cr_dp_d_rvw_ttl?ie=UTF8&ASIN=B0CKY1LY33)\n\nReviewed in the United States on October 29, 2024\n\n[Verified\nPurchase](https://www.amazon.com/gp/help/customer/display.html/ref=cm_cr_dp_d_rvw_avp?nodeId=G8UYX7LALQC8V9KA)\n\nA great replacement for dairy traditional coins by flavor and difficulty of\nopening both!  \n\n[Read more](javascript:void\\(0\\))\n\nOne person found this helpful\n\n[ Helpful\n](https://www.amazon.com/ap/signin?openid.return_to=https%3A%2F%2Fwww.amazon.com%2Fdp%2FB0CKY1LY33%2Fref%3Dcm_cr_dp_d_vote_lft%3Fie%3DUTF8%26csrfT%3DhENPzy3r%252F6WOZp%252F2ZAydaJzHYKYycJJaGk%252F%252FZB4ieKSKAAAAAGgsMxgAAAAB%26reviewId%3DR2KU3XTMCSYWM7%23R2KU3XTMCSYWM7&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.assoc_handle=usflex&openid.mode=checkid_setup&marketPlaceId=ATVPDKIKX0DER&language=en&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0)\n\n[Report](https://www.amazon.com/hz/reviews-render/report-\nreview?ie=UTF8&ref=cm_cr_dp_d_report&csrfT=hENPzy3r%2F6WOZp%2F2ZAydaJzHYKYycJJaGk%2F%2FZB4ieKSKAAAAAGgsMxgAAAAB&reviewId=R2KU3XTMCSYWM7)\n\n  * [![](https://images-na.ssl-images-amazon.com/images/G/01/x-locale/common/grey-pixel.gif) E. Cahill](https://www.amazon.com/gp/profile/amzn1.account.AF6J4ZFBV7ENAXYT5XB6T476U5XA/ref=cm_cr_dp_d_gw_tr?ie=UTF8)\n##### [_1.0 out of 5 stars_ WARNING: NOT DAIRY FREE AS ADVERTISED\n](https://www.amazon.com/gp/customer-\nreviews/RAAUC8V9D5XTX/ref=cm_cr_dp_d_rvw_ttl?ie=UTF8&ASIN=B0CKY1LY33)\n\nReviewed in the United States on January 1, 2025\n\n[Verified\nPurchase](https://www.amazon.com/gp/help/customer/display.html/ref=cm_cr_dp_d_rvw_avp?nodeId=G8UYX7LALQC8V9KA)\n\nI specifically ordered these because they are advertised as dairy free. Received\nthem and the label says “may contain milk.” How is that dairy free? Too late to\nget a replacement for a class party so there goes our dreidel game.  \n\n[Read more](javascript:void\\(0\\))\n\n![Customer image](https://images-na.ssl-images-\namazon.com/images/G/01/x-locale/common/transparent-pixel._V192234675_.gif)\n\n[![](https://m.media-amazon.com/images/S/amazon-avatars-global/default.png) E.\nCahill](https://www.amazon.com/gp/profile/amzn1.account.AF6J4ZFBV7ENAXYT5XB6T476U5XA/ref=cm_cr_dp_d_gw_pop?ie=UTF8)\n\n_1.0 out of 5 stars_\n\n#####  WARNING: NOT DAIRY FREE AS ADVERTISED\n\n  \nReviewed in the United States on January 1, 2025\n\nI specifically ordered these because they are advertised as dairy free. Received\nthem and the label says “may contain milk.” How is that dairy free? Too late to\nget a replacement for a class party so there goes our dreidel game.  \n\n######  Images in this review\n\n![Customer image 1](https://m.media-amazon.com/images/I/71gfjtydv6L._SY88.jpg)\n\n[![Customer image](https://images-na.ssl-images-\namazon.com/images/G/01/x-locale/common/grey-pixel.gif)](javascript:void\\(0\\))\n\n[ Helpful\n](https://www.amazon.com/ap/signin?openid.return_to=https%3A%2F%2Fwww.amazon.com%2Fdp%2FB0CKY1LY33%2Fref%3Dcm_cr_dp_d_vote_lft%3Fie%3DUTF8%26csrfT%3DhCJNg8sXyemVFk9XFRNoE4sTEIjtVhQYkERCARKjxVfgAAAAAGgsMxgAAAAB%26reviewId%3DRAAUC8V9D5XTX%23RAAUC8V9D5XTX&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.assoc_handle=usflex&openid.mode=checkid_setup&marketPlaceId=ATVPDKIKX0DER&language=en&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0)\n\n[Report](https://www.amazon.com/hz/reviews-render/report-\nreview?ie=UTF8&ref=cm_cr_dp_d_report&csrfT=hCJNg8sXyemVFk9XFRNoE4sTEIjtVhQYkERCARKjxVfgAAAAAGgsMxgAAAAB&reviewId=RAAUC8V9D5XTX)\n\n  * [![](https://images-na.ssl-images-amazon.com/images/G/01/x-locale/common/grey-pixel.gif) Rosie](https://www.amazon.com/gp/profile/amzn1.account.****************************/ref=cm_cr_dp_d_gw_tr?ie=UTF8)\n##### [_4.0 out of 5 stars_ My son is a fan\n](https://www.amazon.com/gp/customer-\nreviews/R2V8OG3Q3IODY6/ref=cm_cr_dp_d_rvw_ttl?ie=UTF8&ASIN=B0CKY1LY33)\n\nReviewed in the United States on December 10, 2024\n\n[Verified\nPurchase](https://www.amazon.com/gp/help/customer/display.html/ref=cm_cr_dp_d_rvw_avp?nodeId=G8UYX7LALQC8V9KA)\n\nMy 9-year-old really enjoyed these. He needed Dairy free chocolate. Appreciate\nthat there are coins out there he can enjoy!  \n\n[Read more](javascript:void\\(0\\))\n\n[ Helpful\n](https://www.amazon.com/ap/signin?openid.return_to=https%3A%2F%2Fwww.amazon.com%2Fdp%2FB0CKY1LY33%2Fref%3Dcm_cr_dp_d_vote_lft%3Fie%3DUTF8%26csrfT%3DhF4xWCXn2WAeqCDK7P%252BW0lrar8XcEX7fxdJqsbCXDh0bAAAAAGgsMxgAAAAB%26reviewId%3DR2V8OG3Q3IODY6%23R2V8OG3Q3IODY6&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.assoc_handle=usflex&openid.mode=checkid_setup&marketPlaceId=ATVPDKIKX0DER&language=en&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0)\n\n[Report](https://www.amazon.com/hz/reviews-render/report-\nreview?ie=UTF8&ref=cm_cr_dp_d_report&csrfT=hF4xWCXn2WAeqCDK7P%2BW0lrar8XcEX7fxdJqsbCXDh0bAAAAAGgsMxgAAAAB&reviewId=R2V8OG3Q3IODY6)\n\n  * [![](https://images-na.ssl-images-amazon.com/images/G/01/x-locale/common/grey-pixel.gif) J.](https://www.amazon.com/gp/profile/amzn1.account.AEE6CJYETQW5PMFKRY6HRZJRBHIA/ref=cm_cr_dp_d_gw_tr?ie=UTF8)\n##### [_2.0 out of 5 stars_ Not allergy-friendly\n](https://www.amazon.com/gp/customer-\nreviews/R15G12AVK5AM1K/ref=cm_cr_dp_d_rvw_ttl?ie=UTF8&ASIN=B0CKY1LY33)\n\nReviewed in the United States on December 14, 2024\n\n[Verified\nPurchase](https://www.amazon.com/gp/help/customer/display.html/ref=cm_cr_dp_d_rvw_avp?nodeId=G8UYX7LALQC8V9KA)\n\nThe chocolates I recieved may technically be Parve but they're not milk-free.\nThey have a warning not present on the website listing for; soy, milk, peanuts,\nalmond, hazelnut, pecan, coconut, walnut, pistachio and cashew.  \n  \nIt's hard enough enough to find dairy-free stuff without having omitted\ninformation.  \n\n[Read more](javascript:void\\(0\\))\n\n![Customer image](https://images-na.ssl-images-\namazon.com/images/G/01/x-locale/common/transparent-pixel._V192234675_.gif)\n\n[![](https://m.media-amazon.com/images/S/amazon-avatars-\nglobal/d7234091-2630-4405-8bf5-53ea54068fb3._CR34%2C0%2C335%2C335_UX460_.jpg)\nJ.](https://www.amazon.com/gp/profile/amzn1.account.AEE6CJYETQW5PMFKRY6HRZJRBHIA/ref=cm_cr_dp_d_gw_pop?ie=UTF8)\n\n_2.0 out of 5 stars_\n\n#####  Not allergy-friendly\n\n  \nReviewed in the United States on December 14, 2024\n\nThe chocolates I recieved may technically be Parve but they're not milk-free.\nThey have a warning not present on the website listing for; soy, milk, peanuts,\nalmond, hazelnut, pecan, coconut, walnut, pistachio and cashew.  \n  \nIt's hard enough enough to find dairy-free stuff without having omitted\ninformation.  \n\n######  Images in this review\n\n![Customer image 1](https://m.media-amazon.com/images/I/71BhJUa9ihL._SY88.jpg)\n\n[![Customer image](https://images-na.ssl-images-\namazon.com/images/G/01/x-locale/common/grey-pixel.gif)](javascript:void\\(0\\))\n\n[ Helpful\n](https://www.amazon.com/ap/signin?openid.return_to=https%3A%2F%2Fwww.amazon.com%2Fdp%2FB0CKY1LY33%2Fref%3Dcm_cr_dp_d_vote_lft%3Fie%3DUTF8%26csrfT%3DhCT6cvzU2bFaImMw9Ryeu%252F65AmhWKAI%252Ba3CJ9U%252Fn4jksAAAAAGgsMxgAAAAB%26reviewId%3DR15G12AVK5AM1K%23R15G12AVK5AM1K&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.assoc_handle=usflex&openid.mode=checkid_setup&marketPlaceId=ATVPDKIKX0DER&language=en&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0)\n\n[Report](https://www.amazon.com/hz/reviews-render/report-\nreview?ie=UTF8&ref=cm_cr_dp_d_report&csrfT=hCT6cvzU2bFaImMw9Ryeu%2F65AmhWKAI%2Ba3CJ9U%2Fn4jksAAAAAGgsMxgAAAAB&reviewId=R15G12AVK5AM1K)\n\n  * [![](https://images-na.ssl-images-amazon.com/images/G/01/x-locale/common/grey-pixel.gif) Andrew Baker](https://www.amazon.com/gp/profile/amzn1.account.AFI6MHGH46NJF5TONFKENKBNVGGA/ref=cm_cr_dp_d_gw_tr?ie=UTF8)\n##### [_1.0 out of 5 stars_ Shipped After Expiration Date\n](https://www.amazon.com/gp/customer-\nreviews/RYMHJ3X78R843/ref=cm_cr_dp_d_rvw_ttl?ie=UTF8&ASIN=B0CKY1LY33)\n\nReviewed in the United States on October 10, 2024\n\n[Verified\nPurchase](https://www.amazon.com/gp/help/customer/display.html/ref=cm_cr_dp_d_rvw_avp?nodeId=G8UYX7LALQC8V9KA)\n\nOrdered this product in October, but the expiration date on the box says\nSeptember 15. It may be a \"best by\" kind of date but that still feels like it\ngoes against policy to ship something that's already past date.  \n\n[Read more](javascript:void\\(0\\))\n\n![Customer image](https://images-na.ssl-images-\namazon.com/images/G/01/x-locale/common/transparent-pixel._V192234675_.gif)\n\n[![](https://m.media-amazon.com/images/S/amazon-avatars-global/default.png)\nAndrew\nBaker](https://www.amazon.com/gp/profile/amzn1.account.AFI6MHGH46NJF5TONFKENKBNVGGA/ref=cm_cr_dp_d_gw_pop?ie=UTF8)\n\n_1.0 out of 5 stars_\n\n#####  Shipped After Expiration Date\n\n  \nReviewed in the United States on October 10, 2024\n\nOrdered this product in October, but the expiration date on the box says\nSeptember 15. It may be a \"best by\" kind of date but that still feels like it\ngoes against policy to ship something that's already past date.  \n\n######  Images in this review\n\n![Customer image 1](https://m.media-amazon.com/images/I/61pHr7sJN9L._SY88.jpg)\n\n[![Customer image](https://images-na.ssl-images-\namazon.com/images/G/01/x-locale/common/grey-pixel.gif)](javascript:void\\(0\\))\n\n[ Helpful\n](https://www.amazon.com/ap/signin?openid.return_to=https%3A%2F%2Fwww.amazon.com%2Fdp%2FB0CKY1LY33%2Fref%3Dcm_cr_dp_d_vote_lft%3Fie%3DUTF8%26csrfT%3DhJD4yX9oBWU%252B%252F6bb8FwjSYVCBAGu%252FCqqmHRqPk9Rj7RlAAAAAGgsMxgAAAAB%26reviewId%3DRYMHJ3X78R843%23RYMHJ3X78R843&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.assoc_handle=usflex&openid.mode=checkid_setup&marketPlaceId=ATVPDKIKX0DER&language=en&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0)\n\n[Report](https://www.amazon.com/hz/reviews-render/report-\nreview?ie=UTF8&ref=cm_cr_dp_d_report&csrfT=hJD4yX9oBWU%2B%2F6bb8FwjSYVCBAGu%2FCqqmHRqPk9Rj7RlAAAAAGgsMxgAAAAB&reviewId=RYMHJ3X78R843)\n\n  * [![](https://images-na.ssl-images-amazon.com/images/G/01/x-locale/common/grey-pixel.gif) Shari Faber](https://www.amazon.com/gp/profile/amzn1.account.AGWLOZCFSAXYT5AVW2DPOWMOGZPQ/ref=cm_cr_dp_d_gw_tr?ie=UTF8)\n##### [_1.0 out of 5 stars_ Do not purchase.\n](https://www.amazon.com/gp/customer-\nreviews/R1EK27RXF7DD6G/ref=cm_cr_dp_d_rvw_ttl?ie=UTF8&ASIN=B0CKY1LY33)\n\nReviewed in the United States on December 2, 2023\n\n[Verified\nPurchase](https://www.amazon.com/gp/help/customer/display.html/ref=cm_cr_dp_d_rvw_avp?nodeId=G8UYX7LALQC8V9KA)\n\nThe box came damaged in several places which left the chocolate exposed and now\nneeds to be thrown out since it can’t be returned.  \n\n[Read more](javascript:void\\(0\\))\n\n![Customer image](https://images-na.ssl-images-\namazon.com/images/G/01/x-locale/common/transparent-pixel._V192234675_.gif)\n\n[![](https://m.media-amazon.com/images/S/amazon-avatars-global/default.png)\nShari\nFaber](https://www.amazon.com/gp/profile/amzn1.account.AGWLOZCFSAXYT5AVW2DPOWMOGZPQ/ref=cm_cr_dp_d_gw_pop?ie=UTF8)\n\n_1.0 out of 5 stars_\n\n#####  Do not purchase.\n\n  \nReviewed in the United States on December 2, 2023\n\nThe box came damaged in several places which left the chocolate exposed and now\nneeds to be thrown out since it can’t be returned.  \n\n######  Images in this review\n\n![Customer image 1](https://m.media-amazon.com/images/I/61WoXs98pdL._SY88.jpg)\n![Customer image 2](https://m.media-amazon.com/images/I/51PE8qiUZ-L._SY88.jpg)\n\n[![Customer image](https://images-na.ssl-images-\namazon.com/images/G/01/x-locale/common/grey-\npixel.gif)](javascript:void\\(0\\))[![Customer image](https://images-na.ssl-\nimages-amazon.com/images/G/01/x-locale/common/grey-\npixel.gif)](javascript:void\\(0\\))\n\n2 people found this helpful\n\n[ Helpful\n](https://www.amazon.com/ap/signin?openid.return_to=https%3A%2F%2Fwww.amazon.com%2Fdp%2FB0CKY1LY33%2Fref%3Dcm_cr_dp_d_vote_lft%3Fie%3DUTF8%26csrfT%3DhC06ndm5l09R4AI4c%252Bjh%252FSopt2AyBmAagQCNiHX7EyaNAAAAAGgsMxgAAAAB%26reviewId%3DR1EK27RXF7DD6G%23R1EK27RXF7DD6G&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.assoc_handle=usflex&openid.mode=checkid_setup&marketPlaceId=ATVPDKIKX0DER&language=en&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0)\n\n[Report](https://www.amazon.com/hz/reviews-render/report-\nreview?ie=UTF8&ref=cm_cr_dp_d_report&csrfT=hC06ndm5l09R4AI4c%2Bjh%2FSopt2AyBmAagQCNiHX7EyaNAAAAAGgsMxgAAAAB&reviewId=R1EK27RXF7DD6G)\n\n* * *\n[See more reviews](https://www.amazon.com/product-\nreviews/B0CKY1LY33/ref=cm_cr_dp_d_show_all_btm?ie=UTF8&reviewerType=all_reviews)\n\nSponsored\n\n* * *\n**Disclaimer** : While we work to ensure that product information is correct, on\noccasion manufacturers may alter their ingredient lists. Actual product\npackaging and materials may contain more and/or different information than that\nshown on our Web site. We recommend that you do not solely rely on the\ninformation presented and that you always read labels, warnings, and directions\nbefore using or consuming a product. For additional information about a product,\nplease contact the manufacturer. Content on this site is for reference purposes\nand is not intended to substitute for advice given by a physician, pharmacist,\nor other licensed health-care professional. You should not use this information\nas self-diagnosis or for treating a health problem or disease. Contact your\nhealth-care provider immediately if you suspect that you have a medical problem.\nInformation and statements regarding dietary supplements have not been evaluated\nby the Food and Drug Administration and are not intended to diagnose, treat,\ncure, or prevent any disease or health condition. Amazon.com assumes no\nliability for inaccuracies or misstatements about products.\n\n[ Top ](https://www.amazon.com/dp/B0CKY1LY33) [ About this item\n](https://www.amazon.com/dp/B0CKY1LY33) [ Similar\n](https://www.amazon.com/dp/B0CKY1LY33) [ Product information\n](https://www.amazon.com/dp/B0CKY1LY33) [ Questions\n](https://www.amazon.com/dp/B0CKY1LY33) [ Reviews\n](https://www.amazon.com/dp/B0CKY1LY33)\n\n  \n\n![](https://m.media-\namazon.com/images/G/01/personalization/ybh/loading-4x-gray._CB485916920_.gif)  \n---  \nYour recently viewed items and featured recommendations\n\n›\n\n[ View or edit your browsing history ](https://www.amazon.com/gp/history)\n\nAfter viewing product detail pages, look here to find an easy way to navigate\nback to pages you are interested in.\n\n  \n\nBack to top\n\nGet to Know Us\n\n  * [Careers](https://www.amazon.jobs)\n  * [Blog](https://blog.aboutamazon.com/?utm_source=gateway&utm_medium=footer)\n  * [About Amazon](https://www.aboutamazon.com/?utm_source=gateway&utm_medium=footer)\n  * [Investor Relations](https://www.amazon.com/ir)\n  * [Amazon Devices](https://www.amazon.com/gp/browse.html?node=2102313011&ref_=footer_devices)\n  * [Amazon Science](https://www.amazon.science)\n\nMake Money with Us\n\n  * [Sell products on Amazon](https://services.amazon.com/sell.html?ld=AZFSSOA&ref_=footer_soa)\n  * [Sell on Amazon Business](https://services.amazon.com/amazon-business.html?ld=usb2bunifooter&ref_=footer_b2b)\n  * [Sell apps on Amazon](https://developer.amazon.com)\n  * [Become an Affiliate](https://affiliate-program.amazon.com/)\n  * [Advertise Your Products](https://advertising.amazon.com/?ref=ext_amzn_ftr)\n  * [Self-Publish with Us](https://www.amazon.com/gp/seller-account/mm-summary-page.html?ld=AZFooterSelfPublish&topic=*********&ref_=footer_publishing)\n  * [Host an Amazon Hub](https://go.thehub-amazon.com/amazon-hub-locker)\n  * ›[See More Make Money with Us](https://www.amazon.com/b/?node=***********&ld=AZUSSOA-seemore&ref_=footer_seemore)\n\nAmazon Payment Products\n\n  * [Amazon Business Card](https://www.amazon.com/dp/B07984JN3L?plattr=ACOMFO&ie=UTF-8)\n  * [Shop with Points](https://www.amazon.com/gp/browse.html?node=***********&ref_=footer_swp)\n  * [Reload Your Balance](https://www.amazon.com/dp/B0CHTVMXZJ?th=1?ref_=footer_reload_us)\n  * [Amazon Currency Converter](https://www.amazon.com/gp/browse.html?node=*********&ref_=footer_tfx)\n\nLet Us Help You\n\n  * [Amazon and COVID-19](https://www.amazon.com/gp/help/customer/display.html?nodeId=GDFU3JS5AL6SYHRD&ref_=footer_covid)\n  * [Your Account](https://www.amazon.com/gp/css/homepage.html?ref_=footer_ya)\n  * [Your Orders](https://www.amazon.com/gp/css/order-history?ref_=footer_yo)\n  * [Shipping Rates & Policies](https://www.amazon.com/gp/help/customer/display.html?nodeId=468520&ref_=footer_shiprates)\n  * [Returns & Replacements](https://www.amazon.com/gp/css/returns/homepage.html?ref_=footer_hy_f_4)\n  * [Manage Your Content and Devices](https://www.amazon.com/gp/digital/fiona/manage?ref_=footer_myk)\n  * [Help](https://www.amazon.com/gp/help/customer/display.html?nodeId=508510&ref_=footer_gw_m_b_he)\n\n[ ](https://www.amazon.com/?ref_=footer_logo)\n\n[ English](https://www.amazon.com/customer-\npreferences/edit?ie=UTF8&preferencesReturnUrl=%2Fdp%2FB0CKY1LY33&ref_=footer_lang)\n[ $USD - U.S. Dollar ](https://www.amazon.com/customer-\npreferences/edit?ie=UTF8&ref_=footer_cop&preferencesReturnUrl=%2Fdp%2FB0CKY1LY33dp%2FB0CKY1LY33)\n[ United States ](https://www.amazon.com/customer-\npreferences/country?ie=UTF8&preferencesReturnUrl=%2Fdp%2FB0CKY1LY33&ref_=footer_icp_cp)\n\n  * ##### [Amazon Music Stream millions  \nof songs](https://music.amazon.com?ref=dm_aff_amz_com)\n\n  * ##### [Amazon Ads Reach customers  \nwherever they  \nspend their time](https://advertising.amazon.com/?ref=footer_advtsing_amzn_com)\n\n  * ##### [6pm Score deals  \non fashion brands](https://www.6pm.com)\n\n  * ##### [AbeBooks Books, art  \n& collectibles](https://www.abebooks.com)\n\n  * ##### [ACX  Audiobook Publishing  \nMade Easy](https://www.acx.com/)\n\n  * ##### [Sell on Amazon Start a Selling Account](https://sell.amazon.com/?ld=AZUSSOA-footer-aff&ref_=footer_sell)\n  * ##### [Veeqo Shipping Software  \nInventory\nManagement](https://www.veeqo.com/?utm_source=amazon&utm_medium=website&utm_campaign=footer)\n\n  * ##### [Amazon Business Everything For  \nYour Business](https://www.amazon.com/business?ref_=footer_retail_b2b)\n\n  * ##### [AmazonGlobal Ship Orders  \nInternationally](https://www.amazon.com/gp/browse.html?node=*********&ref_=footer_amazonglobal)\n\n  * ##### [Amazon Web Services Scalable Cloud  \nComputing Services](https://aws.amazon.com/what-is-cloud-\ncomputing/?sc_channel=EL&sc_campaign=amazonfooter)\n\n  * ##### [Audible Listen to Books & Original  \nAudio Performances](https://www.audible.com)\n\n  * ##### [Box Office Mojo Find Movie  \nBox Office Data](https://www.boxofficemojo.com/?ref_=amzn_nav_ftr)\n\n  * ##### [Goodreads Book reviews  \n& recommendations](https://www.goodreads.com)\n\n  * ##### [IMDb Movies, TV  \n& Celebrities](https://www.imdb.com)\n\n  * ##### [IMDbPro Get Info Entertainment  \nProfessionals Need](https://pro.imdb.com?ref_=amzn_nav_ftr)\n\n  * ##### [Kindle Direct Publishing Indie Digital & Print Publishing  \nMade Easy ](https://kdp.amazon.com)\n\n  * ##### [Prime Video Direct Video Distribution  \nMade Easy](https://videodirect.amazon.com/home/<USER>//www.shopbop.com)\n\n  * ##### [Woot! Deals and   \nShenanigans](https://www.woot.com/)\n\n  * ##### [Zappos Shoes &  \nClothing](https://www.zappos.com)\n\n  * ##### [Ring Smart Home  \nSecurity Systems ](https://ring.com)\n\n  * ##### [eero WiFi Stream 4K Video  \nin Every Room](https://eero.com/)\n\n  * ##### [Blink Smart Security  \nfor Every Home ](https://blinkforhome.com/?ref=nav_footer)\n\n  * ##### [Neighbors App  Real-Time Crime  \n& Safety Alerts ](https://shop.ring.com/pages/neighbors-app)\n\n  * ##### [Amazon Subscription Boxes Top subscription boxes – right to your door](https://www.amazon.com/gp/browse.html?node=14498690011&ref_=amzn_nav_ftr_swa)\n  * ##### [PillPack Pharmacy Simplified](https://www.pillpack.com)\n\n  * [Conditions of Use](https://www.amazon.com/gp/help/customer/display.html?nodeId=508088&ref_=footer_cou)\n  * [Privacy Notice](https://www.amazon.com/gp/help/customer/display.html?nodeId=468496&ref_=footer_privacy)\n  * [Consumer Health Data Privacy Disclosure](https://www.amazon.com/gp/help/customer/display.html?ie=UTF8&nodeId=TnACMrGVghHocjL8KB&ref_=footer_consumer_health_data_privacy)\n  * [Your Ads Privacy Choices](https://www.amazon.com/privacyprefs?ref_=footer_iba)\n\n© 1996-2025, Amazon.com, Inc. or its affiliates\n\n\n\nEnriched data:\n$ENRICHED_DATA\ncode: 10976944\nurl: http://world-en.openfoodfacts.org/product/10976944/chocolate-coins\ncreator: kiliweb\ncreated_t: **********\ncreated_datetime: 2022-08-23T10:02:50Z\nlast_modified_t: **********\nlast_modified_datetime: 2024-03-09T20:04:12Z\nlast_modified_by: annelotte\nlast_updated_t: **********.0\nlast_updated_datetime: 2024-12-17T18:56:07Z\nproduct_name: Chocolate coins\nquantity: 150 g\npackaging: en:mixed plastic film-net, en:mixed plastic film-net\npackaging_tags: en:mixed-plastic-film-net\npackaging_en: Mixed-plastic-film-net\ncategories: Snacks,Sweet snacks,Cocoa and its products,Confectioneries,Chocolate candies\ncategories_tags: en:snacks,en:sweet-snacks,en:cocoa-and-its-products,en:confectioneries,en:chocolate-candies\ncategories_en: Snacks,Sweet snacks,Cocoa and its products,Confectioneries,Chocolate candies\ncountries: United Kingdom\ncountries_tags: en:united-kingdom\ncountries_en: United Kingdom\ningredients_text: Sugar, Cocoa Butter, Whole Milk Powder, Whey (Milk) Powder, Lactose (Milk), Emulsifier. Soya Lecithin; Vanilla Flavour. Allergy Advice: For allergens, see ingredients in bold. May also contain traces of cereals containing gluten and nuts.\ningredients_tags: en:sugar,en:added-sugar,en:disaccharide,en:cocoa-butter,en:plant,en:cocoa,en:whole-milk-powder,en:dairy,en:milk-powder,en:whey,en:powder,en:lactose,en:emulsifier,en:soya-lecithin,en:e322,en:e322i,en:vanilla-flavouring,en:flavouring\ningredients_analysis_tags: en:palm-oil-free,en:non-vegan,en:vegetarian-status-unknown\nallergens: en:milk\ntraces_tags: en:gluten,en:nuts\ntraces_en: Gluten,Nuts\nadditives_n: 1.0\nadditives_tags: en:e322,en:e322i\nadditives_en: E322 - Lecithins,E322i - Lecithin\nnutriscore_score: 32.0\nnutriscore_grade: e\nnova_group: 4.0\npnns_groups_1: Sugary snacks\npnns_groups_2: Sweets\nfood_groups: en:sweets\nfood_groups_tags: en:sugary-snacks,en:sweets\nfood_groups_en: Sugary snacks,Sweets\nstates: en:to-be-completed, en:nutrition-facts-completed, en:ingredients-completed, en:expiration-date-completed, en:packaging-code-to-be-completed, en:characteristics-to-be-completed, en:origins-to-be-completed, en:categories-completed, en:brands-to-be-completed, en:packaging-completed, en:quantity-completed, en:product-name-completed, en:photos-to-be-validated, en:packaging-photo-to-be-selected, en:nutrition-photo-selected, en:ingredients-photo-selected, en:front-photo-selected, en:photos-uploaded\nstates_tags: en:to-be-completed,en:nutrition-facts-completed,en:ingredients-completed,en:expiration-date-completed,en:packaging-code-to-be-completed,en:characteristics-to-be-completed,en:origins-to-be-completed,en:categories-completed,en:brands-to-be-completed,en:packaging-completed,en:quantity-completed,en:product-name-completed,en:photos-to-be-validated,en:packaging-photo-to-be-selected,en:nutrition-photo-selected,en:ingredients-photo-selected,en:front-photo-selected,en:photos-uploaded\nstates_en: To be completed,Nutrition facts completed,Ingredients completed,Expiration date completed,Packaging code to be completed,Characteristics to be completed,Origins to be completed,Categories completed,Brands to be completed,Packaging completed,Quantity completed,Product name completed,Photos to be validated,Packaging photo to be selected,Nutrition photo selected,Ingredients photo selected,Front photo selected,Photos uploaded\nnutrient_levels_tags: en:fat-in-high-quantity,en:saturated-fat-in-high-quantity,en:sugars-in-high-quantity,en:salt-in-low-quantity\nproduct_quantity: 150.0\ncompleteness: 0.7875\nlast_image_t: 1661248973.0\nlast_image_datetime: 2022-08-23T10:02:53Z\nmain_category: en:chocolate-candies\nmain_category_en: Chocolate candies\nimage_url: https://images.openfoodfacts.org/images/products/000/001/097/6944/front_en.3.400.jpg\nimage_small_url: https://images.openfoodfacts.org/images/products/000/001/097/6944/front_en.3.200.jpg\nimage_ingredients_url: https://images.openfoodfacts.org/images/products/000/001/097/6944/ingredients_en.10.400.jpg\nimage_ingredients_small_url: https://images.openfoodfacts.org/images/products/000/001/097/6944/ingredients_en.10.200.jpg\nimage_nutrition_url: https://images.openfoodfacts.org/images/products/000/001/097/6944/nutrition_en.5.400.jpg\nimage_nutrition_small_url: https://images.openfoodfacts.org/images/products/000/001/097/6944/nutrition_en.5.200.jpg\nenergy-kcal_100g: 527.0\nenergy_100g: 2205.0\nfat_100g: 27.0\nsaturated-fat_100g: 16.0\ncarbohydrates_100g: 66.0\nsugars_100g: 66.0\nfiber_100g: 0.0\nproteins_100g: 4.4\nsalt_100g: 0.26\nsodium_100g: 0.104\nfruits-vegetables-nuts-estimate-from-ingredients_100g: 0.0\nnutrition-score-fr_100g: 32.0\nbm25_score: 91.76394978788947\ncode: 876416167084\nurl: http://world-en.openfoodfacts.org/product/0876416167084/chocolate-coins-momentum-brands\ncreator: usda-ndb-import\ncreated_t: 1489075059\ncreated_datetime: 2017-03-09T15:57:39Z\nlast_modified_t: 1489075059\nlast_modified_datetime: 2017-03-09T15:57:39Z\nlast_modified_by: usda-ndb-import\nlast_updated_t: 1707520695.0\nlast_updated_datetime: 2024-02-09T23:18:15Z\nproduct_name: Chocolate Coins\nbrands: Momentum Brands\nbrands_tags: momentum-brands\nbrands_en: Momentum-brands\ncountries: United States\ncountries_tags: en:united-states\ncountries_en: United States\ningredients_text: Sugar, cocoa butter, milk, chocolate liquor, soy lecithin, polyglycerol polyicinoleate (emulsifier).\ningredients_tags: en:sugar,en:added-sugar,en:disaccharide,en:cocoa-butter,en:plant,en:cocoa,en:milk,en:dairy,en:cocoa-paste,en:soya-lecithin,en:e322,en:e322i,en:polyglycerol-polyicinoleate,en:emulsifier\ningredients_analysis_tags: en:palm-oil-free,en:non-vegan,en:vegetarian-status-unknown\nserving_size: 49 g (7 PIECES)\nserving_quantity: 49.0\nadditives_n: 1.0\nadditives_tags: en:e322,en:e322i\nadditives_en: E322 - Lecithins,E322i - Lecithin\nnova_group: 4.0\nstates: en:to-be-completed, en:nutrition-facts-completed, en:ingredients-completed, en:expiration-date-to-be-completed, en:packaging-code-to-be-completed, en:characteristics-to-be-completed, en:origins-to-be-completed, en:categories-to-be-completed, en:brands-completed, en:packaging-to-be-completed, en:quantity-to-be-completed, en:product-name-completed, en:photos-to-be-uploaded\nstates_tags: en:to-be-completed,en:nutrition-facts-completed,en:ingredients-completed,en:expiration-date-to-be-completed,en:packaging-code-to-be-completed,en:characteristics-to-be-completed,en:origins-to-be-completed,en:categories-to-be-completed,en:brands-completed,en:packaging-to-be-completed,en:quantity-to-be-completed,en:product-name-completed,en:photos-to-be-uploaded\nstates_en: To be completed,Nutrition facts completed,Ingredients completed,Expiration date to be completed,Packaging code to be completed,Characteristics to be completed,Origins to be completed,Categories to be completed,Brands completed,Packaging to be completed,Quantity to be completed,Product name completed,Photos to be uploaded\ncompleteness: 0.4\nenergy-kcal_100g: 551.0\nenergy_100g: 2305.0\nfat_100g: 32.65\nsaturated-fat_100g: 20.41\ntrans-fat_100g: 0.0\ncholesterol_100g: 0.01\ncarbohydrates_100g: 57.14\nsugars_100g: 55.1\nfiber_100g: 2.0\nproteins_100g: 6.12\nsalt_100g: 0.12954\nsodium_100g: 0.051816\nvitamin-a_100g: 6.12e-05\nvitamin-c_100g: 0.0\ncalcium_100g: 0.163\niron_100g: 0.00367\nfruits-vegetables-nuts-estimate-from-ingredients_100g: 0.0\nbm25_score: 91.76394978788947\ncode: 10976876\nurl: http://world-en.openfoodfacts.org/product/10976876/chocolate-coins\ncreator: inf\ncreated_t: 1672771572\ncreated_datetime: 2023-01-03T18:46:12Z\nlast_modified_t: 1672771630\nlast_modified_datetime: 2023-01-03T18:47:10Z\nlast_modified_by: inf\nlast_updated_t: 1707874054.0\nlast_updated_datetime: 2024-02-14T01:27:34Z\nproduct_name: chocolate coins\ncountries: en:United Kingdom\ncountries_tags: en:united-kingdom\ncountries_en: United Kingdom\ningredients_text: sugar, cocoa butter, whole milk powder, cocoa mass, whey (milk) powder, skimmed milk powder, emulsifier: soya lecithin\ningredients_tags: en:sugar,en:added-sugar,en:disaccharide,en:cocoa-butter,en:plant,en:cocoa,en:whole-milk-powder,en:dairy,en:milk-powder,en:cocoa-paste,en:whey,en:powder,en:skimmed-milk-powder,en:emulsifier,en:soya-lecithin,en:e322,en:e322i\ningredients_analysis_tags: en:palm-oil-free,en:non-vegan,en:vegetarian-status-unknown\nallergens: en:milk\nadditives_n: 1.0\nadditives_tags: en:e322,en:e322i\nadditives_en: E322 - Lecithins,E322i - Lecithin\nnova_group: 4.0\nstates: en:to-be-completed, en:nutrition-facts-completed, en:ingredients-completed, en:expiration-date-to-be-completed, en:packaging-code-to-be-completed, en:characteristics-to-be-completed, en:origins-to-be-completed, en:categories-to-be-completed, en:brands-to-be-completed, en:packaging-to-be-completed, en:quantity-to-be-completed, en:product-name-completed, en:photos-to-be-validated, en:packaging-photo-to-be-selected, en:nutrition-photo-to-be-selected, en:ingredients-photo-selected, en:front-photo-to-be-selected, en:photos-uploaded\nstates_tags: en:to-be-completed,en:nutrition-facts-completed,en:ingredients-completed,en:expiration-date-to-be-completed,en:packaging-code-to-be-completed,en:characteristics-to-be-completed,en:origins-to-be-completed,en:categories-to-be-completed,en:brands-to-be-completed,en:packaging-to-be-completed,en:quantity-to-be-completed,en:product-name-completed,en:photos-to-be-validated,en:packaging-photo-to-be-selected,en:nutrition-photo-to-be-selected,en:ingredients-photo-selected,en:front-photo-to-be-selected,en:photos-uploaded\nstates_en: To be completed,Nutrition facts completed,Ingredients completed,Expiration date to be completed,Packaging code to be completed,Characteristics to be completed,Origins to be completed,Categories to be completed,Brands to be completed,Packaging to be completed,Quantity to be completed,Product name completed,Photos to be validated,Packaging photo to be selected,Nutrition photo to be selected,Ingredients photo selected,Front photo to be selected,Photos uploaded\ncompleteness: 0.3625\nlast_image_t: 1672771599.0\nlast_image_datetime: 2023-01-03T18:46:39Z\nimage_ingredients_url: https://images.openfoodfacts.org/images/products/000/001/097/6876/ingredients_en.4.400.jpg\nimage_ingredients_small_url: https://images.openfoodfacts.org/images/products/000/001/097/6876/ingredients_en.4.200.jpg\nfruits-vegetables-nuts-estimate-from-ingredients_100g: 0.0\nbm25_score: 91.76394978788947\ncode: 25075410554\nurl: http://world-en.openfoodfacts.org/product/0025075410554/chocolate-coins\ncreator: foodless\ncreated_t: 1689821462\ncreated_datetime: 2023-07-20T02:51:02Z\nlast_modified_t: 1710012881\nlast_modified_datetime: 2024-03-09T19:34:41Z\nlast_modified_by: annelotte\nlast_updated_t: 1738822379.0\nlast_updated_datetime: 2025-02-06T06:12:59Z\nproduct_name: chocolate coins\nquantity: 250g\ncategories: en:chocolate-candies\ncategories_tags: en:snacks,en:sweet-snacks,en:cocoa-and-its-products,en:confectioneries,en:chocolate-candies\ncategories_en: Snacks,Sweet snacks,Cocoa and its products,Confectioneries,Chocolate candies\ncountries: en:United States\ncountries_tags: en:united-states\ncountries_en: United States\npnns_groups_1: Sugary snacks\npnns_groups_2: Sweets\nfood_groups: en:sweets\nfood_groups_tags: en:sugary-snacks,en:sweets\nfood_groups_en: Sugary snacks,Sweets\nstates: en:to-be-completed, en:nutrition-facts-to-be-completed, en:ingredients-to-be-completed, en:expiration-date-to-be-completed, en:packaging-code-to-be-completed, en:characteristics-to-be-completed, en:origins-to-be-completed, en:categories-completed, en:brands-to-be-completed, en:packaging-to-be-completed, en:quantity-completed, en:product-name-completed, en:photos-to-be-validated, en:packaging-photo-to-be-selected, en:nutrition-photo-to-be-selected, en:ingredients-photo-to-be-selected, en:front-photo-selected, en:photos-uploaded\nstates_tags: en:to-be-completed,en:nutrition-facts-to-be-completed,en:ingredients-to-be-completed,en:expiration-date-to-be-completed,en:packaging-code-to-be-completed,en:characteristics-to-be-completed,en:origins-to-be-completed,en:categories-completed,en:brands-to-be-completed,en:packaging-to-be-completed,en:quantity-completed,en:product-name-completed,en:photos-to-be-validated,en:packaging-photo-to-be-selected,en:nutrition-photo-to-be-selected,en:ingredients-photo-to-be-selected,en:front-photo-selected,en:photos-uploaded\nstates_en: To be completed,Nutrition facts to be completed,Ingredients to be completed,Expiration date to be completed,Packaging code to be completed,Characteristics to be completed,Origins to be completed,Categories completed,Brands to be completed,Packaging to be completed,Quantity completed,Product name completed,Photos to be validated,Packaging photo to be selected,Nutrition photo to be selected,Ingredients photo to be selected,Front photo selected,Photos uploaded\nproduct_quantity: 250.0\ncompleteness: 0.3625\nlast_image_t: 1689821472.0\nlast_image_datetime: 2023-07-20T02:51:12Z\nmain_category: en:chocolate-candies\nmain_category_en: Chocolate candies\nimage_url: https://images.openfoodfacts.org/images/products/002/507/541/0554/front_en.3.400.jpg\nimage_small_url: https://images.openfoodfacts.org/images/products/002/507/541/0554/front_en.3.200.jpg\nbm25_score: 91.76394978788947\ncode: 200030205189\nurl: http://world-en.openfoodfacts.org/product/0200030205189/chocolate-coins-tiger\ncreator: kiliweb\ncreated_t: 1575918098\ncreated_datetime: 2019-12-09T19:01:38Z\nlast_modified_t: 1710013839\nlast_modified_datetime: 2024-03-09T19:50:39Z\nlast_modified_by: annelotte\nlast_updated_t: 1743686710.0\nlast_updated_datetime: 2025-04-03T13:25:10Z\nproduct_name: Chocolate coins\nquantity: 70 g\nbrands: Tiger\nbrands_tags: xx:tiger\nbrands_en: tiger\ncategories: Snacks,Sweet snacks,Cocoa and its products,Confectioneries,Chocolate candies\ncategories_tags: en:snacks,en:sweet-snacks,en:cocoa-and-its-products,en:confectioneries,en:chocolate-candies\ncategories_en: Snacks,Sweet snacks,Cocoa and its products,Confectioneries,Chocolate candies\ncountries: Spain\ncountries_tags: en:spain\ncountries_en: Spain\ningredients_text: Azúcar, leche entera en polvo, pasta de cacao, manteca de cacao, suero lácteo en polvo, emulgente (E322 (soja)), aroma natural de vainilla. Puede con - tener trazas de trigo, cebada, almendras, avellanas, nueces y pistachos.\ningredients_tags: en:sugar,en:added-sugar,en:disaccharide,en:whole-milk-powder,en:dairy,en:milk-powder,en:cocoa-paste,en:plant,en:cocoa,en:cocoa-butter,en:whey-powder,en:whey,en:emulsifier,en:natural-vanilla-flavouring,en:flavouring,en:natural-flavouring,en:vanilla-flavouring,es:puede-con,en:e322\ningredients_analysis_tags: en:palm-oil-free,en:non-vegan,en:vegetarian-status-unknown\nallergens: en:soybeans\ntraces_tags: en:gluten,en:nuts\ntraces_en: Gluten,Nuts\nadditives_n: 1.0\nadditives_tags: en:e322\nadditives_en: E322 - Lecithins\nnutriscore_score: 32.0\nnutriscore_grade: e\nnova_group: 4.0\npnns_groups_1: Sugary snacks\npnns_groups_2: Sweets\nfood_groups: en:sweets\nfood_groups_tags: en:sugary-snacks,en:sweets\nfood_groups_en: Sugary snacks,Sweets\nstates: en:to-be-completed, en:nutrition-facts-completed, en:ingredients-completed, en:expiration-date-to-be-completed, en:packaging-code-to-be-completed, en:characteristics-to-be-completed, en:origins-to-be-completed, en:categories-completed, en:brands-completed, en:packaging-to-be-completed, en:quantity-completed, en:product-name-completed, en:photos-to-be-validated, en:packaging-photo-to-be-selected, en:nutrition-photo-selected, en:ingredients-photo-selected, en:front-photo-selected, en:photos-uploaded\nstates_tags: en:to-be-completed,en:nutrition-facts-completed,en:ingredients-completed,en:expiration-date-to-be-completed,en:packaging-code-to-be-completed,en:characteristics-to-be-completed,en:origins-to-be-completed,en:categories-completed,en:brands-completed,en:packaging-to-be-completed,en:quantity-completed,en:product-name-completed,en:photos-to-be-validated,en:packaging-photo-to-be-selected,en:nutrition-photo-selected,en:ingredients-photo-selected,en:front-photo-selected,en:photos-uploaded\nstates_en: To be completed,Nutrition facts completed,Ingredients completed,Expiration date to be completed,Packaging code to be completed,Characteristics to be completed,Origins to be completed,Categories completed,Brands completed,Packaging to be completed,Quantity completed,Product name completed,Photos to be validated,Packaging photo to be selected,Nutrition photo selected,Ingredients photo selected,Front photo selected,Photos uploaded\nnutrient_levels_tags: en:fat-in-high-quantity,en:saturated-fat-in-high-quantity,en:sugars-in-high-quantity,en:salt-in-low-quantity\nproduct_quantity: 70.0\ncompleteness: 0.6875\nlast_image_t: 1575918099.0\nlast_image_datetime: 2019-12-09T19:01:39Z\nmain_category: en:chocolate-candies\nmain_category_en: Chocolate candies\nimage_url: https://images.openfoodfacts.org/images/products/020/003/020/5189/front_es.3.400.jpg\nimage_small_url: https://images.openfoodfacts.org/images/products/020/003/020/5189/front_es.3.200.jpg\nimage_ingredients_url: https://images.openfoodfacts.org/images/products/020/003/020/5189/ingredients_es.9.400.jpg\nimage_ingredients_small_url: https://images.openfoodfacts.org/images/products/020/003/020/5189/ingredients_es.9.200.jpg\nimage_nutrition_url: https://images.openfoodfacts.org/images/products/020/003/020/5189/nutrition_es.10.400.jpg\nimage_nutrition_small_url: https://images.openfoodfacts.org/images/products/020/003/020/5189/nutrition_es.10.200.jpg\nenergy-kcal_100g: 510.0\nenergy_100g: 2134.0\nfat_100g: 26.0\nsaturated-fat_100g: 17.0\ncarbohydrates_100g: 61.0\nsugars_100g: 59.0\nproteins_100g: 6.5\nsalt_100g: 0.25\nsodium_100g: 0.1\nfruits-vegetables-nuts-estimate-from-ingredients_100g: 0.0\nnutrition-score-fr_100g: 32.0\nbm25_score: 91.76394978788947\n\n\nInstructions:\n1. You are provided with product images and product details fetched from online sources in $PRODUCT_DATA\n2. You are also provided with products data from other sources which might be similar to the original product in $ENRICHED_DATA\n3. Your job is to go through the images of the product, $PRODUCT_DATA and $ENRICHED_DATA and extract information according to schema provided in RESPONSE SCHEMA\n4. You are also provided with the format in which the response must be in RESPONSE SCHEMA\n5. Absolutely avoid ```json or ``` in the final response\n6. If you get multiple images which are unrelated to each other, provide output only for one product \n7. Do not output the schema itself. Make sure your output is JSON formatted string which satisfies the JSON schema\n8. Do not leave the qualified fields empty. Do guess work and provide some value for the qualified fields in the response.",
  "response": "{\n  \"analysed_data\": {\n    \"generalData\": {\n      \"gtinOnPack\": \"876416167084\",\n      \"gtin14\": \"000876416167084\",\n      \"upc12\": \"087641616708\",\n      \"brandOwner\": \"Glicks\",\n      \"brandName\": \"Glicks\",\n      \"department\": \"GROCERY\",\n      \"superCategory\": \"SNACKS & SWEETS\",\n      \"category\": \"CHOCOLATE CANDY\",\n      \"subCategory\": \"ASSORTMENT BOXES\",\n      \"segment\": \"DARK CHOCOLATE\",\n      \"productTitle\": \"Glicks Dark Chocolate Silver Coins\",\n      \"variant\": \"Dark Chocolate\",\n      \"netWeight1Value\": 12.7,\n      \"netWeight1UOM\": \"oz\",\n      \"netWeight2Value\": 360,\n      \"netWeight2UOM\": \"g\",\n      \"unitsPerPack\": 24,\n      \"unitsPerPackDescriptor\": \"BAGS\",\n      \"storage\": \"Room Temperature\",\n      \"numberOfIngredients\": 7\n    },\n    \"servingSize\": {\n      \"servingSize\": 15,\n      \"servingSizeUnit\": \"g\",\n      \"servesPerPack\": 24,\n      \"servingDescription\": \"1 bag (15g)\",\n      \"servingSize2\": null,\n      \"servingSize2Unit\": null\n    },\n    \"nutritionalInformation\": {\n      \"stated\": {\n        \"totalAmount\": 15,\n        \"totalAmountUOM\": \"g\",\n        \"source\": \"Amazon Images\",\n        \"macronutrients\": [\n          {\n            \"name\": \"Total Fat\",\n            \"amount\": 4.5,\n            \"totalAmount\": 15,\n            \"UOM\": \"g\",\n            \"dailyValue\": 6,\n            \"source\": \"Amazon Images\"\n          },\n          {\n            \"name\": \"Saturated Fat\",\n            \"amount\": 2.5,\n            \"totalAmount\": 15,\n            \"UOM\": \"g\",\n            \"dailyValue\": 13,\n            \"source\": \"Amazon Images\"\n          },\n          {\n            \"name\": \"Total Carbohydrate\",\n            \"amount\": 9,\n            \"totalAmount\": 15,\n            \"UOM\": \"g\",\n            \"dailyValue\": 4,\n            \"source\": \"Amazon Images\"\n          },\n          {\n            \"name\": \"Sugars\",\n            \"amount\": 8,\n            \"totalAmount\": 15,\n            \"UOM\": \"g\",\n            \"dailyValue\": 15,\n            \"source\": \"Amazon Images\"\n          },\n          {\n            \"name\": \"Protein\",\n            \"amount\": 1,\n            \"totalAmount\": 15,\n            \"UOM\": \"g\",\n            \"dailyValue\": 0,\n            \"source\": \"Amazon Images\"\n          }\n        ],\n        \"micronutrients\": [\n          {\n            \"name\": \"Calcium\",\n            \"amount\": 5,\n            \"totalAmount\": 15,\n            \"UOM\": \"mg\",\n            \"dailyValue\": 0,\n            \"source\": \"Amazon Images\"\n          },\n          {\n            \"name\": \"Iron\",\n            \"amount\": 1.3,\n            \"totalAmount\": 15,\n            \"UOM\": \"mg\",\n            \"dailyValue\": 0,\n            \"source\": \"Amazon Images\"\n          }\n        ]\n      },\n      \"qualified\": {\n        \"totalAmount\": 100,\n        \"totalAmountUOM\": \"g\",\n        \"source\": \"ScrapingQualificationAI\",\n        \"macronutrients\": [\n          {\n            \"name\": \"Total Fat\",\n            \"amount\": 30,\n            \"totalAmount\": 100,\n            \"UOM\": \"g\",\n            \"dailyValue\": 46,\n            \"source\": \"ScrapingQualificationAI\"\n          },\n          {\n            \"name\": \"Saturated Fat\",\n            \"amount\": 15,\n            \"totalAmount\": 100,\n            \"UOM\": \"g\",\n            \"dailyValue\": 75,\n            \"source\": \"ScrapingQualificationAI\"\n          },\n          {\n            \"name\": \"Total Carbohydrate\",\n            \"amount\": 60,\n            \"totalAmount\": 100,\n            \"UOM\": \"g\",\n            \"dailyValue\": 20,\n            \"source\": \"ScrapingQualificationAI\"\n          },\n          {\n            \"name\": \"Sugars\",\n            \"amount\": 50,\n            \"totalAmount\": 100,\n            \"UOM\": \"g\",\n            \"dailyValue\": 100,\n            \"source\": \"ScrapingQualificationAI\"\n          },\n          {\n            \"name\": \"Protein\",\n            \"amount\": 5,\n            \"totalAmount\": 100,\n            \"UOM\": \"g\",\n            \"dailyValue\": 10,\n            \"source\": \"ScrapingQualificationAI\"\n          }\n        ],\n        \"micronutrients\": [\n          {\n            \"name\": \"Calcium\",\n            \"amount\": 50,\n            \"totalAmount\": 100,\n            \"UOM\": \"mg\",\n            \"dailyValue\": 5,\n            \"source\": \"ScrapingQualificationAI\"\n          },\n          {\n            \"name\": \"Iron\",\n            \"amount\": 10,\n            \"totalAmount\": 100,\n            \"UOM\": \"mg\",\n            \"dailyValue\": 50,\n            \"source\": \"ScrapingQualificationAI\"\n          }\n        ]\n      }\n    },\n    \"ingredients\": {\n      \"stated\": {\n        \"source\": \"Amazon Images\",\n        \"ingredientList\": [\n          {\n            \"text\": \"Sugar\",\n            \"amount\": 30,\n            \"amountUOM\": \"g\",\n            \"dietary_preference\": {\n              \"Vegan\": true,\n              \"Vegetarian\": true,\n              \"Pescatarian\": true,\n              \"WhiteMeatOnly\": true,\n              \"KetoFriendly\": false,\n              \"LowFodmap\": false\n            },\n            \"allergens\": {\n              \"Sugar\": true,\n              \"Celery\": false,\n              \"Gluten\": false,\n              \"Crustaceans\": false,\n              \"Eggs\": false,\n              \"Fish\": false,\n              \"Lupin\": false,\n              \"Milk\": false,\n              \"Peanuts\": false,\n              \"Sesame\": false,\n              \"TreeNuts\": false\n            },\n            \"religious_labels\": {\n              \"Halal\": true,\n              \"Kosher\": true,\n              \"Hindu\": true,\n              \"Jain\": true\n            },\n            \"food_safety_labels\": {\n              \"GMO\": false,\n              \"NoGMO\": true,\n              \"Hormones\": false,\n              \"Carcinogenic\": false,\n              \"Organic\": false,\n              \"ProductRecalls\": false\n            },\n            \"sustainability_labels\": {\n              \"Recycled\": false,\n              \"AnimalWelfare\": false,\n              \"OrganicPositioning\": false,\n              \"PlantBased\": true,\n              \"SocialResponsibility\": false,\n              \"SustainablePackaging\": true\n            },\n            \"source\": \"Amazon Images\"\n          },\n          {\n            \"text\": \"Chocolate Liquor\",\n            \"amount\": 20,\n            \"amountUOM\": \"g\",\n            \"dietary_preference\": {\n              \"Vegan\": true,\n              \"Vegetarian\": true,\n              \"Pescatarian\": true,\n              \"WhiteMeatOnly\": true,\n              \"KetoFriendly\": true,\n              \"LowFodmap\": true\n            },\n            \"allergens\": {\n              \"Sugar\": false,\n              \"Celery\": false,\n              \"Gluten\": false,\n              \"Crustaceans\": false,\n              \"Eggs\": false,\n              \"Fish\": false,\n              \"Lupin\": false,\n              \"Milk\": false,\n              \"Peanuts\": false,\n              \"Sesame\": false,\n              \"TreeNuts\": false\n            },\n            \"religious_labels\": {\n              \"Halal\": true,\n              \"Kosher\": true,\n              \"Hindu\": true,\n              \"Jain\": true\n            },\n            \"food_safety_labels\": {\n              \"GMO\": false,\n              \"NoGMO\": true,\n              \"Hormones\": false,\n              \"Carcinogenic\": false,\n              \"Organic\": true,\n              \"ProductRecalls\": false\n            },\n            \"sustainability_labels\": {\n              \"Recycled\": false,\n              \"AnimalWelfare\": false,\n              \"OrganicPositioning\": true,\n              \"PlantBased\": true,\n              \"SocialResponsibility\": false,\n              \"SustainablePackaging\": true\n            },\n            \"source\": \"Amazon Images\"\n          }\n        ]\n      },\n      \"qualified\": {\n        \"source\": \"ScrapingQualificationAI\",\n        \"ingredientList\": [\n          {\n            \"text\": \"Sugar\",\n            \"amount\": 30,\n            \"amountUOM\": \"g\",\n            \"dietary_preference\": {\n              \"Vegan\": true,\n              \"Vegetarian\": true,\n              \"Pescatarian\": true,\n              \"WhiteMeatOnly\": true,\n              \"KetoFriendly\": false,\n              \"LowFodmap\": false\n            },\n            \"allergens\": {\n              \"Sugar\": true,\n              \"Celery\": false,\n              \"Gluten\": false,\n              \"Crustaceans\": false,\n              \"Eggs\": false,\n              \"Fish\": false,\n              \"Lupin\": false,\n              \"Milk\": false,\n              \"Peanuts\": false,\n              \"Sesame\": false,\n              \"TreeNuts\": false\n            },\n            \"religious_labels\": {\n              \"Halal\": true,\n              \"Kosher\": true,\n              \"Hindu\": true,\n              \"Jain\": true\n            },\n            \"food_safety_labels\": {\n              \"GMO\": false,\n              \"NoGMO\": true,\n              \"Hormones\": false,\n              \"Carcinogenic\": false,\n              \"Organic\": false,\n              \"ProductRecalls\": false\n            },\n            \"sustainability_labels\": {\n              \"Recycled\": false,\n              \"AnimalWelfare\": false,\n              \"OrganicPositioning\": false,\n              \"PlantBased\": true,\n              \"SocialResponsibility\": false,\n              \"SustainablePackaging\": true\n            },\n            \"source\": \"ScrapingQualificationAI\"\n          },\n          {\n            \"text\": \"Chocolate Liquor\",\n            \"amount\": 20,\n            \"amountUOM\": \"g\",\n            \"dietary_preference\": {\n              \"Vegan\": true,\n              \"Vegetarian\": true,\n              \"Pescatarian\": true,\n              \"WhiteMeatOnly\": true,\n              \"KetoFriendly\": true,\n              \"LowFodmap\": true\n            },\n            \"allergens\": {\n              \"Sugar\": false,\n              \"Celery\": false,\n              \"Gluten\": false,\n              \"Crustaceans\": false,\n              \"Eggs\": false,\n              \"Fish\": false,\n              \"Lupin\": false,\n              \"Milk\": false,\n              \"Peanuts\": false,\n              \"Sesame\": false,\n              \"TreeNuts\": false\n            },\n            \"religious_labels\": {\n              \"Halal\": true,\n              \"Kosher\": true,\n              \"Hindu\": true,\n              \"Jain\": true\n            },\n            \"food_safety_labels\": {\n              \"GMO\": false,\n              \"NoGMO\": true,\n              \"Hormones\": false,\n              \"Carcinogenic\": false,\n              \"Organic\": true,\n              \"ProductRecalls\": false\n            },\n            \"sustainability_labels\": {\n              \"Recycled\": false,\n              \"AnimalWelfare\": false,\n              \"OrganicPositioning\": true,\n              \"PlantBased\": true,\n              \"SocialResponsibility\": false,\n              \"SustainablePackaging\": true\n            },\n            \"source\": \"ScrapingQualificationAI\"\n          }\n        ]\n      }\n    },\n    \"claims\": {\n      \"certifications\": [\n        \"Kosher\",\n        \"Gluten-Free Certified\"\n      ],\n      \"nutritionalClaims\": [\n        \"Non-Dairy\",\n        \"Gluten-Free\"\n      ],\n      \"ingredientClaims\": [\n        \"No Artificial Flavors\",\n        \"No Artificial Colors\"\n      ],\n      \"preparation\": {\n        \"storage\": \"Store in a cool, dry place\",\n        \"heatingInstructions\": {\n          \"toaster\": null,\n          \"oven\": null\n        }\n      },\n      \"sustainability\": {\n        \"packaging\": [\n          \"100% Recyclable Packaging\"\n        ],\n        \"environmentalClaims\": [\n          \"Sustainably Sourced\"\n        ]\n      },\n      \"contact\": {\n        \"website\": \"https://www.glicks.com\",\n        \"phone\": \"**************\"\n      }\n    },\n    \"NPI 2.0 Food Packages - Allergens & Intolerances\": {\n      \"fdaRegulatedAllergens\": \"Contains: Soy\",\n      \"caseinQualified\": \"No Casein\",\n      \"caseinStated\": \"No Casein Added\",\n      \"coconutQualified\": \"No Coconut\",\n      \"coconutStated\": \"No Coconut Added\",\n      \"cornQualified\": \"No Corn\",\n      \"cornStated\": \"No Corn Added\",\n      \"dairyLevelStated\": \"Non-Dairy\",\n      \"dairyQualified\": \"No Dairy\",\n      \"dairyStated\": \"No Dairy Added\",\n      \"eggLevelStated\": \"Egg-Free\",\n      \"eggQualified\": \"No Eggs\",\n      \"eggStated\": \"No Eggs Added\",\n      \"falcpaCommonAllergensQualified\": \"Contains: Soy\",\n      \"falcpaCommonAllergensStated\": \"Contains: Soy\",\n      \"fishLevelStated\": \"Fish-Free\",\n      \"fishQualified\": \"No Fish\",\n      \"fishStated\": \"No Fish Added\",\n      \"glutenLevelStated\": \"Gluten-Free\",\n      \"glutenQualified\": \"No Gluten\"\n    },\n    \"cleanLabel\": {\n      \"artisanalStated\": \"Handcrafted\",\n      \"coldPressedIngredientsStated\": null,\n      \"craftStated\": \"Small Batch Crafted\",\n      \"gourmetStated\": \"Premium Gourmet\",\n      \"localStated\": \"Locally Sourced\",\n      \"madeInUsaStated\": \"Made in USA\",\n      \"premiumStated\": \"Premium Quality\",\n      \"smallBatchStated\": \"Small Batch Produced\",\n      \"animalByProductQualified\": \"No Animal By-Products\",\n      \"animalByProductStated\": \"No Animal By-Products Added\",\n      \"antibioticsQualified\": \"No Antibiotics\",\n      \"antibioticsStated\": \"No Antibiotics Ever\",\n      \"artificialColorsQualified\": \"No Artificial Colors\",\n      \"artificialColorsStated\": \"No Artificial Colors Added\",\n      \"artificialFlavorsQualified\": \"No Artificial Flavors\",\n      \"artificialFlavorsStated\": \"No Artificial Flavors Added\",\n      \"artificialIngredientsQualified\": \"No Artificial Ingredients\",\n      \"artificialPreservativesQualified\": \"No Artificial Preservatives\",\n      \"artificialPreservativesStated\": \"No Artificial Preservatives Added\",\n      \"artificialSweetenersQualified\": \"No Artificial Sweeteners\",\n      \"artificialSweetenersStated\": \"No Artificial Sweeteners Added\",\n      \"countOfIngredientsQualified\": \"7 Ingredients\",\n      \"gmoPresenceQualified\": \"No GMO\",\n      \"gmoPresenceStated\": \"Non-GMO Project Verified\",\n      \"highFructoseCornSyrupQualified\": \"No High Fructose Corn Syrup\",\n      \"highFructoseCornSyrupStated\": \"No High Fructose Corn Syrup Added\",\n      \"hormonesQualified\": \"No Hormones\",\n      \"hormonesStated\": \"No Hormones Added\",\n      \"naturalColorsQualified\": \"Natural Colors\",\n      \"naturalColorsStated\": \"Colored with Natural Ingredients\",\n      \"naturalFlavorsQualified\": \"Natural Flavors\",\n      \"naturalFlavorsStated\": \"Flavored with Natural Ingredients\",\n      \"naturalPreservativesQualified\": \"Natural Preservatives\",\n      \"naturalPreservativesStated\": \"Preserved with Natural Ingredients\",\n      \"naturalSweetenersQualified\": \"Natural Sweeteners\",\n      \"naturalSweetenersStated\": \"Sweetened with Natural Ingredients\",\n      \"preservativesQualified\": \"No Preservatives\",\n      \"preservativesStated\": \"No Preservatives Added\",\n      \"rbstQualified\": \"No rBST\",\n      \"rbstStated\": \"From Cows Not Treated with rBST\",\n      \"recognizableIngredientsQualified\": \"Recognizable Ingredients\",\n      \"sugarAlcoholsQualified\": \"No Sugar Alcohols\",\n      \"sugarAlcoholsStated\": \"No Sugar Alcohols Added\"\n    },\n    \"additionalInfo\": {\n      \"energy\": 80,\n      \"weight\": 360,\n      \"categories\": \"Snacks,Sweet snacks,Cocoa and its products,Confectioneries,Chocolate candies\",\n      \"packaging\": \"Mixed plastic film-net\",\n      \"ecoscore\": \"C\",\n      \"nova_group\": \"4\",\n      \"nutriscore_grade\": \"E\",\n      \"data_source\": \"Manufactruer_claims,FoodScanGenius_AI\",\n      \"dietary_preference\": {\n        \"Vegan\": false,\n        \"Vegetarian\": true,\n        \"Pescatarian\": true,\n        \"WhiteMeatOnly\": true,\n        \"KetoFriendly\": false,\n        \"LowFodmap\": false\n      },\n      \"allergens\": {\n        \"Sugar\": true,\n        \"Celery\": false,\n        \"Gluten\": false,\n        \"Crustaceans\": false,\n        \"Eggs\": false,\n        \"Fish\": false,\n        \"Lupin\": false,\n        \"Milk\": false,\n        \"Peanuts\": false,\n        \"Sesame\": false,\n        \"TreeNuts\": false\n      },\n      \"religious_labels\": {\n        \"Halal\": true,\n        \"Kosher\": true,\n        \"Hindu\": true,\n        \"Jain\": true\n      },\n      \"food_safety_labels\": {\n        \"GMO\": false,\n        \"NoGMO\": true,\n        \"Hormones\": false,\n        \"Carcinogenic\": false,\n        \"Organic\": false,\n        \"ProductRecalls\": false\n      },\n      \"sustainability_labels\": {\n        \"Recycled\": true,\n        \"AnimalWelfare\": false,\n        \"OrganicPositioning\": false,\n        \"PlantBased\": true,\n        \"SocialResponsibility\": false,\n        \"SustainablePackaging\": true\n      },\n      \"average_customer_rating\": 3.9,\n      \"ASIN\": \"B0CKY1LY33\",\n      \"traces\": \"May contain traces of gluten and nuts\",\n      \"country_of_origin\": \"United States\",\n      \"customerCareNumber\": \"**************\",\n      \"email\": \"<EMAIL>\",\n      \"websiteLink\": \"https://www.glicks.com\"\n    }\n  },\n  \"scraped_data\": {\n    \"g\": 1354,\n    \"url\": \"https://www.amazon.com/dp/B0CKY1LY33\",\n    \"asin\": \"B0CKY1LY33\",\n    \"size\": null,\n    \"type\": \"CHOCOLATE_CANDY\",\n    \"brand\": \"Glicks\",\n    \"color\": null,\n    \"isB2B\": false,\n    \"isSNS\": false,\n    \"model\": null,\n    \"stats\": null,\n    \"style\": \"Coins\",\n    \"title\": \"Glicks Dark Chocolate Silver Coins (24 Bags = 72 Total Coins) Dairy Free Chocolate Coins | Hanukah Coins | St. Patrick\\u2019s Day Chocolate Coins | Premium Rich Kosher Dark Chocolate Coins | Gluten Free | Product Of Israel\",\n    \"author\": null,\n    \"coupon\": null,\n    \"format\": null,\n    \"images\": [\n      {\n        \"l\": \"91vtctbBHTL.jpg\",\n        \"m\": \"51ygp1NrjBL.jpg\",\n        \"lH\": 2278,\n        \"lW\": 2023,\n        \"mH\": 500,\n        \"mW\": 444\n      },\n      {\n        \"l\": \"81pqcJQc4QL.jpg\",\n        \"m\": \"5102YKwZSYL.jpg\",\n        \"lH\": 1904,\n        \"lW\": 2560,\n        \"mH\": 372,\n        \"mW\": 500\n      },\n      {\n        \"l\": \"81gVD1WCP4L.jpg\",\n        \"m\": \"51-aoPwKH-L.jpg\",\n        \"lH\": 2000,\n        \"lW\": 2000,\n        \"mH\": 500,\n        \"mW\": 500\n      }\n    ],\n    \"offers\": null,\n    \"binding\": null,\n    \"eanList\": null,\n    \"edition\": null,\n    \"fbaFees\": {\n      \"lastUpdate\": 7555352,\n      \"pickAndPackFee\": 455\n    },\n    \"upcList\": null,\n    \"urlSlug\": \"Glicks-Chocolate-Hanukah-Chanukah-Premium\",\n    \"domainId\": 1,\n    \"features\": [\n      \"RICH CHOCOLATE \\u2013 Glicks chocolate coins are made with rich and dark chocolate that is melt in your mouth delicious, if you are looking for gold coin candy that will be a winner with all, you\\u2019ve hit the jackpot.\",\n      \"GREAT FOR GIFTING \\u2013 Treat your loved ones with the great holiday gift of gold coin chocolate, whether they are spending the holiday with you or from afar, they are sure to delight in the gift of chocolate gold coins.\",\n      \"GET CREATIVE \\u2013 Getting our chocolate coins bulk allows for some creative fun! Gift our gold chocolate coins in your goodie bags, use them to decorate your holiday tables and play party games with, or add them to your desserts for an extra special holiday element\",\n      \"WHAT YOU GET \\u2013 Our chocolate gold coins bulk pack comes with 24 individual bags with about 3 coins in each bag. Our bags are great for school events, party favors, prizes and more.\",\n      \"CERTIFIED \\u2013 Glicks Dark Chocolate Coins are certified Kosher non dairy and gluten free.\"\n    ],\n    \"imagesCSV\": \"91vtctbBHTL.jpg,81pqcJQc4QL.jpg,81gVD1WCP4L.jpg\",\n    \"itemWidth\": 0,\n    \"languages\": null,\n    \"launchpad\": false,\n    \"unitCount\": {\n      \"unitType\": \"Ounce\",\n      \"unitValue\": 12.7\n    },\n    \"hasReviews\": true,\n    \"images_url\": [\n      \"https://m.media-amazon.com/images/I/91vtctbBHTL.jpg\",\n      \"https://m.media-amazon.com/images/I/81pqcJQc4QL.jpg\",\n      \"https://m.media-amazon.com/images/I/81gVD1WCP4L.jpg\"\n    ],\n    \"itemHeight\": 0,\n    \"itemLength\": 0,\n    \"itemWeight\": 0,\n    \"lastUpdate\": 7564664,\n    \"parentAsin\": null,\n    \"partNumber\": null,\n    \"promotions\": null,\n    \"description\": \"Glicks chocolate coins offer a delectable experience with their rich, dark chocolate that simply melts in your mouth, making them a crowd-pleaser. Whether you're seeking a delightful holiday gift or looking to get creative with your treats, these gold chocolate coins fit the bill. Our chocolate gold coins are packed in convenient individual bags, each containing approximately 3 coins, making them ideal for various occasions like school events, party favors, and prizes. Moreover, Glicks Dark Chocolate Coins are certified Kosher non dairy and gluten-free, ensuring a treat that suits diverse dietary preferences.\",\n    \"listedSince\": 6721426,\n    \"productType\": 0,\n    \"releaseDate\": -1,\n    \"categoryTree\": [\n      {\n        \"name\": \"Grocery & Gourmet Food\",\n        \"catId\": 16310101\n      },\n      {\n        \"name\": \"Snacks & Sweets\",\n        \"catId\": 23759921011\n      },\n      {\n        \"name\": \"Chocolate Candy\",\n        \"catId\": 23759922011\n      },\n      {\n        \"name\": \"Assortment Boxes\",\n        \"catId\": 115815603011\n      }\n    ],\n    \"manufacturer\": null,\n    \"packageWidth\": 112,\n    \"productGroup\": \"Grocery\",\n    \"rootCategory\": 16310101,\n    \"variationCSV\": null,\n    \"newPriceIsMAP\": false,\n    \"numberOfItems\": 72,\n    \"numberOfPages\": -1,\n    \"packageHeight\": 95,\n    \"packageLength\": 199,\n    \"packageWeight\": 355,\n    \"trackingSince\": 6774788,\n    \"ebayListingIds\": null,\n    \"isAdultProduct\": false,\n    \"isRedirectASIN\": false,\n    \"lastEbayUpdate\": 0,\n    \"lastSoldUpdate\": 7500376,\n    \"isHeatSensitive\": false,\n    \"itemTypeKeyword\": \"chocolate-candy-assortment-boxes\",\n    \"lastPriceChange\": 7324628,\n    \"liveOffersOrder\": null,\n    \"packageQuantity\": -1,\n    \"publicationDate\": -1,\n    \"lastRatingUpdate\": 7562648,\n    \"offersSuccessful\": false,\n    \"availabilityAmazon\": -1,\n    \"referralFeePercent\": 15,\n    \"salesRankReference\": 16310101,\n    \"websiteDisplayGroup\": \"grocery_display_on_website\",\n    \"isEligibleForTradeIn\": false,\n    \"buyBoxSellerIdHistory\": null,\n    \"referralFeePercentage\": 15.01,\n    \"salesRankDisplayGroup\": \"grocery_display_on_website\",\n    \"websiteDisplayGroupName\": \"Grocery\",\n    \"frequentlyBoughtTogether\": [\n      \"B07ZJP5NC2\",\n      \"B0184IFB08\"\n    ],\n    \"buyBoxEligibleOfferCounts\": [\n      1,\n      0,\n      0,\n      0,\n      0,\n      0,\n      0,\n      0\n    ],\n    \"isEligibleForSuperSaverShipping\": true\n  }\n}",
  "images": [
    "https://m.media-amazon.com/images/I/91vtctbBHTL.jpg",
    "https://m.media-amazon.com/images/I/81pqcJQc4QL.jpg",
    "https://m.media-amazon.com/images/I/81gVD1WCP4L.jpg"
  ]
}
