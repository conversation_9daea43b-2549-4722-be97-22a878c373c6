import json

# Step 1: Read the JSON file
with open('response.json', 'r') as file:
    file_content = json.load(file)

# Step 2: Extract the string
string_data = file_content['data']

# Step 3: Parse the string into a JSON object
json_object = json.loads(string_data)

# Step 4: Output the result
print(json_object)  # Output: {'name': '<PERSON>', 'age': 30}

# Optional: Save the JSON object to a new file
with open('output.json', 'w') as file:
    json.dump(json_object, file, indent=4)