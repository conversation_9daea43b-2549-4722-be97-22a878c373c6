["{\n  \"$schema\": \"http://json-schema.org/draft-07/schema#\",\n  \"title\": \"Comprehensive Product Information Schema\",\n  \"description\": \"A comprehensive schema that combines all product information including general data, nutritional information, serving sizes, ingredients, allergens, and product claims.\",\n  \"type\": \"object\",\n  \"properties\": {\n    \"generalData\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"gtinOnPack\": { \"type\": \"string\", \"value\": \"7706512558604\" },\n        \"gtin14\": { \"type\": \"string\", \"value\": \"0007706512558604\" },\n        \"upc12\": { \"type\": \"string\", \"value\": \"7706512558604\" },\n        \"brandOwner\": { \"type\": \"string\", \"value\": \"Matiz\" },\n        \"brandName\": { \"type\": \"string\", \"value\": \"Matiz (RUUFE)\" },\n        \"department\": { \"type\": \"string\", \"value\": \"Grocery & Gourmet Food\" },\n        \"superCategory\": { \"type\": \"string\", \"value\": \"Beverages\" },\n        \"category\": { \"type\": \"string\", \"value\": \"Coffee\" },\n        \"subCategory\": { \"type\": \"string\", \"value\": \"Instant Coffee\" },\n        \"segment\": { \"type\": \"string\", \"value\": \"Ground Coffee\" },\n        \"productTitle\": { \"type\": \"string\", \"value\": \"Matiz Colombian Light Coffee (2 Pack) Matiz Cafe Colombiano Suave roasted and ground coffee Matiz Suave\" },\n        \"variant\": { \"type\": \"string\", \"value\": \"Suave Marfil\" },\n        \"netWeight1Value\": { \"type\": \"number\", \"value\": 340 },\n        \"netWeight1UOM\": { \"type\": \"string\", \"value\": \"g\" },\n        \"netWeight2Value\": { \"type\": \"number\", \"value\": 340 },\n        \"netWeight2UOM\": { \"type\": \"string\", \"value\": \"g\" },\n        \"unitsPerPack\": { \"type\": \"integer\", \"value\": 2 },\n        \"unitsPerPackDescriptor\": { \"type\": \"string\", \"value\": \"Pack of 2\" },\n        \"storage\": { \"type\": \"string\", \"value\": \"Room Temperature\" },\n        \"numberOfIngredients\": { \"type\": \"integer\", \"value\": 1 }\n      },\n      \"required\": [\n        \"gtinOnPack\",\n        \"gtin14\",\n        \"upc12\",\n        \"brandOwner\",\n        \"brandName\",\n        \"department\",\n        \"superCategory\",\n        \"category\",\n        \"subCategory\",\n        \"segment\",\n        \"productTitle\",\n        \"variant\",\n        \"netWeight1Value\",\n        \"netWeight1UOM\",\n        \"netWeight2Value\",\n        \"netWeight2UOM\",\n        \"unitsPerPack\",\n        \"unitsPerPackDescriptor\",\n        \"storage\",\n        \"numberOfIngredients\"\n    },\n    \"servingSize\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"servingSize\": { \"type\": \"number\", \"value\": 11.0 },\n        \"servingSizeUnit\": { \"type\": \"string\", \"value\": \"Ounce\" },\n        \"servesPerPack\": { \"type\": \"integer\", \"value\": 2 },\n        \"servingDescription\": { \"type\": \"string\", \"value\": \"2 Pack\" },\n        \"servingSize2\": { \"type\": \"null\" },\n        \"servingSize2Unit\": { \"type\": \"null\" }\n      },\n      \"required\": [\n        \"servingSize\",\n        \"servingSizeUnit\",\n        \"servesPerPack\",\n        \"servingDescription\"\n    },\n    \"nutritionalInformation\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"stated\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"totalAmount\": { \"type\": \"number\", \"value\": null },\n            \"totalAmountUOM\": { \"type\": \"string\", \"value\": null },\n            \"source\": { \"type\": \"string\", \"value\": \"Amazon Images\" },\n            \"macronutrients\": {\n              \"type\": \"array\",\n              \"items\": [\n                {\n                  \"name\": { \"type\": \"string\", \"value\": \"Protein\" },\n                  \"amount\": { \"type\": \"number\", \"value\": null },\n                  \"totalAmount\": { \"type\": \"number\", \"value\": null },\n                  \"UOM\": { \"type\": \"string\", \"value\": null },\n                  \"dailyValue\": { \"type\": \"number\", \"value\": null },\n                  \"source\": { \"type\": \"string\", \"value\": \"Amazon Images\" }\n                },\n                {\n                  \"name\": { \"type\": \"string\", \"value\": \"Carbohydrates\" },\n                  \"amount\": { \"type\": \"number\", \"value\": null },\n                  \"totalAmount\": { \"type\": \"number\", \"value\": null },\n                  \"UOM\": { \"type\": \"string\", \"value\": null },\n                  \"dailyValue\": { \"type\": \"number\", \"value\": null },\n                  \"source\": { \"type\": \"string\", \"value\": \"Amazon Images\" }\n                },\n                {\n                  \"name\": { \"type\": \"string\", \"value\": \"Fat\" },\n                  \"amount\": { \"type\": \"number\", \"value\": null },\n                  \"totalAmount\": { \"type\": \"number\", \"value\": null },\n                  \"UOM\": { \"type\": \"string\", \"value\": null },\n                  \"dailyValue\": { \"type\": \"number\", \"value\": null },\n                  \"source\": { \"type\": \"string\", \"value\": \"Amazon Images\" }\n                },\n                {\n                  \"name\": { \"type\": \"string\", \"value\": \"Fiber\" },\n                  \"amount\": { \"type\": \"number\", \"value\": null },\n                  \"totalAmount\": { \"type\": \"number\", \"value\": null },\n                  \"UOM\": { \"type\": \"string\", \"value\": null },\n                  \"dailyValue\": { \"type\": \"number\", \"value\": null },\n                  \"source\": { \"type\": \"string\", \"value\": \"Amazon Images\" }\n                },\n                {\n                  \"name\": { \"type\": \"string\", \"value\": \"Sugar\" },\n                  \"amount\": { \"type\": \"number\", \"value\": null },\n                  \"totalAmount\": { \"type\": \"number\", \"value\": null },\n                  \"UOM\": { \"type\": \"string\", \"value\": null },\n                  \"dailyValue\": { \"type\": \"number\", \"value\": null },\n                  \"source\": { \"type\": \"string\", \"value\": \"Amazon Images\" }\n                },\n                {\n                  \"name\": { \"type\": \"string\", \"value\": \"Sodium\" },\n                  \"amount\": { \"type\": \"number\", \"value\": null },\n                  \"totalAmount\": { \"type\": \"number\", \"value\": null },\n                  \"UOM\": { \"type\": \"string\", \"value\": null },\n                  \"dailyValue\": { \"type\": \"number\", \"value\": null },\n                  \"source\": { \"type\": \"string\", \"value\": \"Amazon Images\" }\n                }\n              ]\n            },\n            \"micronutrients\": {\n              \"type\": \"array\",\n              \"items\": []\n            }\n          }\n        },\n        \"qualified\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"totalAmount\": { \"type\": \"number\", \"value\": null },\n            \"totalAmountUOM\": { \"type\": \"string\", \"value\": null },\n            \"source\": { \"type\": \"string\", \"value\": \"ScrapingQualificationAI\" },\n            \"macronutrients\": {\n              \"type\": \"array\",\n              \"items\": []\n            },\n            \"micronutrients\": {\n              \"type\": \"array\",\n              \"items\": []\n            }\n          }\n        }\n      }\n    },\n    \"ingredients\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"stated\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"source\": { \"type\": \"string\", \"value\": \"Amazon Images\" },\n            \"ingredientList\": {\n              \"type\": \"array\",\n              \"items\": [\n                {\n                  \"text\": { \"type\": \"string\", \"value\": \"100% Colombian Arabica Coffee\" },\n                  \"amount\": { \"type\": \"number\", \"value\": 500 },\n                  \"amountUOM\": { \"type\": \"string\", \"value\": \"g\" },\n                  \"dietary_preference\": { \"type\": \"object\", \"value\": {} },\n                  \"allergens\": { \"type\": \"object\", \"value\": {} },\n                  \"religious_labels\": { \"type\": \"object\", \"value\": {} },\n                  \"food_safety_labels\": { \"type\": \"object\", \"value\": {} },\n                  \"sustainability_labels\": { \"type\": \"object\", \"value\": {} },\n                  \"subIngredients\": { \"type\": \"array\", \"value\": [] }\n                }\n              ]\n            }\n          }\n        },\n        \"qualified\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"source\": { \"type\": \"string\", \"value\": \"ScrapingQualificationAI\" },\n            \"ingredientList\": {\n              \"type\": \"array\",\n              \"items\": []\n            }\n          }\n        }\n      }\n    },\n    \"claims\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"certifications\": { \"type\": \"array\", \"value\": [\"USDA Organic\"] },\n        \"nutritionalClaims\": { \"type\": \"array\", \"value\": [\"Light Roast Level\"] },\n        \"ingredientClaims\": { \"type\": \"array\", \"value\": [\"Premium Quality\"] },\n        \"preparation\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"storage\": { \"type\": \"string\", \"value\": \"Room Temperature\" },\n            \"heatingInstructions\": { \"type\": \"object\", \"value\": {} }\n          }\n        },\n        \"sustainability\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"packaging\": { \"type\": \"array\", \"value\": [\"Recycled\"] },\n            \"environmentalClaims\": { \"type\": \"array\", \"value\": [\"Sustainably Sourced\"] \n          }\n        },\n        \"contact\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"website\": { \"type\": \"string\", \"value\": \"https://www.matiz.com\" },\n            \"phone\": { \"type\": \"string\", \"value\": \"******-288-1089\" }\n          }\n        }\n      }\n    },\n    \"NPI 2.0 Food Packages - Allergens & Intolerances\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"fdaRegulatedAllergens\": { \"type\": \"string\", \"value\": \"Contains: Milk, Eggs, Fish, Shellfish, Tree Nuts, Peanuts, Soy\" },\n        \"caseinQualified\": { \"type\": \"string\", \"value\": \"No Casein\" },\n        \"caseinStated\": { \"type\": \"string\", \"value\": \"No Casein Added\" },\n        \"coconutQualified\": { \"type\": \"string\", \"value\": \"No Coconut\" },\n        \"coconutStated\": { \"type\": \"string\", \"value\": \"No Coconut Added\" },\n        \"cornQualified\": { \"type\": \"string\", \"value\": \"No Corn\" },\n        \"cornStated\": { \"type\": \"string\", \"value\": \"No Corn Added\" },\n        \"dairyLevelStated\": { \"type\": \"string\", \"value\": \"Lactose-Free\" },\n        \"dairyQualified\": { \"type\": \"string\", \"value\": \"Dairy Free\" },\n        \"dairyStated\": { \"type\": \"string\", \"value\": \"Dairy Free\" },\n        \"eggLevelStated\": { \"type\": \"string\", \"value\": \"Egg-Free\" },\n        \"eggQualified\": { \"type\": \"string\", \"value\": \"No Eggs\" },\n        \"eggStated\": { \"type\": \"string\", \"value\": \"No Eggs Added\" },\n        \"falcpaCommonAllergensQualified\": { \"type\": \"string\", \"value\": \"Major Allergens\" },\n        \"falcpaCommonAllergensStated\": { \"type\": \"string\", \"value\": \"Contains: Milk, Eggs, Fish, Shellfish, Tree Nuts, Peanuts, Soy\" },\n        \"fishLevelStated\": { \"type\": \"string\", \"value\": \"Fish-Free\" },\n        \"fishQualified\": { \"type\": \"string\", \"value\": \"No Fish\" },\n        \"fishStated\": { \"type\": \"string\", \"value\": \"No Fish Added\" },\n        \"glutenLevelStated\": { \"type\": \"string\", \"value\": \"Gluten-Free\" },\n        \"glutenQualified\": { \"type\": \"string\", \"value\": \"Gluten Free\" },\n        \"glutenStated\": { \"type\": \"string\", \"value\": \"Gluten Free\" },\n        \"dairyLevelStated\": { \"type\": \"string\", \"value\": \"Lactose-Free\" },\n        \"dairyQualified\": { \"type\": \"string\", \"value\": \"Dairy Free\" },\n        \"dairyStated\": { \"type\": \"string\", \"value\": \"Dairy Free\" },\n        \"eggLevelStated\": { \"type\": \"string\", \"value\": \"Egg-Free\" },\n        \"eggQualified\": { \"type\": \"string\", \"value\": \"No Eggs\" },\n        \"eggStated\": { \"type\": \"string\", \"value\": \"No Eggs Added\" },\n        \"falcpaCommonAllergensQualified\": { \"type\": \"string\", \"value\": \"Major Allergens\" },\n        \"falcpaCommonAllergensStated\": { \"type\": \"string\", \"value\": \"Contains: Milk, Eggs, Fish, Shellfish, Tree Nuts, Peanuts, Soy\" },\n        \"fishLevelStated\": { \"type\": \"string\", \"value\": \"Fish-Free\" },\n        \"fishQualified\": { \"type\": \"string\", \"value\": \"No Fish\" },\n        \"fishStated\": { \"type\": \"string\", \"value\": \"No Fish Added\" },\n        \"glutenLevelStated\": { \"type\": \"string\", \"value\": \"Gluten-Free\" },\n        \"glutenQualified\": { \"type\": \"string\", \"value\": \"Gluten Free\" },\n        \"dairyLevelStated\": { \"type\": \"string\", \"value\": \"Lactose-Free\" },\n        \"dairyQualified\": { \"type\": \"string\", \"value\": \"Dairy Free\" },\n        \"dairyStated\": { \"type\": \"string\", \"value\": \"Dairy Free\" },\n        \"eggLevelStated\": { \"type\": \"string\", \"value\": \"Egg-Free\" },\n        \"eggQualified\": { \"type\": \"string\", \"value\": \"No Eggs\" },\n        \"eggStated\": { \"type\": \"string\", \"value\": \"No Eggs Added\" },\n        \"falcpaCommonAllergensQualified\": { \"type\": \"string\", \"value\": \"Major Allergens\" },\n        \"falcpaCommonAllergensStated\": { \"type\": \"string\", \"value\": \"Contains: Milk, Eggs, Fish, Shellfish, Tree Nuts, Peanuts, Soy\" },\n        \"fishLevelStated\": { \"type\": \"string\", \"value\": \"Fish-Free\" },\n        \"fishQualified\": { \"type\": \"string\", \"value\": \"No Fish\" },\n        \"fishStated\": { \"type\": \"string\", \"value\": \"No Fish Added\" },\n        \"glutenLevelStated\": { \"type\": \"string\", \"value\": \"Gluten-Free\" },\n        \"glutenQualified\": { \"type\": \"string\", \"value\": \"Gluten Free\" },\n        \"dairyLevelStated\": { \"type\": \"string\", \"value\": \"Lactose-Free\" },\n        \"dairyQualified\": { \"type\": \"string\", \"value\": \"Dairy Free\" },\n        \"dairyStated\": { \"type\": \"string\", \"value\": \"Dairy Free\" },\n        \"eggLevelStated\": { \"type\": \"string\", \"value\": \"Egg-Free\" },\n        \"eggQualified\": { \"type\": \"string\", \"value\": \"No Eggs\" },\n        \"eggStated\": { \"type\": \"string\", \"value\": \"No Eggs Added\" },\n        \"cleanLabel\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"artisanalStated\": { \"type\": \"string\", \"value\": \"Handcrafted\" },\n            \"coldPressedIngredientsStated\": { \"type\": \"string\", \"value\": \"Cold Pressed\" },\n            \"craftStated\": { \"type\": \"string\", \"value\": \"Craft Brewed\" },\n            \"gourmetStated\": { \"type\": \"string\", \"value\": \"Gourmet Quality\" },\n            \"localStated\": { \"type\": \"string\", \"value\": \"Locally Sourced\" },\n            \"madeInUsaStated\": { \"type\": \"string\", \"value\": \"Made in USA\" },\n            \"animalByProductQualified\": { \"type\": \"string\", \"value\": \"No Animal By-Products\" },\n            \"antibioticsQualified\": { \"type\": \"string\", \"value\": \"No Antibiotics Ever\" },\n            \"artificialColorsQualified\": { \"type\": \"string\", \"value\": \"No Artificial Colors\" },\n            \"artificialFlavorsQualified\": { \"type\": \"string\", \"value\": \"No Artificial Flavors\" },\n            \"artificialIngredientsQualified\": { \"type\": \"string\", \"value\": \"No Artificial Ingredients\" },\n            \"artificialPreservativesQualified\": { \"type\": \"string\", \"value\": \"No Artificial Preservatives\" },\n            \"artificialSweetenersQualified\": { \"type\": \"string\", \"value\": \"No Artificial Sweeteners\" },\n            \"countOfIngredientsQualified\": { \"type\": \"string\", \"value\": \"10 Ingredients\" },\n            \"gmoPresenceQualified\": { \"type\": \"string\", \"value\": \"Non-GMO\" },\n            \"hormonesQualified\": { \"type\": \"string\", \"value\": \"No Hormones\" },\n            \"naturalColorsQualified\": { \"type\": \"string\", \"value\": \"Natural Colors\" },\n            \"naturalFlavorsQualified\": { \"type\": \"string\", \"value\": \"Natural Flavors\" },\n            \"naturalPreservativesQualified\": { \"type\": \"string\", \"value\": \"Natural Preservatives\" },\n            \"naturalSweetenersQualified\": { \"type\": \"string\", \"value\": \"Natural Sweeteners\" }\n          }\n        },\n        \"additionalInfo\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"energy\": { \"type\": \"number\", \"value\": 200 },\n            \"weight\": { \"type\": \"number\", \"value\": 340 },\n            \"categories\": { \"type\": \"string\", \"value\": \"Coffee\" },\n            \"packaging\": { \"type\": \"string\", \"value\": \"Plastic\" },\n            \"ecoscore\": { \"type\": \"string\", \"value\": \"B\" },\n            \"nova_group\": { \"type\": \"string\", \"value\": \"3\" },\n            \"nutriscore_grade\": { \"type\": \"string\", \"value\": \"C\" },\n            \"data_source\": { \"type\": \"string\", \"value\": \"Manufacturer_claims,FoodScanGenius_AI\" },\n            \"dietary_preference\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"Vegan\": { \"type\": \"boolean\", \"value\": false },\n                \"Vegetarian\": { \"type\": \"boolean\", \"value\": true },\n                \"Pescatarian\": { \"type\": \"boolean\", \"value\": false },\n                \"WhiteMeatOnly\": { \"type\": \"boolean\", \"value\": false },\n                \"KetoFriendly\": { \"type\": \"boolean\", \"value\": false },\n                \"LowFodmap\": { \"type\": \"boolean\", \"value\": false }\n              }\n            },\n            \"allergens\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"Sugar\": { \"type\": \"boolean\", \"value\": false },\n                \"Celery\": { \"type\": \"boolean\", \"value\": false },\n                \"Gluten\": { \"type\": \"boolean\", \"value\": false },\n                \"Crustaceans\": { \"type\": \"boolean\", \"value\": false },\n                \"Eggs\": { \"type\": \"boolean\", \"value\": false },\n                \"Fish\": { \"type\": \"boolean\", \"value\": false },\n                \"Lupin\": { \"type\": \"boolean\", \"value\": false },\n                \"Milk\": { \"type\": \"boolean\", \"value\": false },\n                \"Peanuts\": { \"type\": \"boolean\", \"value\": false },\n                \"Sesame\": { \"type\": \"boolean\", \"value\": false },\n                \"TreeNuts\": { \"type\": \"boolean\", \"value\": false }\n              }\n            },\n            \"religious_labels\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"Halal\": { \"type\": \"boolean\", \"value\": false },\n                \"Kosher\": { \"type\": \"boolean\", \"value\": false },\n                \"Hindu\": { \"type\": \"boolean\", \"value\": false },\n                \"Jain\": { \"type\": \"boolean\", \"value\": false }\n              }\n            },\n            \"food_safety_labels\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"GMO\": { \"type\": \"boolean\", \"value\": false },\n                \"NoGMO\": { \"type\": \"boolean\", \"value\": true },\n                \"Hormones\": { \"type\": \"boolean\", \"value\": false },\n                \"Carcinogenic\": { \"type\": \"boolean\", \"value\": false },\n                \"Organic\": { \"type\": \"boolean\", \"value\": true },\n                \"ProductRecalls\": { \"type\": \"boolean\", \"value\": false }\n              }\n            },\n            \"sustainability_labels\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"Recycled\": { \"type\": \"boolean\", \"value\": true },\n                \"AnimalWelfare\": { \"type\": \"boolean\", \"value\": true },\n                \"OrganicPositioning\": { \"type\": \"boolean\", \"value\": true },\n                \"PlantBased\": { \"type\": \"boolean\", \"value\": true },\n                \"SocialResponsibility\": { \"type\": \"boolean\", \"value\": true },\n                \"SustainablePackaging\": { \"type\": \"boolean\", \"value\": true }\n              }\n            },\n            \"average_customer_rating\": { \"type\": \"number\", \"value\": 4.5 },\n            \"ASIN\": { \"type\": \"string\", \"value\": \"B0BRGHLXHD\" },\n            \"traces\": { \"type\": \"string\", \"value\": \"None\" },\n            \"country_of_origin\": { \"type\": \"string\", \"value\": \"Colombia\" },\n            \"customerCareNumber\": { \"type\": \"string\", \"value\": \"******-288-1089\" },\n            \"email\": { \"type\": \"string\", \"value\": \"<EMAIL>\" },\n            \"websiteLink\": { \"type\": \"string\", \"value\": \"https://www.matiz.com\" }\n          }\n        }\n      }\n    }\n  }\n}\n}", {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Comprehensive Product Information Schema", "description": "A comprehensive schema that combines all product information including general data, nutritional information, serving sizes, ingredients, allergens, and product claims.", "type": "object", "properties": {"generalData": {"type": "object", "properties": {"gtinOnPack": "013562130627", "gtin14": "00013562130627", "upc12": "001356213062", "brandOwner": "<PERSON><PERSON><PERSON>", "brandName": "Glicks (Glicks)", "department": "Grocery & Gourmet Food", "superCategory": "Snacks & Sweets", "category": "Chocolate Candy", "subCategory": "Assortment Boxes", "segment": "Premium Rich Kosher Dark Chocolate Coins", "productTitle": "Glicks Dark Chocolate Silver Coins", "variant": "", "netWeight1Value": 12.7, "netWeight1UOM": "oz", "netWeight2Value": 355, "netWeight2UOM": "g", "unitsPerPack": 24, "unitsPerPackDescriptor": "Bags", "storage": "Room Temperature", "numberOfIngredients": 8}, "required": ["gtinOnPack", "brandOwner", "brandName", "department", "superCategory", "category", "segment", "productTitle", "netWeight1Value", "netWeight1UOM", "unitsPerPack", "unitsPerPackDescriptor"]}, "servingSize": {"type": "object", "properties": {"servingSize": 1, "servingSizeUnit": "bag", "servesPerPack": 24, "servingDescription": "1 bag = 15g"}, "required": ["servingSize", "servingSizeUnit", "servesPerPack", "servingDescription"]}, "nutritionalInformation": {"stated": {"totalAmount": 100, "totalAmountUOM": "g", "macronutrients": [{"name": "Total Fat", "amount": 4.5, "totalAmount": 100, "UOM": "g", "dailyValue": 6}, {"name": "Saturated Fat", "amount": 2.5, "totalAmount": 100, "UOM": "g", "dailyValue": 13}, {"name": "Trans Fat", "amount": 0, "totalAmount": 100, "UOM": "g"}, {"name": "Cholesterol", "amount": 0, "totalAmount": 100, "UOM": "mg"}, {"name": "Sodium", "amount": 0, "totalAmount": 100, "UOM": "mg"}, {"name": "<PERSON><PERSON>", "amount": "<1", "totalAmount": 100, "UOM": "g"}, {"name": "Calcium", "amount": 5, "totalAmount": 100, "UOM": "mg"}, {"name": "Potassium", "amount": 0, "totalAmount": 100, "UOM": "mg"}], "micronutrients": [{"name": "<PERSON><PERSON>", "amount": 0, "totalAmount": 100, "UOM": "mcg"}, {"name": "Iron", "amount": 1.3, "totalAmount": 100, "UOM": "mg"}]}, "qualified": {"totalAmount": 100, "totalAmountUOM": "g", "macronutrients": [], "micronutrients": []}}, "ingredients": {"stated": {"source": "Amazon Images", "ingredientList": [{"text": "Sugar", "amount": 80, "amountUOM": "g", "dietary_preference": {}, "allergens": {}, "religious_labels": {}, "food_safety_labels": {}, "sustainability_labels": {}}, {"text": "Cocoa Butter", "amount": 10, "amountUOM": "g", "dietary_preference": {}, "allergens": {}, "religious_labels": {}, "food_safety_labels": {}, "sustainability_labels": {}}, {"text": "<PERSON><PERSON><PERSON>", "amount": 1, "amountUOM": "g", "dietary_preference": {}, "allergens": {}, "religious_labels": {}, "food_safety_labels": {}, "sustainability_labels": {}}, {"text": "Emulsifier (PGPR)", "amount": 0.1, "amountUOM": "g", "dietary_preference": {}, "allergens": {}, "religious_labels": {}, "food_safety_labels": {}, "sustainability_labels": {}}, {"text": "Artificial Flavor (Vanillin)", "amount": 0.1, "amountUOM": "g", "dietary_preference": {}, "allergens": {}, "religious_labels": {}, "food_safety_labels": {}, "sustainability_labels": {}}]}, "qualified": {"source": "ScrapingQualificationAI", "ingredientList": []}}, "claims": {"certifications": ["Certified Gluten Free", "<PERSON><PERSON>"], "nutritionalClaims": [], "ingredientClaims": [], "preparation": {"storage": "Room Temperature", "heatingInstructions": {}}, "sustainability": {"packaging": [], "environmentalClaims": []}, "contact": {"website": "https://www.glicks.com/", "phone": "******-288-1089"}}, "NPI 2.0 Food Packages - Allergens & Intolerances": {"dairyLevelStated": "Contains Milk", "glutenLevelStated": "Gluten-Free", "caseinQualified": "No Casein", "coconutQualified": "No Coconut", "cornQualified": "No Corn", "fishLevelStated": "", "glutenQualified": "Gluten-Free", "eggLevelStated": "", "falcpaCommonAllergensQualified": "Contains Milk", "animalByProductQualified": "", "antibioticsQualified": "", "artificialColorsQualified": "", "artificialFlavorsQualified": "", "artificialIngredientsQualified": "", "gmoPresenceQualified": ""}, "cleanLabel": {"artisanalStated": "", "coldPressedIngredientsStated": "", "craftStated": "", "premiumStated": "", "localStated": "", "madeInUsaStated": "Product of Israel", "animalByProductQualified": "", "antibioticsQualified": "", "artificialColorsQualified": "", "artificialFlavorsQualified": "", "artificialIngredientsQualified": "", "gmoPresenceQualified": ""}, "additionalInfo": {"energy": 80, "weight": 12.7, "categories": "Chocolate Candy", "packaging": "Mixed Plastic Film-Net", "ecoscore": "Not Available", "nova_group": 4, "nutriscore_grade": "e", "data_source": "Manufacturer_claims,FoodScanGenius_AI", "dietary_preference": {"Vegan": false, "Vegetarian": false, "Pescatarian": false, "WhiteMeatOnly": false, "KetoFriendly": false, "LowFodmap": false}, "allergens": {"Sugar": true, "Celery": false, "Gluten": false, "Crustaceans": false, "Eggs": false, "Fish": false, "Lupin": false, "Milk": true, "Peanuts": false, "Sesame": false, "TreeNuts": false}, "religious_labels": {"Halal": false, "Kosher": true, "Hindu": false, "Jain": false}, "food_safety_labels": {"GMO": false, "NoGMO": true, "Hormones": false, "Carcinogenic": false, "Organic": false, "ProductRecalls": false}, "sustainability_labels": {"Recycled": false, "AnimalWelfare": false, "OrganicPositioning": false, "PlantBased": false, "SocialResponsibility": false, "SustainablePackaging": false}, "average_customer_rating": 3.9, "ASIN": "B0CKY1LY33", "traces": "", "country_of_origin": "Israel", "customerCareNumber": "******-288-1089", "email": "<EMAIL>", "websiteLink": "https://www.glicks.com/"}}}, "The image shows a product display featuring a bottle of Ritual Zero Proof Rum Alternative and five smaller bottles of Fever Tree Distillers Cola. The Ritual Zero Proof Rum Alternative is a non-alcoholic rum alternative, which is described as having a rich profile with toasted spices and flavors of ripe banana and burnt orange. It's designed for crafting cocktails and comes in a 750ml bottle. Accompanying it are five 200ml bottles of Fever Tree Distillers Cola, which is a non-alcoholic cola made with natural flavors including Caribbean kola nut. The overall setting suggests a focus on alcohol-free beverages, possibly aimed at consumers looking for alternatives to traditional alcoholic drinks.", {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Comprehensive Product Information Schema", "description": "A comprehensive schema that combines all product information including general data, nutritional information, serving sizes, ingredients, allergens, and product claims.", "type": "object", "properties": {"generalData": {"type": "object", "properties": {"gtinOnPack": "850048408043", "gtin14": "850048408043", "upc12": "850048408043", "brandOwner": "FOREVER FOODS", "brandName": "FOREVER FOODS", "department": "Grocery & Gourmet Food", "superCategory": "Meat & Seafood", "category": "<PERSON><PERSON>", "subCategory": "Stew Meat", "segment": "Stew Meat", "productTitle": "Uncooked Freeze-Dr<PERSON>f <PERSON>ubes", "variant": "", "netWeight1Value": 8.5, "netWeight1UOM": "oz", "netWeight2Value": 241, "netWeight2UOM": "g", "unitsPerPack": 6, "unitsPerPackDescriptor": "CUPS", "storage": "Refrigerated", "numberOfIngredients": 1}, "required": ["gtinOnPack", "gtin14", "upc12", "brandOwner", "brandName", "department", "superCategory", "category", "subCategory", "segment", "productTitle", "variant", "netWeight1Value", "netWeight1UOM", "netWeight2Value", "netWeight2UOM", "unitsPerPack", "unitsPerPackDescriptor", "storage", "numberOfIngredients"]}, "servingSize": {"type": "object", "properties": {"servingSize": 49, "servingSizeUnit": "g", "servesPerPack": 6, "servingDescription": "1 cup uncooked cubes (49g)", "servingSize2": null, "servingSize2Unit": null}, "required": ["servingSize", "servingSizeUnit", "servesPerPack", "servingDescription"]}, "nutritionalInformation": {"stated": {"totalAmount": 100, "totalAmountUOM": "g", "source": "Amazon Images", "macronutrients": [{"name": "<PERSON><PERSON>", "amount": 35, "totalAmount": 100, "UOM": "g", "dailyValue": 68}, {"name": "Total Fat", "amount": 5, "totalAmount": 100, "UOM": "g", "dailyValue": 6}, {"name": "Saturated Fat", "amount": 1.5, "totalAmount": 100, "UOM": "g", "dailyValue": 8}, {"name": "Cholesterol", "amount": 90, "totalAmount": 100, "UOM": "mg", "dailyValue": 30}, {"name": "Sodium", "amount": 80, "totalAmount": 100, "UOM": "mg", "dailyValue": 3}, {"name": "Total Carbohydrate", "amount": 0, "totalAmount": 100, "UOM": "g", "dailyValue": 0}, {"name": "Dietary Fiber", "amount": 0, "totalAmount": 100, "UOM": "g", "dailyValue": 0}, {"name": "Total Sugars", "amount": 0, "totalAmount": 100, "UOM": "g", "dailyValue": 0}], "micronutrients": [{"name": "<PERSON><PERSON>", "amount": 0, "totalAmount": 100, "UOM": "mcg", "dailyValue": 0}, {"name": "Calcium", "amount": 20, "totalAmount": 100, "UOM": "mg", "dailyValue": 2}, {"name": "Iron", "amount": 2.2, "totalAmount": 100, "UOM": "mg", "dailyValue": 10}, {"name": "Potassium", "amount": 490, "totalAmount": 100, "UOM": "mg", "dailyValue": 10}]}, "qualified": {"totalAmount": 100, "totalAmountUOM": "g", "source": "ScrapingQualificationAI", "macronutrients": [], "micronutrients": []}}, "ingredients": {"stated": {"source": "Product Scrape", "ingredientList": [{"text": "Potato", "amount": 100, "amountUOM": "g", "dietary_preference": {}, "allergens": {}, "religious_labels": {}, "food_safety_labels": {}, "sustainability_labels": {}, "subIngredients": []}, {"text": "Cooked Beef", "amount": 100, "amountUOM": "g", "dietary_preference": {}, "allergens": {}, "religious_labels": {}, "food_safety_labels": {}, "sustainability_labels": {}}, {"text": "Carrot", "amount": 100, "amountUOM": "g", "dietary_preference": {}, "allergens": {}, "religious_labels": {}, "food_safety_labels": {}, "sustainability_labels": {}}, {"text": "Green Peas", "amount": 100, "amountUOM": "g", "dietary_preference": {}, "allergens": {}, "religious_labels": {}, "food_safety_labels": {}, "sustainability_labels": {}}, {"text": "Corn Oil with Spice Extract", "amount": 100, "amountUOM": "g", "dietary_preference": {}, "allergens": {}, "religious_labels": {}, "food_safety_labels": {}, "sustainability_labels": {}}, {"text": "Modified Corn Starch", "amount": 100, "amountUOM": "g", "dietary_preference": {}, "allergens": {}, "religious_labels": {}, "food_safety_labels": {}, "sustainability_labels": {}}, {"text": "Hydrolyzed Vegetable Protein", "amount": 100, "amountUOM": "g", "dietary_preference": {}, "allergens": {}, "religious_labels": {}, "food_safety_labels": {}, "sustainability_labels": {}}, {"text": "Dehydrated Onion", "amount": 100, "amountUOM": "g", "dietary_preference": {}, "allergens": {}, "religious_labels": {}, "food_safety_labels": {}, "sustainability_labels": {}}, {"text": "Sugar", "amount": 100, "amountUOM": "g", "dietary_preference": {}, "allergens": {}, "religious_labels": {}, "food_safety_labels": {}, "sustainability_labels": {}}, {"text": "Spice", "amount": 100, "amountUOM": "g", "dietary_preference": {}, "allergens": {}, "religious_labels": {}, "food_safety_labels": {}, "sustainability_labels": {}}, {"text": "<PERSON><PERSON><PERSON>", "amount": 100, "amountUOM": "g", "dietary_preference": {}, "allergens": {}, "religious_labels": {}, "food_safety_labels": {}, "sustainability_labels": {}}]}, "qualified": {"source": "ScrapingQualificationAI", "ingredientList": []}}, "claims": {"certifications": [], "nutritionalClaims": [], "ingredientClaims": [], "preparation": {"storage": "Refrigerated", "heatingInstructions": {"toaster": "", "oven": ""}}, "sustainability": {"packaging": "", "environmentalClaims": []}, "contact": {"website": "https://www.foreverfoods.com/", "phone": "******-288-1089"}}, "NPI 2.0 Food Packages - Allergens & Intolerances": {"fdaRegulatedAllergens": "Milk, Eggs, Wheat, Soy, Sesame, Almond, Cashew, Walnut, Pecan, Coconut", "caseinQualified": "No", "caseinStated": "No Casein Added", "coconutQualified": "No", "coconutStated": "No Coconut Added", "cornQualified": "No", "cornStated": "No Corn Added", "dairyLevelStated": "Contains Milk", "dairyQualified": "Yes", "dairyStated": "Contains Milk", "eggLevelStated": "Contains Eggs", "eggQualified": "Yes", "eggStated": "Contains Eggs", "falcpaCommonAllergensQualified": "Yes", "falcpaCommonAllergensStated": "Contains Milk, Eggs, Fish, Shellfish, Tree Nuts, Peanuts, Wheat, Soybeans", "fishLevelStated": "Contains Fish", "fishQualified": "Yes", "fishStated": "Contains Fish", "glutenLevelStated": "Gluten-Free", "glutenQualified": "Yes", "glutenStated": "Gluten-Free", "animalByProductQualified": "No", "animalByProductStated": "No Animal By-Products Added", "antibioticsQualified": "No", "antibioticsStated": "No Antibiotics Ever", "artificialColorsQualified": "No", "artificialColorsStated": "No Artificial Colors Added", "artificialFlavorsQualified": "No", "artificialFlavorsStated": "No Artificial Flavors Added", "artificialIngredientsQualified": "No", "artificialPreservativesQualified": "No", "artificialPreservativesStated": "No Artificial Preservatives Added", "artificialSweetenersQualified": "No", "artificialSweetenersStated": "No Artificial Sweeteners Added", "countOfIngredientsQualified": "Multiple", "gmoPresenceQualified": "Non-GMO", "gmoPresenceStated": "Non-GMO", "highFructoseCornSyrupQualified": "No", "highFructoseCornSyrupStated": "No High Fructose Corn Syrup Added", "hormonesQualified": "No", "hormonesStated": "No Hormones Added", "naturalColorsQualified": "Yes", "naturalColorsStated": "Natural Colors", "naturalFlavorsQualified": "Yes", "naturalFlavorsStated": "Natural Flavors", "naturalPreservativesQualified": "No", "naturalPreservativesStated": "No Natural Preservatives Added", "naturalSweetenersQualified": "No", "naturalSweetenersStated": "No Natural Sweeteners Added", "preservativesQualified": "No", "preservativesStated": "No Preservatives Added", "rbstQualified": "No", "rbstStated": "No rBST Added", "recognizableIngredientsQualified": "Yes", "sugarAlcoholsQualified": "No", "sugarAlcoholsStated": "No Sugar Alcohols Added", "additionalInfo": {"energy": 190, "weight": 8.5, "categories": "Meals", "packaging": "Plastic", "ecoscore": "B", "nova_group": "4", "nutriscore_grade": "e", "data_source": "Manufacturer_claims,FoodScanGenius_AI", "dietary_preference": {"Vegan": false, "Vegetarian": false, "Pescatarian": false, "WhiteMeatOnly": false, "KetoFriendly": false, "LowFodmap": false}, "allergens": {"Sugar": false, "Celery": false, "Gluten": false, "Crustaceans": false, "Eggs": true, "Fish": false, "Lupin": false, "Milk": true, "Peanuts": false, "Sesame": false, "TreeNuts": false}, "religious_labels": {"Halal": false, "Kosher": false, "Hindu": false, "Jain": false}, "food_safety_labels": {"GMO": false, "NoGMO": true, "Hormones": false, "Carcinogenic": false, "Organic": false, "ProductRecalls": false}, "sustainability_labels": {"Recycled": false, "AnimalWelfare": false, "OrganicPositioning": false, "PlantBased": false, "SocialResponsibility": false, "SustainablePackaging": false}, "average_customer_rating": 4.4, "ASIN": "B0C6NXHWBJ", "traces": "", "country_of_origin": "United States", "customerCareNumber": "******-288-1089", "email": "", "websiteLink": "https://www.foreverfoods.com/"}}}}]