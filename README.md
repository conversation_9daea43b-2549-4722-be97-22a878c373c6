# Data to Prompt-Response Pairs Generator

This project generates JSON Lines (JSONL) files containing prompt-response pairs for training AI models on product data extraction tasks. The script processes scraped product data and extraction results to create structured training data.

## Overview

The solution takes two main input files:
1. **Scrape Content Data** - Contains raw product data scraped from Amazon, including product details, markdown content, and enriched data
2. **Extraction Results Data** - Contains processed/extracted content that serves as the ground truth responses

The output is a JSONL file where each line contains:
- **prompt**: A populated template with product data, scraped content, and enriched data
- **response**: The extracted/processed content from the extraction results
- **images**: URLs of product images extracted from the product data

## Files

### Core Scripts
- `generate_prompt_response_pairs.py` - Main script that processes the data and generates JSONL output
- `verify_output.py` - Verification script to check the output format and content

### Input Files
- `scrape_content_202507021012.json` - Scraped product data from Amazon
- `extraction_results_202507021013.json` - Processed extraction results
- `prompt.txt` - Template for generating prompts
- `response_schema.json` - JSON schema for the expected response format

### Output Files
- `final_output.jsonl` - Complete JSONL file with 207 prompt-response pairs
- `output_updated.jsonl` - Test output with 5 entries (for testing)

## Usage

### Basic Usage
```bash
python generate_prompt_response_pairs.py scrape_content.json extraction_results.json output.jsonl
```

### Advanced Options
```bash
# Limit number of entries for testing
python generate_prompt_response_pairs.py scrape_content.json extraction_results.json output.jsonl --max-entries 10

# Download images as base64 (warning: slow for large datasets)
python generate_prompt_response_pairs.py scrape_content.json extraction_results.json output.jsonl --download-images

# Use custom template and schema files
python generate_prompt_response_pairs.py scrape_content.json extraction_results.json output.jsonl \
    --prompt-template custom_prompt.txt \
    --response-schema custom_schema.json
```

### Verification
```bash
python verify_output.py final_output.jsonl
```

## Data Flow

1. **Input Processing**:
   - Loads scrape content data containing product information, markdown content, and enriched data
   - Loads extraction results containing processed responses
   - Loads prompt template and response schema

2. **Data Matching**:
   - Matches scrape content entries with extraction results by `task_id`
   - Ensures each prompt has a corresponding response

3. **Prompt Generation**:
   - Populates the prompt template with:
     - Response schema (JSON schema for expected output)
     - Product data (parsed from JSON string in scrape content)
     - Markdown content (scraped webpage content)
     - Enriched data (OpenFoodFacts results and additional context)

4. **Image Extraction**:
   - Extracts image URLs from the `images_url` field in product data
   - Falls back to constructing URLs from the `images` field if needed
   - Optionally downloads and converts images to base64

5. **Response Processing**:
   - Uses the `extracted_json` field from extraction results as the response
   - Formats the response as properly structured JSON

6. **Output Generation**:
   - Creates JSONL file with one entry per line
   - Each entry contains prompt, response, and images

## Data Structure

### Input Data Structure

**Scrape Content Entry**:
```json
{
  "id": 16753,
  "task_id": 22677,
  "asin": "B0BRGHLXHD",
  "product_data": "{...}",  // JSON string containing product details
  "markdown_content": "...",  // Scraped webpage content
  "enriched_data": "...",  // OpenFoodFacts results and additional data
  "extracted_data": "...",  // Ignored (use extraction results instead)
  "created_date": "...",
  "updated_date": "..."
}
```

**Extraction Results Entry**:
```json
{
  "task_id": 22677,
  "extracted_json": "{...}",  // Processed extraction result as JSON string
  "created_date": "...",
  "updated_date": "..."
}
```

### Output Data Structure

**JSONL Entry**:
```json
{
  "prompt": "You are a helpful assistant...[full populated template]",
  "response": "{\"generalData\": {...}, \"nutritionalInformation\": {...}}",
  "images": ["https://m.media-amazon.com/images/I/51h7aTHB51L.jpg", "..."]
}
```

## Key Features

1. **Template Population**: Automatically populates prompt templates with actual data
2. **Image Extraction**: Extracts and processes product images from various data formats
3. **Data Validation**: Matches scrape content with extraction results by task_id
4. **Flexible Output**: Supports both URL-based and base64-encoded images
5. **Error Handling**: Gracefully handles missing data and parsing errors
6. **Verification Tools**: Includes scripts to verify output format and content

## Requirements

- Python 3.7+
- `requests` library (for image downloading, if using --download-images option)
- Standard library modules: `json`, `sys`, `argparse`, `pathlib`

## Output Statistics

The final output (`final_output.jsonl`) contains:
- **207 prompt-response pairs**
- **Average prompt length**: ~143,000 characters
- **Average response length**: ~15,000 characters
- **Images per entry**: 1-10 images (average ~5 images)
- **Total file size**: ~30MB

Each prompt includes:
- Complete response schema (JSON schema)
- Product details (brand, title, features, etc.)
- Scraped webpage content (full Amazon product page)
- Enriched data (OpenFoodFacts results and additional context)
- Detailed instructions for extraction

## Notes

- The script ignores the `extracted_data` field from scrape content and uses extraction results instead
- Image URLs are extracted from the `images_url` field in product data
- The enriched data includes OpenFoodFacts results and additional product context
- All prompts follow the same template structure for consistency
- Responses are structured JSON following the provided schema
