#!/usr/bin/env python3
"""
Simple script to convert generate.json from string format to dictionary format.
This version directly modifies the original file.
"""

import json

def convert_generate_json():
    """Convert generate.json from string entries to dictionary entries."""
    
    # Read the original file
    with open('generate.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"Loaded {len(data)} entries from generate.json")
    
    # Convert each string entry to dictionary
    converted_data = []
    for i, entry in enumerate(data):
        if isinstance(entry, str):
            # Skip empty strings
            if entry.strip() == "":
                print(f"Skipping empty entry {i+1}")
                continue
                
            try:
                # Parse the JSON string to dictionary
                parsed_entry = json.loads(entry)
                converted_data.append(parsed_entry)
                print(f"Converted entry {i+1} from string to dictionary")
            except json.JSONDecodeError as e:
                print(f"Error parsing entry {i+1}: {e}")
                # Keep the original string if parsing fails
                converted_data.append(entry)
        else:
            # Keep as is if already a dictionary
            converted_data.append(entry)
    
    # Write back to the same file
    with open('generate.json', 'w', encoding='utf-8') as f:
        json.dump(converted_data, f, indent=2, ensure_ascii=False)
    
    print(f"Successfully converted generate.json!")
    print(f"All {len(converted_data)} entries are now in dictionary format.")

if __name__ == "__main__":
    convert_generate_json()
